<?php
/**
 * Plugin Name:       SimpleWishlist New
 * Plugin URI:        https://simple-wishlist.com
 * Description:       Social Proof & Recent Sales Popup, Comment Notification, Subscription Notification, Notification Bar and many more.
 * Version:           1.0.0
 * Author:            MD Shakibul Islam 
 * Author URI:        https://github.com/shuvo7670
 * License:           GPL-3.0+
 * License URI:       https://www.gnu.org/licenses/gpl-3.0.html
 * Text Domain:       simple-wishlist
 * Domain Path:       /languages
 *
 * @package           SimpleWishlist
 * @link              https://github.com/shuvo7670
 * @since             1.0.0
 */

/**
 * If this file is called directly, abort.
 */
if ( ! defined( 'WPINC' ) ) {
	die;
}
/**
 * Defines CONSTANTS for Whole plugins.
 */
define( 'SW_FILE', __FILE__ );
define( 'SW_VERSION', '1.0.0' );
define( 'SW_URL', plugins_url( '/', __FILE__ ) );
define( 'SW_PATH', plugin_dir_path( __FILE__ ) );
define( 'SW_BASENAME', plugin_basename( __FILE__ ) );

define( 'SW_ASSETS', SW_URL . 'assets/' );
define( 'SW_ASSETS_PATH', SW_PATH . 'assets/' );
define( 'SW_DEV_ASSETS', SW_URL . 'build/' );
define( 'SW_DEV_ASSETS_PATH', SW_PATH . 'build/' );
define( 'SW_INCLUDES', SW_PATH . 'includes/' );


define( 'SW_TEXTDOMAIN', 'simple-wishlist' );
define( 'SW_PLUGIN_URL', 'https://simple-wishlist.com' );
define( 'SW_ADMIN_URL', SW_ASSETS . 'admin/' );
define( 'SW_PUBLIC_URL', SW_ASSETS . 'public/' );

/**
 * The Core Engine of the Plugin
 */
if ( ! class_exists( '\SimpleWishlist\SimpleWishlist' ) ) {
    require_once SW_PATH . 'vendor/autoload.php';
    if(sw_is_plugin_active( 'simple-wishlist-pro/simple-wishlist-pro.php' )){
        add_action( 'admin_notices', 'sw_free_compatibility_notice' );
        if( file_exists(dirname(SW_PATH) . '/simple-wishlist-pro/vendor/autoload.php') ) {
            require_once dirname(SW_PATH) . '/simple-wishlist-pro/vendor/autoload.php';
        } else {
            add_action('plugins_loaded', function(){
                remove_action( 'admin_notices', 'simple_wishlist_install_core_notice' );
            });
        }
    }

    function activate_simple_wishlist() {
        \SimpleWishlist\SimpleWishlist::get_instance()->activator();
    }
    /**
     * Plugin Activator
     */
    register_activation_hook( SW_FILE, 'activate_simple_wishlist' );
    \SimpleWishlist\SimpleWishlist::get_instance();
}

function sw_free_compatibility_notice(){
    if ( ! function_exists( 'get_plugins' ) ) {
        require_once ABSPATH . 'wp-admin/includes/plugin.php';
    }
    $plugins = get_plugins();
    if( isset( $plugins['simple-wishlist-pro/simple-wishlist-pro.php'], $plugins['simple-wishlist-pro/simple-wishlist-pro.php']['Version'] ) && version_compare( $plugins['simple-wishlist-pro/simple-wishlist-pro.php']['Version'], '2.1.0', '>=' ) ) {
        return;
    }
    ?>
        <div class="notice notice-warning is-dismissible">
            <p>
            <?php echo sprintf(__("<strong>Recommended: </strong> Seems like you haven't updated the SimpleWishlist Pro version. Please make sure to update SimpleWishlist Pro plugin from <a href='%s'><strong>wp-admin -> Plugins</strong></a>.", 'simple-wishlist' ), esc_url( admin_url('plugins.php' )));?></p>
        </div>
    <?php
}


function sw_is_plugin_active( $plugin ) {
    return in_array( $plugin, (array) get_option( 'active_plugins', array() ), true ) || sw_is_plugin_active_for_network( $plugin );
}


function sw_is_plugin_active_for_network( $plugin ) {
    if ( ! is_multisite() ) {
        return false;
    }

    $plugins = get_site_option( 'active_sitewide_plugins' );
    if ( isset( $plugins[ $plugin ] ) ) {
        return true;
    }

    return false;
}