<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit89ec86686dc155609d949b902a87bbd5
{
    public static $classMap = array (
        'AutoCampaign\\Admin\\Admin' => __DIR__ . '/../..' . '/includes/Admin/Admin.php',
        'AutoCampaign\\AutoCampaign' => __DIR__ . '/../..' . '/includes/AutoCampaign.php',
        'AutoCampaign\\GetInstance' => __DIR__ . '/../..' . '/includes/GetInstance.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'SimpleWishlistPro\\Core\\RoleManagement' => __DIR__ . '/../..' . '/includes/Core/RoleManagement.php',
        'SimpleWishlist\\Admin\\Layouts' => __DIR__ . '/../..' . '/includes/Admin/Layouts.php',
        'SimpleWishlist\\Admin\\Settings' => __DIR__ . '/../..' . '/includes/Admin/Settings.php',
        'SimpleWishlist\\Core\\Database' => __DIR__ . '/../..' . '/includes/Core/Database.php',
        'SimpleWishlist\\Core\\Helper' => __DIR__ . '/../..' . '/includes/Core/Helper.php',
        'SimpleWishlist\\Core\\REST' => __DIR__ . '/../..' . '/includes/Core/REST.php',
        'SimpleWishlist\\Core\\Rule' => __DIR__ . '/../..' . '/includes/Core/Rule.php',
        'SimpleWishlist\\Core\\Rules' => __DIR__ . '/../..' . '/includes/Core/Rules.php',
        'SimpleWishlist\\Extensions\\GlobalFields' => __DIR__ . '/../..' . '/includes/Extensions/GlobalFields.php',
        'SimpleWishlist\\Frontend\\Frontend' => __DIR__ . '/../..' . '/includes/Frontend/Frontend.php',
        'SimpleWishlist\\Shortcode\\Shortcode' => __DIR__ . '/../..' . '/includes/Frontend/Shortcode.php',
        'UsabilityDynamics\\Job' => __DIR__ . '/..' . '/wpdeveloper/lib-utility/lib/class-job.php',
        'UsabilityDynamics\\Loader' => __DIR__ . '/..' . '/wpdeveloper/lib-utility/lib/class-loader.php',
        'UsabilityDynamics\\Settings' => __DIR__ . '/..' . '/wpdeveloper/lib-settings/lib/class-settings.php',
        'UsabilityDynamics\\Structure' => __DIR__ . '/..' . '/wpdeveloper/lib-utility/lib/class-structure.php',
        'UsabilityDynamics\\Term' => __DIR__ . '/..' . '/wpdeveloper/lib-utility/lib/class-term.php',
        'UsabilityDynamics\\Utility' => __DIR__ . '/..' . '/wpdeveloper/lib-utility/lib/class-utility.php',
        'UsabilityDynamics\\Utility\\Guid_Fix' => __DIR__ . '/..' . '/wpdeveloper/lib-utility/lib/class-guid-fix.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->classMap = ComposerStaticInit89ec86686dc155609d949b902a87bbd5::$classMap;

        }, null, ClassLoader::class);
    }
}
