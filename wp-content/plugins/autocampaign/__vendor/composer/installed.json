{"packages": [{"name": "wpdeveloper/lib-settings", "version": "dev-master", "version_normalized": "dev-master", "source": {"type": "git", "url": "https://github.com/WPDevelopers/lib-settings.git", "reference": "5ddf9c46be5e06d27ac3eb1860fa6e2cb2b9b7df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WPDevelopers/lib-settings/zipball/5ddf9c46be5e06d27ac3eb1860fa6e2cb2b9b7df", "reference": "5ddf9c46be5e06d27ac3eb1860fa6e2cb2b9b7df", "shasum": ""}, "require": {"wpdeveloper/lib-utility": "dev-master"}, "require-dev": {"justinrainbow/json-schema": "1.1.*"}, "time": "2024-05-13T07:52:33+00:00", "default-branch": true, "type": "library", "extra": {"component": {"scripts": ["static/scripts/settings.js"], "templates": ["static/views/new-installation.xhtml"], "files": ["static/schemas/settings.json"]}}, "installation-source": "dist", "autoload": {"classmap": ["lib"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.usabilitydynamics.com", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.usabilitydynamics.com", "role": "Developer"}, {"name": "<PERSON>", "email": "anton.korot<PERSON>@usabilitydynamics.com", "homepage": "https://www.usabilitydynamics.com", "role": "Developer"}], "homepage": "https://github.com/udx/lib-settings", "keywords": ["settings"], "support": {"source": "https://github.com/WPDevelopers/lib-settings/tree/master"}, "install-path": "../wpdeveloper/lib-settings"}, {"name": "wpdeveloper/lib-utility", "version": "dev-master", "version_normalized": "dev-master", "source": {"type": "git", "url": "**************:WPDevelopers/lib-utility.git", "reference": "9b8ebd1ec89f2c0ae5c4207cfe7b1daddf592fe1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WPDevelopers/lib-utility/zipball/9b8ebd1ec89f2c0ae5c4207cfe7b1daddf592fe1", "reference": "9b8ebd1ec89f2c0ae5c4207cfe7b1daddf592fe1", "shasum": ""}, "require-dev": {"phpunit/phpunit": "4.1.*"}, "time": "2024-05-13T07:52:38+00:00", "default-branch": true, "type": "library", "extra": {"installer-name": "lib-utility", "component": {"name": "utility", "scripts": ["scripts/*.js"]}}, "installation-source": "dist", "autoload": {"classmap": ["lib"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.usabilitydynamics.com", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.usabilitydynamics.com", "role": "Developer"}, {"name": "<PERSON>", "email": "anton.korot<PERSON>@usabilitydynamics.com", "homepage": "https://www.usabilitydynamics.com", "role": "Developer"}], "keywords": ["job", "process", "utility"], "support": {"source": "https://github.com/WPDevelopers/lib-utility/tree/master", "issues": "https://github.com/WPDevelopers/lib-utility/issues"}, "install-path": "../wpdeveloper/lib-utility"}], "dev": true, "dev-package-names": []}