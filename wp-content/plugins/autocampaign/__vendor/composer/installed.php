<?php return array(
    'root' => array(
        'name' => 'wpdeveloper/autocampaign',
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'reference' => 'c9b784a0e0645b9c2c2df6a9416b7f04cbfd2828',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'wpdeveloper/autocampaign' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => 'c9b784a0e0645b9c2c2df6a9416b7f04cbfd2828',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wpdeveloper/lib-settings' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '5ddf9c46be5e06d27ac3eb1860fa6e2cb2b9b7df',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wpdeveloper/lib-settings',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
        'wpdeveloper/lib-utility' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '9b8ebd1ec89f2c0ae5c4207cfe7b1daddf592fe1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wpdeveloper/lib-utility',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
    ),
);
