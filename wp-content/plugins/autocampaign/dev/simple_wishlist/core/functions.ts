
import apiFetch from "@wordpress/api-fetch";
import { sprintf, __ } from "@wordpress/i18n";
import Swal from 'sweetalert2';
import { useAppContext } from "../hooks";
import { ToastAlert } from './ToasterMsg'

/**
 * apiFetch setup
 */
// apiFetch.use(apiFetch.createNonceMiddleware(api_nonce));
// apiFetch.use(apiFetch.createRootURLMiddleware(rest_url));

class AutoCampaignHelpers {
    namespace = "/autocampaign";
    version = "v1";
    getPath = (path) => {
        return `${this.namespace}/${this.version}/${path}`;
    };
    post = (endpoint, data = {}, args = {}) => {
        let path = this.getPath(endpoint);
        args = { path, method: "POST", data, ...args };
        return apiFetch(args)
            .then((res) => res)
            .catch((err) => console.error(err));
    };
    delete = (endpoint, data = {}, args = {}) => {
        let path = this.getPath(endpoint);
        args = { path, method: "DELETE", data, ...args };
        return apiFetch(args)
            .then((res) => res)
            .catch((err) => console.error(err));
    };
    get = (endpoint, args = {}, queryParams = {}) => {
        
         // Convert query parameters object to a query string
        const queryString = new URLSearchParams(queryParams).toString();
        let path = this.getPath(endpoint);

        // Append query string to path if there are any query parameters
        if (queryString) {
            path += `?${queryString}`;
        }

        args = { path, method: "GET", ...args };
        return apiFetch(args)
            .then((res) => res)
            .catch((err) => console.error(err));
    };
    useQuery = (search) => {
        search = search;
        return new URLSearchParams(search);
    };
    getParam = (param, d = null) => {
        const query = autocampaignHelper.useQuery(location.search);
        return query.get(param) || d;
    };
    getRedirect = (params: {[key: string]: any}, keepHash = false) => {
        const query = autocampaignHelper.useQuery(location.search);
        const hash = keepHash === true ? location.hash : (typeof keepHash == 'string' ? keepHash : '');

        switch (params.page) {
            case 'autocampaign-dashboard':
                query.delete('comparison');
                query.delete('tab');
                query.delete('id');
                break;
            case 'autocampaign-settings':
                query.delete('comparison');
                query.delete('status');
                query.delete('per-page');
                query.delete('p');
                query.delete('id');

                break;
            default:
                break;
        }

        for (const key in params) {
            query.set(key, params[key]);
        }

        return {
            pathname: '/admin.php',
            search: '?' + query.toString() + hash,
        };
    }
    swal = ({confirmedCallback, completeAction, completeArgs, afterComplete, ...args}) => {
        Swal.fire(args).then((result) => {
            if (result.isConfirmed) {
                confirmedCallback().then((res) => {
                    if (res?.success) {
                        const result = completeAction(res);
                        const [type, message] = completeArgs(result);
                        ToastAlert(type, message).then(afterComplete);
                    }
                })
                .catch((err) => console.error("Delete Error: ", err));
            }
        });
    }
}

const autocampaignHelper = new AutoCampaignHelpers();

export const SweetAlert = ( args: any = {} ) => {
	return Swal.mixin({
		target: args?.target ?? "#autocampaign",
		type: args?.type ?? "success",
		html: args?.html,
		title: args?.title ?? __("Title Goes Here: title", 'autocampaign'),
		text: args?.text ?? __("Text Goes Here: text", 'autocampaign'),
		icon: args?.icon ?? (args?.type || "success"),
		timer: args?.timer ?? null,
		...args,
	});
};

export const getThemeName = (settings) => {
    let themeName = settings.themes.replace(settings.source + "_", "");
    themeName = themeName.replace(settings.type + "_", "");
    if (settings?.custom_type) {
        themeName = themeName.replace(settings?.custom_type + "_", "");
    }
    return themeName;
}

export const proAlert = ( html = null ) => {
    if( html === null ) {
        html = sprintf(__("You need to upgrade to the <strong><a href='%s' target='_blank'>Premium Version</a></strong> to use this feature.", 'autocampaign'), 'http://wpdeveloper.net/in/upgrade-autocampaign');
    }
    return SweetAlert({
        showConfirmButton: false,
        showDenyButton: true,
        type: 'warning',
        title: __('Opps...', 'autocampaign'),
        customClass: { actions: 'rb-pro-alert-actions' },
        denyButtonText: 'Close',
        html
    });
}

export const settingsSavedAlert = ( html = null ) => {
    return SweetAlert({
        showConfirmButton: false,
        showDenyButton: true,
        type: 'success',
        title: __('Settings saved successfully', 'autocampaign'),
        customClass: { actions: 'rb-pro-alert-actions' },
        denyButtonText: 'Close',
        html
    });
}

export const assetsURL = (path ='', admin = true) => {
    const builderContext = useAppContext();
    if(admin){
        return builderContext.assets.admin + path;
    }
    else{
        return builderContext.assets.public + path;
    }
}

export default autocampaignHelper;