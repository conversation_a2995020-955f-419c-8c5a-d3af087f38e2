
.wprf-control-datetime{
  .components-datetime {
    .components-datetime__time {
      .components-datetime__time-field-month-select,
      input[type="number"],
      input[type="text"] {
          min-height: 32px;
          padding: 2px 8px;
          border: 1.5px solid #e6effb;
          border-radius: 5px;
          color: #25396f;
          font-size: 12px;
          font-weight: 500;
          width: initial;
          max-width: 400px;
          margin-right: 5px;
          &::placeholder {
              color: #a7b6d8;
          }
          &:focus {
              border-color: #7c8db5;
              outline: none;
              box-shadow: none;
          }
      }
      input[type="number"] {
          max-width: 50px;
      }
      input.readonly,
      input[readonly],
      textarea.readonly,
      textarea[readonly] {
          background: rgba(#6a4bff, 0.1);
      }
      .components-datetime__time-field-month-select {
        width: 100px;
      }
      .components-datetime__time-field-time {
        display: flex;
        .components-datetime__time-separator {
          align-self: center;
        }
      }
      .components-datetime__time-field-am-pm {
        display: flex;
      }
      .components-button-group {
        .components-button {
          min-height: 32px;
          border-radius: 5px;
          font-size: 12px;
          padding: 2px 6px;
          font-weight: 500;
          margin-right: 5px;
          &.is-secondary {
            border: 1.5px solid #7c8db5;
            color: #fff;
            background: #7c8db5;
          }
          &.is-primary {
            border: 1.5px solid #6a4bff;
            color: #fff;
            background: #6a4bff;
          }
        }
      }
      .components-datetime__timezone {
        display: flex;
        align-items: center;
      }
    }
    .components-datetime__date .CalendarDay__selected:hover {
      background: rgba(#6648fe, 0.7)!important;
    }
    .components-datetime__buttons {
      display: none;
    }
  } 
} 