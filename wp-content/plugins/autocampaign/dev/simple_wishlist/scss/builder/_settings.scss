.simple_wishlist-settings {
    .simple_wishlist-settings-content {
        display: flex;
        padding-left: 0;
        padding-right: 0;
        margin-top: 30px;
        @media only screen and (max-width: 1399px) {
            margin-top: 20px;
        }
        @media only screen and (max-width: 1279px) {
            flex-direction: column-reverse;
        }
        .wprf-tab-flex {
            display: flex;
            @media only screen and (max-width: 1279px) {
                flex-direction: column-reverse;
            }
            .wprf-tab-contents {
                flex: 6;
            }
        }
    }
    // Right Side Settings CSS
    .simple_wishlist-settings-right {
        margin-top: 0;
        border-radius: 10px;
        margin-bottom: 0;
        box-shadow: none;
        padding: 20px;
        flex: 2;
        display: none;
        @media only screen and (max-width: 1279px) {
            margin-bottom: 20px;
            margin-left: 0;
        }
        .rb-sidebar {
            margin-top: 0px;
            @media only screen and (max-width: 1279px) {
                padding: 0px;
            }
            .rb-sidebar-block {
                @media only screen and (max-width: 1279px) {
                    margin: 0;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    justify-content: space-between;
                    width: 100%;
                }
                .simple_wishlist-dashboard-sidebar-logo {
                    max-width: 300px;
                    margin-bottom: 50px;
                    @media only screen and (max-width: 1399px) {
                        max-width: 250px;
                        margin-bottom: 30px;
                    }
                    @media only screen and (max-width: 1279px) {
                        margin: 0;
                        img{
                            margin: 0;
                        }
                    }
                }
                .simple_wishlist-dashboard-sidebar-cta{
                    display: flex;
                }
                .simple_wishlist-dashboard-sidebar-cta a {
                    min-height: 46px;
                    font-size: 16px;
                    font-weight: 500;
                    background: rgba(#6A4BFF, 0.05);
                    color: #25396F;
                    letter-spacing: 0px;
                    display: inline-flex;
                    justify-content: center;
                    align-items: center;
                    border: 2px solid rgba(#6A4BFF, 0.05);
                    border-radius: 10px;
                    padding: 2px 40px;
                    width: initial;
                    outline: none;
                    box-sizing: border-box;
                    transition: all .3s ease-in-out;
                    @media only screen and (max-width: 1399px) {
                        padding: 2px 25px;
                        margin-top: 0px;
                    }
                    @media only screen and (max-width: 1279px) {
                        margin: 0;
                    }
                    &:hover {
                        color: #fff;
                        background: #6A4BFF;
                        border-color:#6A4BFF;
                        box-shadow: 0 15px 25px -5px rgba(#5614d5, 0.3);
                    }
                }
            }

        }
    }
    .simple_wishlist-settings-documentation {
        margin: 0;
        margin-top: 30px;
        padding: 0;
        background-color: transparent;
        @media only screen and (max-width: 1399px) {
            margin-top: 20px;
        }
        .simple_wishlist-settings-row {
            margin-left: -10px;
            margin-right: -10px;
            @media only screen and (max-width: 1279px) {
                flex-wrap: wrap;
            }
            .simple_wishlist-dashboard-block {
                margin-left: 10px;
                margin-right: 10px;
                background: #fff;
                border: none;
                box-shadow: none;
                outline: none;
                padding: 30px;
                border-radius: 10px;
                @media only screen and (max-width: 1599px) {
                    padding: 20px;
                }
                @media only screen and (max-width: 1279px) {
                    flex-basis: calc(50% - 60px);
                    margin-bottom: 20px;
                }
                .simple_wishlist-dashboard-block-header {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    .simple_wishlist-dashboard-block-header-icon {
                        height: 40px;
                        width: 40px;
                    }
                    .simple_wishlist-dashboard-title {
                        font-size: 20px;
                        font-weight: 700;
                        color: #25396f;
                        margin-top: 20px;
                        margin-left: 0;
                        line-height: 1.3;
                    }
                }
                .simple_wishlist-dashboard-block-content{
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    p{
                        font-size: 15px;
                        font-weight: 400;
                        color: #7C8DB5;
                        line-height: 1.8;
                        margin-bottom: 26px;
                        @media only screen and (max-width: 1599px) { 
                          font-size: 14px;
                        }
                        a{
                            text-decoration: underline;
                            color: #6A4BFF;
                        }
                    }
                    .rb-button{
                        margin-top: auto;color:  #fff;
                        border: 1.5px solid #6A4BFF;
                        border-radius: 10px;
                        display: inline-flex;
                        min-height: 50px;
                        padding: 2px 35px;
                        font-size: 16px;
                        font-weight: 500;
                        align-items: center;
                        justify-content: center;
                        text-decoration: none;
                        outline: none;
                        color: #6A4BFF;
                        background: #fff;
                        line-height: 1.15;
                        transition: 0.3s ease-in-out;
                        cursor: pointer;
                        box-sizing: border-box;
                        @media only screen and (max-width: 1599px) { 
                          min-height: 40px;
                          padding: 2px 20px;
                          font-size: 14px;
                          border-radius: 5px;
                        }
                        &:hover,
                        &:focus{
                            color: #fff;
                            background-color: #5614d5;
                            border-color: #5614d5;
                            outline: none;
                            box-shadow: 0 15px 25px -5px rgba(#5614d5, 0.3);
                        }
                    }
                }
            }
        }
    }
}
