

.Toastify__toast--info {
    background: #6a4bff;
}

.Toastify__toast--error {
    background: #e93e41;
}

.Toastify__toast--warning {
    background: #FC9714;
}

.Toastify__close-button {
    color: #fff;
    opacity: 0.5;
}

.rb-toast-wrapper {
    display: flex;
    align-items: center;
    img,
    svg,
    object {
        display: inline-flex;
        width: 45px;
        height: 45px;
        fill: #fff;
        color: #fff;
    }
    p {
        margin-left: 10px;
        color: #fff;
    }
}
