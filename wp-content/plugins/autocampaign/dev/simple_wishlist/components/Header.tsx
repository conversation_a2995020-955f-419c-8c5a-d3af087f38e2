import React, { useState } from 'react';
import { __ } from '@wordpress/i18n';
import { Link } from 'react-router-dom'
import { applyFilters } from '@wordpress/hooks'
import { useAppContext } from '../hooks';
import nxHelper from '../core/functions';

const Version = ({ version }) => {
    return <span>{__("SimpleWishlist:", 'simple_wishlist')}  <strong>{version}</strong></span>
}

const Header = ({ addNew = false, context = {} }) => {
    const builderContext = useAppContext();
    const pro_version = builderContext.pro_version;
    const version = builderContext.version;
    return (
        <div className="simple_wishlist-settings-header">
            <div className="rb-header-left">
                <div className="simple_wishlist-dashboard-header">
                    {!builderContext?.createRedirect && !addNew && <Link className="rb-add-new-btn" to={nxHelper.getRedirect({page: `rb-edit`})}>{__('Add New', 'simple_wishlist')}</Link>}
                </div>
            </div>
            <div className="rb-header-right">
                {applyFilters('simple_wishlist_header', <Version version={version} />)}
                {typeof pro_version === 'string' && <span>{__("SimpleWishlist Pro:", 'simple_wishlist')} <strong>{pro_version}</strong></span>}
            </div>
        </div >
    )
}
export default Header;