import './scss/main.scss';
import { createWishlistButton, insertWishlistButton } from './includes/wishlistButton';
import { attachWishlistEvent } from './includes/api';
// @ts-ignore 
const settings = simple_wishlistFrontend;
document.addEventListener("DOMContentLoaded", function () {    
    setTimeout(() => {
        let form = document.querySelector('form[data-wp-context]');
        if (form) {
            let wpContext = JSON.parse(form.getAttribute('data-wp-context'));
            let productId = wpContext?.product?.id;
            let productName = wpContext?.product?.name;
            if (productId) {
                let wishlistButtons = document.querySelectorAll('.sw-wishlist-button');
                wishlistButtons.forEach(button => {
                    button.setAttribute('data-product-id', productId);
                    if( productName ) {
                        button.setAttribute('data-product-name', productName);
                    }
                });
            }
        }
    }, 0);
});

// Add wishlist for single product page
document.addEventListener("DOMContentLoaded", function () {

    let targets = [
        { key: "after_tags", selector: ".single-sc_product .wp-block-surecart-product-collection-tags", position: "after" },
        { key: "after_title", selector: ".single-sc_product .wp-block-surecart-product-title", position: "after" },
        { key: "after_description", selector: ".single-sc_product .sc-prose.wp-block-surecart-product-description", position: "after" },
        { key: "after_quantity", selector: ".single-sc_product .wp-block-surecart-product-quantity", position: "after" },
        { key: "before_buy_button", selector: ".single-sc_product .wp-block-surecart-product-buy-buttons", position: "top" },
        { key: "after_buy_button", selector: ".single-sc_product .wp-block-surecart-product-buy-buttons", position: "bottom" }
    ];
     // Find the matching target for the selected key and insert button
     let target = targets.find(t => t.key === settings.button_position);     
     if (target && settings?.show_button_on_single_shop) {
         let element = document.querySelector(target.selector);
         insertWishlistButton(target.position, element);
     }
});


// Shop page options
document.addEventListener("DOMContentLoaded", function () {
    // @ts-ignore 
    if(settings?.show_button_shop) {
        // @ts-ignore 
        addWishlistButton( settings?.button_position_shop || "after_title"); // Change this to "after-image" or "end"
    }
});

function addWishlistButton(position = "after_title") {
    let shopProducts = document.querySelectorAll(".wp-block-surecart-product-template .sc-product-item");
    shopProducts.forEach(product => {
        let productContext = product.querySelector('.sc-product-item-link')?.getAttribute('data-wp-context');        
        if (productContext) {
            let wpContext = JSON.parse(productContext);
            let productId = wpContext?.product?.id;
            let productName = wpContext?.product?.name;
            if (productId) {
                let wishlistButton = createWishlistButton(position);
                if (wishlistButton) {
                    wishlistButton.setAttribute("data-product-id", productId);
                    if( productName ) {
                        wishlistButton.setAttribute("data-product-name", productName);
                    }
                    wishlistButton.classList.add(`wishlist-button-${position}`); // Add class based on position

                    // Handle different positions directly
                    switch (position) {
                        case "inside_image":
                            product.insertAdjacentElement("afterbegin", wishlistButton);
                            break;
                        case "after_title":
                            product.insertAdjacentElement("beforeend", wishlistButton);
                            break;
                        default: // "end"
                            product.insertAdjacentElement("beforeend", wishlistButton);
                            break;
                    }
                }
            }
        }
    });
}

document.addEventListener("DOMContentLoaded", function () {
    // @ts-ignore 
    if(settings?.show_button_shop) { 
        setTimeout(() => {
            let wishlistButtons = document.querySelectorAll('.sw-wishlist-button');
            wishlistButtons.forEach(button => {
                attachWishlistEvent(button); // Attach click event
            });
        }, 100);
    }
    
});
