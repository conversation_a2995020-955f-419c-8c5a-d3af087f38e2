import './scss/main.scss';
import { createWishlistButton, insertWishlistButton } from './includes/wishlistButton';
import { attachWishlistEvent } from './includes/api';

document.addEventListener("DOMContentLoaded", function () {
    // Wishlist for Product Single page
    let targets = [
        { selector: ".single-sc_product .wp-block-surecart-product-collection-tags", position: "after" },
        // { selector: ".single-sc_product .wp-block-surecart-product-title", position: "after" },
        // { selector: ".single-sc_product .sc-prose.wp-block-surecart-product-description", position: "after" },
        // { selector: ".single-sc_product .wp-block-surecart-product-quantity", position: "after" },
        // { selector: ".single-sc_product .wp-block-surecart-product-buy-buttons", position: "top" },
        // { selector: ".single-sc_product .wp-block-surecart-product-buy-buttons", position: "bottom" }
    ];
    

    targets.forEach(target => {
        let element = document.querySelector(target.selector);
        insertWishlistButton(target.position, element);
    });

    setTimeout(() => {
        let form = document.querySelector('form[data-wp-context]');
        if (form) {
            let wpContext = JSON.parse(form.getAttribute('data-wp-context'));
            let productId = wpContext?.product?.id;
            let productName = wpContext?.product?.name;
            if (productId) {
                let wishlistButtons = document.querySelectorAll('.ac-wishlist-button');
                wishlistButtons.forEach(button => {
                    button.setAttribute('data-product-id', productId);
                    if( productName ) {
                        button.setAttribute('data-product-name', productName);
                    }
                });
            }
        }
    }, 0);
});

// Shop page options
document.addEventListener("DOMContentLoaded", function () {
    addWishlistButton("after-image"); // Change this to "after-image" or "end"
});

function addWishlistButton(position = "before-image") {
    let shopProducts = document.querySelectorAll(".wp-block-surecart-product-template .sc-product-item");
    shopProducts.forEach(product => {
        let productContext = product.querySelector('.sc-product-item-link')?.getAttribute('data-wp-context');        
        if (productContext) {
            let wpContext = JSON.parse(productContext);
            let productId = wpContext?.product?.id;
            let productName = wpContext?.product?.name;
            if (productId) {
                let wishlistButton = createWishlistButton(position);
                if (wishlistButton) {
                    wishlistButton.setAttribute("data-product-id", productId);
                    if( productName ) {
                        wishlistButton.setAttribute("data-product-name", productName);
                    }
                    wishlistButton.classList.add(`wishlist-button-${position}`); // Add class based on position

                    // Handle different positions directly
                    switch (position) {
                        case "before-image":
                            product.insertAdjacentElement("afterbegin", wishlistButton);
                            break;
                        case "after-image":
                            product.insertAdjacentElement("beforeend", wishlistButton);
                            break;
                        default: // "end"
                            product.insertAdjacentElement("beforeend", wishlistButton);
                            break;
                    }
                }
            }
        }
    });
}



document.addEventListener("DOMContentLoaded", function () {
    setTimeout(() => {
        let wishlistButtons = document.querySelectorAll('.sw-wishlist-button');
        wishlistButtons.forEach(button => {
            attachWishlistEvent(button); // Attach click event
        });
    }, 100);
});
