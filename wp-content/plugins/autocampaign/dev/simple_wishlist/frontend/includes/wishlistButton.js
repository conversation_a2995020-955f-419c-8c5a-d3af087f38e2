import { createElement } from './domUtils';

const loveIcon = `
    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="11" viewBox="0 0 13 11" fill="none">
        <path d="M11.3207 0.700087C9.72484 -0.387976 7.75423 0.119787 6.69035 1.36501C5.62647 0.119787 3.65586 -0.39402 2.06004 0.700087C1.21377 1.28039 0.681825 2.25964 0.645557 3.2933C0.560929 5.63868 2.64034 7.51861 5.81386 10.402L5.8743 10.4564C6.33371 10.8735 7.04095 10.8735 7.50035 10.4503L7.56684 10.3899C10.7404 7.51257 12.8137 5.63264 12.7351 3.28726C12.6989 2.25964 12.1669 1.28039 11.3207 0.700087Z" fill="#202020"/>
    </svg>
`;
const ADD_TO_WISHLIST =   `${loveIcon} <span>Add to Wishlist</span>`;
const ADD_TO_WISHLIST_ICON =   `${loveIcon} <span>Add to Wishlist</span>`;


export function createWishlistButton(position) {
    let button;
    if( position == 'inside_image' ) {
        button = createElement("button", "sw-wishlist-button", ADD_TO_WISHLIST_ICON);
    }else{
        button = createElement("button", "sw-wishlist-button", ADD_TO_WISHLIST);
    }
    return button;
}

export function insertWishlistButton(position, referenceElement) {
    if (referenceElement) {
        let wishlistBtn = createWishlistButton();
        switch (position) {
            case "before":
                referenceElement.insertAdjacentElement("beforebegin", wishlistBtn);
                break;
            case "after":
                referenceElement.insertAdjacentElement("afterend", wishlistBtn);
                break;
            case "top":
                referenceElement.insertAdjacentElement("afterbegin", wishlistBtn);
                break;
            case "bottom":
                referenceElement.insertAdjacentElement("beforeend", wishlistBtn);
                break;
        }
    }
}
