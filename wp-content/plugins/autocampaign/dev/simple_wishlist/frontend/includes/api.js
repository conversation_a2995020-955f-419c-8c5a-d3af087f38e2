import { showMessage } from "./toaster";

const loveIcon = `
    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="11" viewBox="0 0 13 11" fill="none">
        <path d="M11.3207 0.700087C9.72484 -0.387976 7.75423 0.119787 6.69035 1.36501C5.62647 0.119787 3.65586 -0.39402 2.06004 0.700087C1.21377 1.28039 0.681825 2.25964 0.645557 3.2933C0.560929 5.63868 2.64034 7.51861 5.81386 10.402L5.8743 10.4564C6.33371 10.8735 7.04095 10.8735 7.50035 10.4503L7.56684 10.3899C10.7404 7.51257 12.8137 5.63264 12.7351 3.28726C12.6989 2.25964 12.1669 1.28039 11.3207 0.700087Z" fill="#202020"/>
    </svg>
`;
const AFTER_ADDED_TO_WISHLIST = `${loveIcon} <span>Remove from Wishlist</span>`;
const ADD_TO_WISHLIST =   `${loveIcon} <span>Add to Wishlist</span>`;
// Helper function to get cookie value safely
function getCookie(name) {
    let cookies = document.cookie.split('; ');
    let cookie = cookies.find(row => row.startsWith(name + '='));

    try {
        return cookie ? JSON.parse(decodeURIComponent(cookie.split('=')[1])) : [];
    } catch (error) {
        console.error("Error parsing wishlist cookie:", error);
        return [];
    }
}

// Helper function to set cookie
function setCookie(name, value, days = 30) {
    let expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${encodeURIComponent(JSON.stringify(value))}; expires=${expires.toUTCString()}; path=/`;
}

const wishlistApiUrl = "/wp-json/autocampaign/v1"; // Ensure this matches your registered API namespace

// Function to update wishlist via API
async function updateWishlist(productId, button, isRemove = false) {
    button.innerHTML = isRemove ? "Removing..." : "Updating...";
    let productName = button.getAttribute("data-product-name") || "Product";
    try {
        let response = await fetch(`${wishlistApiUrl}/wishlist`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ product_id: productId })
        });

        let result = await response.json();
        if (result.success) {
            let wishlist = result.wishlist.map(id => id.toString()); // Normalize IDs as strings
            setCookie("sw-wishlist", wishlist); // Sync with cookies
            
            if (result.action === "removed") {
                button.closest(".wishlist-item")?.remove(); // Remove from DOM if applicable
                showMessage(` "${productName}" removed from your wishlist.`, "error");
            } else {
                button.innerHTML = AFTER_ADDED_TO_WISHLIST;
                button.classList.add("added");
                showMessage(` "${productName}" added from your wishlist.`, "success");
            }
            if (isRemove) {
                // location.reload();
                button.classList.remove("added");
                button.innerHTML = ADD_TO_WISHLIST;
            }
        } else {
            button.innerHTML = "Error";
        }
    } catch (error) {
        console.error("Wishlist error:", error);
        button.innerHTML = "Failed";
    }
}

// Attach click event for adding/removing from wishlist
export function attachWishlistEvent(button) {
    button.addEventListener("click", (event) => {
        event.preventDefault();
        let productId = button.getAttribute("data-product-id");
        if (!productId) return;
        if( button.classList.contains('added') ) {
            updateWishlist(productId, button, true);
        }else{
            updateWishlist(productId, button); // Handles both adding and removing
        }
    });
}

// Load wishlist status from Cookie or API
export async function updateWishlistButtons() {
    let wishlist = getCookie("ac-wishlist"); // Get wishlist from cookie
    if (wishlist.length > 0) {
        // If wishlist data exists in cookie, update buttons without calling API
        updateWishlistButtonsFromData(wishlist);
    } else {
        // If no cookie found, fetch from API and update buttons
        try {
            let response = await fetch(`${wishlistApiUrl}/wishlist`);
            let result = await response.json();
            if (result.success) {
                let wishlist = result.data.map(id => id.toString()); // Ensure IDs are strings
                setCookie("ac-wishlist", wishlist); // Store in cookie
                updateWishlistButtonsFromData(wishlist);
            }
        } catch (error) {
            console.error("Error loading wishlist:", error);
        }
    }
}

// Function to update buttons based on wishlist data
function updateWishlistButtonsFromData(wishlist) {
    setTimeout(() => {
        document.querySelectorAll(".sw-wishlist-button").forEach(button => {            
            let productId = button.getAttribute("data-product-id");
            let isWishlisted = wishlist.includes(productId);
            button.innerHTML =  isWishlisted ? AFTER_ADDED_TO_WISHLIST : ADD_TO_WISHLIST;
            button.classList.toggle("added", isWishlisted);
        });
    }, 300);
}

// Attach Remove Button Event
export function attachRemoveEvent(button) {
    button.addEventListener("click", (event) => {
        event.preventDefault();
        let productId = button.getAttribute("data-id");
        if (!productId) return;
        updateWishlist(productId, button, true);
    });
}

// Initialize Wishlist Buttons on Page Load
document.addEventListener("DOMContentLoaded", function () {
    updateWishlistButtons(); // Load wishlist data from cookie or API
    document.querySelectorAll(".remove-btn").forEach(button => attachRemoveEvent(button));
});
