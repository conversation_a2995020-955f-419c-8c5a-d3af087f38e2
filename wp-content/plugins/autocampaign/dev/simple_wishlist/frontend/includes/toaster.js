// Ensure toast container exists on initial load
(function createToastContainer() {
    if (!document.getElementById("sw-toast-container")) {
        let toastContainer = document.createElement("div");
        toastContainer.id = "sw-toast-container";
        document.body.appendChild(toastContainer);
    }
})();

// Show toast message dynamically
export function showMessage(message, type = "success") {
    let toastContainer = document.getElementById("sw-toast-container");

    let toast = document.createElement("div");
    toast.className = `sw-toast ${type === "error" ? "error" : ""}`;
    toast.innerText = message;

    toastContainer.appendChild(toast);

    // Fade out after 3 seconds
    setTimeout(() => {
        toast.style.opacity = "0";
        setTimeout(() => toast.remove(), 500);
    }, 3000);
}
