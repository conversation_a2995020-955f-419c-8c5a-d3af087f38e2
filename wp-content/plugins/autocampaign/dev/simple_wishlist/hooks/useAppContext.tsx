import { __ } from '@wordpress/i18n';
import * as React from 'react';

export const SimpleWishlistContext = React.createContext(undefined as any);
SimpleWishlistContext.displayName = process.env.NODE_ENV === 'production' ? 'Anonymous' : 'SimpleWishlistContext';

export const SimpleWishlistProvider = SimpleWishlistContext.Provider;
export const SimpleWishlistConsumer = SimpleWishlistContext.Consumer;

export default function useAppContext() {
    const appContext = React.useContext(SimpleWishlistContext);
    __(
        !!appContext,
        __(`SimpleWishlistContext context is undefined, please verify you are calling useAppContext() as child of a <SimpleWishlist> component.`, 'simple_wishlist')
    );
    return appContext;
}
