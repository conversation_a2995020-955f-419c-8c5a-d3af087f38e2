<?php

/**
 * AutoCampaign File
 *
 * @package AutoCampaign
 */

namespace AutoCampaign;

use AutoCampaign\Admin\Admin;
use AutoCampaign\Admin\Layouts;
use AutoCampaign\Admin\Settings;
use AutoCampaign\Core\REST;
use AutoCampaign\Extensions\GlobalFields;
use AutoCampaign\Frontend\Frontend;
use AutoCampaign\Shortcode\Shortcode;
use AutoCampaignPro\Core\RoleManagement;

/**
 * Plugin Engine.
 */
class AutoCampaign {
    /**
     * Instance of AutoCampaign
     * @var AutoCampaign
     */
    use GetInstance;
    /**
     * Settings
     * @var Settings
     */
    public $settings;
    /**
     * WP_CLI
     * @var boolean
     */
    public static $WP_CLI = false;
    /**
     * Invoked initially.
     */
    public function __construct() {
        static::$WP_CLI = defined('WP_CLI') && WP_CLI;
        $this->settings = Settings::get_instance([
            'key'         => 'autocampaign',
            'auto_commit' => true,
            'debug'       => false,
            'store'       => 'options',
        ]);

        $args = Settings::get_instance()->get_role_map();
        new RoleManagement($args);
        Frontend::get_instance();
        Shortcode::get_instance();
        
        if (is_admin() || empty($_GET['frontend']) || $_GET['frontend'] != true) {
            Admin::get_instance();
            Layouts::get_instance();
        }
        add_action('init', [$this, 'init'], 10);

        /**
         * Register all REST Endpoint
         */
        REST::get_instance();
    }
    /**
     * The Plugin Activator
     * @return void
     */
    public function activator(){
        // nx_activated
		if( ! static::$WP_CLI && current_user_can( 'delete_users' ) ) {
			set_transient( 'nx_activated', true, 30 );
		}
        $this->create_wishlist_page();
        
    }

    public function create_wishlist_page()
    {
        $page_title = 'Wishlist';
        $page_slug  = 'wishlist';
    
        // Check if the page already exists
        $page = get_page_by_path($page_slug);
    
        if (!$page) {
            // Create the wishlist page
            $page_id = wp_insert_post([
                'post_title'     => $page_title,
                'post_name'      => $page_slug,
                'post_content'   => '', // Content will be generated via shortcode
                'post_status'    => 'publish',
                'post_type'      => 'page',
                'post_author'    => 1, // Admin user
                'comment_status' => 'closed',
                'meta_input'     => ['_wp_page_template' => 'wishlist-template.php']
            ]);
    
            if ($page_id) {
                update_post_meta($page_id, '_wp_page_template', 'wishlist-template.php');
            }
        }
    }

    public function init() {
        //  @todo remove
        if(defined('AC_DEBUG') && AC_DEBUG){
            add_action('wp_ajax_ac', [$this, 'get_tabs']); // executed when logged in
        }

    }

    /**
     * Checks whether pro plugin is active.
     *
     * @return boolean
     */
    public static function is_pro() {
        return class_exists('\AutoCampaignPro\AutoCampaign');
    }
    /**
     * Get Tabs array in json.
     *
     * @return json
     */
    public function get_tabs() {
        $tabs = GlobalFields::get_instance()->tabs();
        wp_send_json($tabs);
    }
    
    /**
     * Convert `fields` associative array to numeric array recursively.
     * @todo improve implementation.
     *
     * @param array $arr
     * @return array
     */
    public function normalize($arr) {

        if (!empty($arr['fields'])) {
            $arr['fields'] = array_values($arr['fields']);
        }

        if (!empty($arr['options'])) {
            $arr['options'] = array_values($arr['options']);
        }

        if (!empty($arr['tabs'])) {
            $arr['tabs'] = array_values($arr['tabs']);
        }

        if (is_array($arr)) {
            foreach ($arr as $key => $value) {
                if (is_array($value)) {
                    $arr[$key] = $this->normalize($value);
                }
            }
        }
        return $arr;
    }

    public function normalize_post($settings) {
        $fields = $this->get_field_names();
        foreach ($fields as $key => $value) {
            if (!isset($settings[$key]) && isset($value['default'])) {
                $settings[$key] = $value['default'];
            }
            if (isset( $value['type'] ) && ($value['type'] == 'checkbox' || $value['type'] == 'toggle')) {
                $settings[$key] = (bool) (isset($settings[$key]) ? $settings[$key] : false);
            }
            if (isset( $value['type'] ) && $value['type'] == 'number') {
                $settings[$key] = isset($settings[$key]) ? $settings[$key] : 0;
                $settings[$key] = is_numeric($settings[$key]) ? $settings[$key] + 0 : 0;
            }
        }
        return $settings;
    }

    public function get_tab(){
        if(empty($this->tabs)){
            $this->tabs = GlobalFields::get_instance()->tabs();
        }
        return $this->tabs;
    }

    public function get_field_names(){
        $fields = [];
        $tabs = $this->get_tab();
        if(!empty($tabs['tabs'])){
            $fields = $this->_get_field_names($tabs['tabs']);
        }
        return $fields;
    }

    // @todo maybe remove if not used in future.
    public function _get_field_names($fields, $names = []) {
        foreach ($fields as $key => $field) {
            if (empty($field['type'])) {
                $names = $this->_get_field_names($field['fields'], $names);
            } else if ($field['type'] == 'section' || $field['type'] == 'group') { //
                $_names = $this->_get_field_names($field['fields'], []);
                if ($field['type'] == 'section')
                    $names = array_merge($names, $_names);
                else
                foreach ($_names as $key => $value) {
                    $names[$field['name']][$key] = [
                        'type'         => $value['type'],
                        'default'      => isset($value['default']) ? $value['default'] : '',
                        'help'         => isset($value['help']) ? $value['help'] : '',
                        'label'        => isset($value['label']) ? $value['label'] : '',
                        'parent_label' => isset($field['label']) ? $field['label'] : '',
                    ];
                }
            } elseif (!empty($field['name'])) {
                $names[$field['name']] = [
                    'type'    => $field['type'],
                    'default' => isset($field['default']) ? $field['default'] : '',
                    'help'    => isset($field['help']) ? $field['help'] : '',
                    'label'   => isset($field['label']) ? $field['label'] : '',
                ];
            }
        }

        return $names;
    }
}
