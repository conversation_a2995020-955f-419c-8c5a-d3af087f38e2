<?php
/**
 * Admin Class File.
 *
 * @package AutoCampaign\Admin
 */

namespace AutoCampaign\Admin;

use AutoCampaign\Core\Helper;
use AutoCampaign\AutoCampaign;
use AutoCampaign\GetInstance;
use AutoCampaign\Extensions\GlobalFields;

/**
 * Admin Class, this class is responsible for all Admin Actions
 */
class Admin {
    /**
     * Instance of Admin
     *
     * @var Admin
     */
    use GetInstance;
    /**
     * Assets Path and URL
     */
    const ASSET_URL  = AC_ASSETS . 'admin/';
    const ASSET_PATH = AC_ASSETS_PATH . 'admin/';
    const VIEWS_PATH = AC_INCLUDES . 'Admin/views/';

    /**
     * Initially Invoked
     * when its initialized.
     */
    public function __construct(){
        add_action('init', [$this, 'init'], 5);
    }

    /**
     * This method is responsible for Admin Menu of
     * AutoCampaign
     *
     * @return void
     */
    public function init(){
        add_action('admin_menu', [$this, 'menu'], 10);
        Settings::get_instance()->init();
        add_filter('autocampaign_product_lists', [$this, 'products']);
        add_action('admin_enqueue_scripts', [$this, 'admin_enqueue_scripts']);
        add_filter('template_include', [$this, 'sw_load_custom_template']);
        add_filter('theme_page_templates', [$this, 'sw_register_template']);
    }

    public function sw_register_template($templates) {
        $templates['wishlist-template.php'] = 'Wishlist Page';
        return $templates;
    }

    public function sw_load_custom_template($template) {
        global $post;
        if ($post && get_page_template_slug($post->ID) === 'wishlist-template.php') {
            $template = plugin_dir_path(__FILE__) . 'templates/wishlist-template.php';
        }
        return $template;
    }

     /**
     * Register scripts and styles.
     *
     * @param string $hook
     * @return void
     */
    function admin_enqueue_scripts($hook) {
        if ($hook !== "toplevel_page_simple_wishlist-dashboard" && $hook !== "simple_wishlist_page_nx-edit" && $hook !== "simple_wishlist_page_simple_wishlist-settings" && $hook !== "simple_wishlist_page_nx-analytics" && $hook !== "simple_wishlist_page_nx-builder") {
            return;
        }
        // @todo not sure why did it. maybe remove.
        wp_enqueue_media();

        $tabs = $this->get_localize_scripts();

        $d = include Helper::file('admin/js/admin.asset.php');

        wp_enqueue_script(
            'simple_wishlist-admin',
            Helper::file( 'admin/js/admin.js', true ),
            $d['dependencies'],
            $d['version'],
            true
        );
        wp_localize_script('simple_wishlist-admin', 'simple_wishlistTabs', $tabs);
        wp_enqueue_style( 'simple_wishlist-admin', Helper::file( 'admin/css/admin.css', true ), [], $d['version'], 'all' );
        wp_enqueue_style(
            'google-font-inter', 
            'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap', 
            [], 
            null
        );
        wp_set_script_translations( 'simple_wishlist-admin', 'simple_wishlist' );
        do_action('simple_wishlist_admin_scripts');

    }

    public function get_localize_scripts(){
        $tabs                                 = SimpleWishlist::get_instance()->normalize( GlobalFields::get_instance()->tabs() );
        $tabs['createRedirect']               = !current_user_can( 'edit_simple_wishlist' );
        $tabs['analyticsRedirect']            = !(current_user_can( 'read_simple_wishlist_analytics' ) && Settings::get_instance()->get('settings.enable_analytics', true));
        // $tabs['rest']                         = REST::get_instance()->rest_data();
        $tabs['current_page']                 = 'add-rb';
        $tabs['settings']                     = Settings::get_instance()->get_form_data();
        $tabs['settings']['settingsRedirect'] = !current_user_can( 'edit_simple_wishlist_settings' );
        // $tabs['settings']['analytics']        = $tabs['analytics'];
        $tabs['admin_url']                    = get_admin_url();
        $tabs['assets']                       = [
            'admin'  => SW_ADMIN_URL,
            'public' => SW_PUBLIC_URL,
        ];

        $tabs = apply_filters('nx_builder_configs', $tabs);
        return $tabs;
    }

    /**
     * This method is responsible for Admin Menu of
     * SimpleWishlist
     *
     * @return void
     */
    public function menu(){
        add_menu_page(
            __('Simple Wishlist', 'simple_wishlist'),
            __('Simple Wishlist', 'simple_wishlist'),
            'read_simple_wishlist', // User Permission.
            'simple_wishlist-dashboard',
            array( $this, 'views' ),
            self::ASSET_URL . 'images/logo-icon.svg',
            80
        );
    }

    /**
     * Admin Views
     *
     * @return void
     */
    public function views() {
        include_once Admin::VIEWS_PATH . 'main.views.php';
    }
    /**
     * Get File Modification Time or URL
     *
     * @param string $file  File relative path for Admin
     * @param boolean $url  true for URL return
     * @return void|string|integer
     */
    public function file( $file, $url = false ){
        if( $url ) {
            return self::ASSET_URL . $file;
        }
        return filemtime( self::ASSET_PATH . $file );
    }

}
