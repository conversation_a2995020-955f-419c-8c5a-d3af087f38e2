{"name": "autocampaign", "version": "2.0.0", "main": "nxdev/index.js", "author": "<PERSON>", "scripts": {"start": "wp-scripts start", "build": "wp-scripts build", "cs": "WP_NO_EXTERNALS=1 wp-scripts start --config webpack.cs.config.js", "csb": "WP_NO_EXTERNALS=1 wp-scripts build --config webpack.cs.config.js", "pot": "wp i18n make-pot . languages/simple_wishlist.pot "}, "plugins": ["@babel/plugin-transform-runtime", "@babel/plugin-proposal-class-properties"], "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.25.9", "@babel/plugin-transform-runtime": "^7.25.9", "@babel/preset-typescript": "^7.26.0", "@babel/runtime": "^7.26.0", "@babel/runtime-corejs3": "^7.26.0", "@types/react": "^19.0.2", "@types/react-router-dom": "^5.3.3", "@wordpress/api-fetch": "^7.14.0", "@wordpress/components": "^29.0.0", "@wordpress/date": "^5.14.0", "@wordpress/dom-ready": "^4.14.0", "@wordpress/hooks": "^4.14.0", "@wordpress/i18n": "^5.14.0", "@wordpress/scripts": "^30.7.0", "moment": "^2.30.1", "react": "^19.0.0", "react-dom": "^19.0.0", "ts-loader": "^9.5.1", "typescript": "^5.7.1", "url-loader": "^4.1.1"}, "dependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "babel-loader": "^9.2.1", "classnames": "^2.3.1", "html-react-parser": "^1.3.0", "lodash": "^4.17.21", "quickbuilder": "github:WPDevelopers/quickbuilder#master", "rc-pagination": "^3.1.9", "react-cookies": "^0.1.1", "react-datepicker": "^6.3.0", "react-router-dom": "^5.3.3", "react-select": "^5.9.0", "react-table": "^7.8.0", "react-toastify": "^11.0.2", "sort-array": "^5.0.0", "sweetalert2": "^11.15.3"}}