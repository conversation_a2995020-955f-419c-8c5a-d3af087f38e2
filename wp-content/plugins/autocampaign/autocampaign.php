<?php
/**
 * Plugin Name:       AutoCampaign
 * Plugin URI:        https://autocampaign.com
 * Description:       Social Proof & Recent Sales Popup, Comment Notification, Subscription Notification, Notification Bar and many more.
 * Version:           1.0.0
 * Author:            MD Shakibul Islam
 * Author URI:        https://github.com/shuvo7670
 * License:           GPL-3.0+
 * License URI:       https://www.gnu.org/licenses/gpl-3.0.html
 * Text Domain:       autocampaign
 * Domain Path:       /languages
 *
 * @package           AutoCampaign
 * @link              https://github.com/shuvo7670
 * @since             1.0.0
 */

/**
 * If this file is called directly, abort.
 */
if ( ! defined( 'WPINC' ) ) {
	die;
}
/**
 * Defines CONSTANTS for Whole plugins.
 */
define( 'AC_FILE', __FILE__ );
define( 'AC_VERSION', '1.0.0' );
define( 'AC_URL', plugins_url( '/', __FILE__ ) );
define( 'AC_PATH', plugin_dir_path( __FILE__ ) );
define( 'AC_BASENAME', plugin_basename( __FILE__ ) );

define( 'AC_ASSETS', AC_URL . 'assets/' );
define( 'AC_ASSETS_PATH', AC_PATH . 'assets/' );
define( 'AC_DEV_ASSETS', AC_URL . 'build/' );
define( 'AC_DEV_ASSETS_PATH', AC_PATH . 'build/' );
define( 'AC_INCLUDES', AC_PATH . 'includes/' );


define( 'AC_TEXTDOMAIN', 'autocampaign' );
define( 'AC_PLUGIN_URL', 'https://autocampaign.com' );
define( 'AC_ADMIN_URL', AC_ASSETS . 'admin/' );
define( 'AC_PUBLIC_URL', AC_ASSETS . 'public/' );

/**
 * The Core Engine of the Plugin
 */
if ( ! class_exists( '\AutoCampaign\AutoCampaign' ) ) {
    require_once AC_PATH . 'vendor/autoload.php';
    if(ac_is_plugin_active( 'autocampaign-pro/autocampaign-pro.php' )){
        add_action( 'admin_notices', 'ac_free_compatibility_notice' );
        if( file_exists(dirname(AC_PATH) . '/autocampaign-pro/vendor/autoload.php') ) {
            require_once dirname(AC_PATH) . '/autocampaign-pro/vendor/autoload.php';
        } else {
            add_action('plugins_loaded', function(){
                remove_action( 'admin_notices', 'autocampaign_install_core_notice' );
            });
        }
    }

    function activate_autocampaign() {
        \AutoCampaign\AutoCampaign::get_instance()->activator();
    }
    /**
     * Plugin Activator
     */
    register_activation_hook( AC_FILE, 'activate_autocampaign' );
    \AutoCampaign\AutoCampaign::get_instance();
}

function ac_free_compatibility_notice(){
    if ( ! function_exists( 'get_plugins' ) ) {
        require_once ABSPATH . 'wp-admin/includes/plugin.php';
    }
    $plugins = get_plugins();
    if( isset( $plugins['autocampaign-pro/autocampaign-pro.php'], $plugins['autocampaign-pro/autocampaign-pro.php']['Version'] ) && version_compare( $plugins['autocampaign-pro/autocampaign-pro.php']['Version'], '2.1.0', '>=' ) ) {
        return;
    }
    ?>
        <div class="notice notice-warning is-dismissible">
            <p>
            <?php echo sprintf(__("<strong>Recommended: </strong> Seems like you haven't updated the AutoCampaign Pro version. Please make sure to update AutoCampaign Pro plugin from <a href='%s'><strong>wp-admin -> Plugins</strong></a>.", 'autocampaign' ), esc_url( admin_url('plugins.php' )));?></p>
        </div>
    <?php
}


function ac_is_plugin_active( $plugin ) {
    return in_array( $plugin, (array) get_option( 'active_plugins', array() ), true ) || ac_is_plugin_active_for_network( $plugin );
}


function ac_is_plugin_active_for_network( $plugin ) {
    if ( ! is_multisite() ) {
        return false;
    }

    $plugins = get_site_option( 'active_sitewide_plugins' );
    if ( isset( $plugins[ $plugin ] ) ) {
        return true;
    }

    return false;
}