
#simple_wishlist {
    font-family: "Inter";
     .wp-react-form.wprf-tabs-wrapper.wprf-tab-menu-as-sidebar, .wp-react-form.wprf-tabs-wrapper  {
        margin: 0;
    }
     .wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper .wprf-tab-nav .wprf-tab-nav-item {
        border-bottom: 2px solid #fff;
        border-top: none;
        background-color: #fff;
        color: #000000;
        display: flex;
        justify-content: start;
        align-items: center;
        gap: 10px;
    }
    
    .wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper .wprf-tab-nav .wprf-tab-nav-item.main-menu {
        justify-content: center;
        &.wprf-active-nav {
            background-color: #f3efff;
        }
    }

     .wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper .wprf-tab-nav .wprf-tab-nav-item.wprf-active-nav {
        border-bottom: 2px solid var(--simple_wishlist-primary-color);
        color: var(--simple_wishlist-primary-color);
        cursor: pointer;
        background-color: #f3efff;
        svg path {
            fill: var(--simple_wishlist-primary-color);
        }
    }
    .wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper .wprf-tab-nav {
       border-bottom: 1px solid  var(--simple_wishlist-border-color);
       background-color: #fff;
    }
    .wp-react-form.wprf-tabs-wrapper .wprf-tab-menu-wrapper .wprf-tab-nav .wprf-tab-nav-item {
        font-size: 0.875rem;
        cursor: pointer;
    }
    .wp-react-form.wprf-tabs-wrapper .wprf-tab-content-wrapper {
        width: 70%;
        margin: 3rem auto;
        border-radius: 5px;
        padding: 0;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
        .wprf-tab-content-wrapper {
            padding: 1.25rem;
            box-shadow: none !important;
            margin: 0;
        }
    }
    .wprf-tab-content {
        min-height: 55vh;
    }
    .wprf-tab-content.wprf-tab-sw_general_tab > .wprf-tab-heading-wrapper {
        border-bottom: 1px solid var(--simple_wishlist-border-color);
        padding: 20px;
        h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
            color: #000000;
        }
    }
    .wp-react-form .wp-react-form {
        flex-direction: row;
        .wprf-tab-nav {
            flex-direction: column;
            border: none !important;
        }
        .wprf-tab-nav .wprf-tab-nav-item {
            border-left: 4px solid #fff;
            border-bottom: none !important;
            font-size: 14px !important;
        }
        &.wprf-tabs-wrapper .wprf-tab-menu-wrapper {
            flex-basis: 25%;
            border-right: 1px solid var(--simple_wishlist-border-color);
            padding-top: 10px;
        }
        .wprf-tab-nav .wprf-tab-nav-item.wprf-active-nav {
            border-left: 4px solid var(--simple_wishlist-primary-color);
            color: var(--simple_wishlist-primary-color);
            cursor: pointer;
        }
        .wprf-tab-content > .wprf-tab-heading-wrapper {
            display: none;
        }
        .wprf-control-wrapper {
            align-items: start !important;
            .wprf-control-label {
                flex-basis: 50%;
                font-size: 14px;
                font-weight: 400;
                line-height: 1.2;
                margin-bottom: 5px;
                margin-top: 5px;
                color: #1e293b;
            }
            .wprf-control-field {
                flex-basis: 50%;
            }
        }
        .wprf-control-wrapper .wprf-control-field input {
            color: #575a5d;
            padding: 8px 15px;
            min-width: 100%;
            height: 36px;
            font-size: 14px;
            border-radius: 2px;
            border: solid 1px rgba(147, 42, 30, .08);
            background-color: #fff;
        }
        .wprf-control-wrapper .wprf-control-field .wprf-select-wrapper {
            width: 100%;
        }
        .wprf-control-wrapper .wprf-control-field textarea {
            border: solid 1px rgba(147, 42, 30, .08);
            font-size: 14px;
            border-radius: 2px;
            width: 100%;
        }
    }
    .wprf-control.wprf-submit {
        justify-content: end;
        align-items: center;
        position: absolute;
        width: 70%;
        display: flex;
    }
    .wprf-control.wprf-submit {
        display: flex;
        justify-content: end;
        align-items: center;
        button {
            margin: 0 30px 30px 0;
            cursor: pointer;
            padding: 15px 30px;
            display: inline-flex;
            align-items: center;
            font-size: 14px;
            font-weight: 600;
            background-color: var(--simple_wishlist-primary-color);
            border-radius: 3px;
            color: #fff;
        }
    }
    .wprf-toggle-wrap.wprf-checked:before, .wprf-toggle-wrap input:checked + label:before {
        background-color: var(--simple_wishlist-primary-color) !important;
    }
}
