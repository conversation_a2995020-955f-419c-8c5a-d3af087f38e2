.wprf-control {
    .wprf-row {
        margin: -15px;
        @media only screen and (max-width: 1399px) {
            margin: -10px;
        }
        .wprf-column {
            padding: 15px;
            @media only screen and (max-width: 1399px) {
                padding: 10px;
            }
        }
        .wprf-column-3 {
            @media only screen and (max-width: 1399px) {
                width: 33.333333%;
            }
            @media only screen and (max-width: 782px) {
                width: 50%;
            }
            @media only screen and (max-width: 575px) {
                width: 100%;
            }
        }
        .wprf-column-4 {
            @media only screen and (max-width: 1279px) {
                width: 50%;
            }
            @media only screen and (max-width: 575px) {
                width: 100%;
            }
        }
    }
}
.wprf-button {
    display: inline-flex;
    width: initial;
    min-height: 40px;
    background: transparent;
    border: 1.5px solid #16453e;
    border-radius: 4px;
    padding: 2px 25px;
    font-size: 16px;
    font-weight: 500;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    outline: none;
    background: #16453e;
    color: #fff;
    line-height: 1.15;
    transition: 0.3s ease-in-out;
    box-sizing: border-box;
    cursor: pointer;
    &:hover,
    &:focus {
        color: #fff;
        background-color: darken(#5614d5, 1%);
        border-color: darken(#5614d5, 1%);
        outline: none;
        box-shadow: 0 15px 25px -5px rgba(#5614d5, 0.3);
    }
}
.wprf-control.wprf-radio-card {
    .wprf-input-radio-option {
        margin-top: 0;
        border-radius: 10px;
        box-shadow: none;
        display: flex;
        flex-direction: column;
        min-height: 84px;
        height: 100%;
        .wprf-input-label {
            background-color: #f6f7fe;
            border: 2px solid rgba(#0ccfc2, 0.05);
            font-size: 18px;
            font-weight: 500;
            color: #25396f;
            border-radius: 10px;
            flex-grow: 1;
            padding: 5px;
            line-height: 1.3;
            text-align: center;
            @media only screen and (max-width: 1599px) {
                font-size: 14px;
            }
            .wprf-label-image {
                max-height: 80px;
            }
            .wprf-badge {
                .wprf-badge-item {
                    height: 24px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    line-height: 1;
                    background: #16453e;
                    color: #fff;
                    top: 5px;
                    left: -32px;
                    box-shadow: none;
                }
            }
        }
        &.wprf-label-has-image {
            padding: 15px;
        }
        &.wprf-option-selected {
            .wprf-input-label:not(.wprf-label-has-image) {
                background-color: #16453e;
                color: #fff;
                .wprf-badge {
                    .wprf-badge-item {
                        background: #f6f7fe;
                        color: #16453e;
                    }
                }
            }
            &:before {
                top: 10px;
                right: 10px;
                background: #fff;
            }
            &:after {
                border-bottom: 2px solid #16453e;
                border-right: 2px solid #16453e;
                height: 8px;
                width: 3px;
                right: 17.5px;
                top: 13.5px;
            }
            &.wprf-option-has-image {
                &:before {
                    background: #45c9b1;
                }
                &:after {
                    border-bottom: 2px solid #fff;
                    border-right: 2px solid #fff;
                }
            }
        }
    }
}

.wprf-image-uploader {
    margin-bottom: -5px;
    > * {
        margin-bottom: 5px;
    }
}

.wprf-control-wrapper {
    align-items: flex-start;
    @media only screen and (max-width: 700px) {
        flex-direction: column;
    }
    &:not(.wprf-style-card) > .wprf-badge-wrapper {
        display: flex;
        @media only screen and (max-width: 700px) {
            flex-direction: column;
            align-items: flex-start;
        }
        .wprf-control-label {
            display: inline-flex;
            align-items: flex-start;
        }
        &.pro-deactivated {
            .wprf-control-field {
                opacity: 0.6;
                filter: blur(0.5px);
            }
            .wprf-description {
                display: none;
            }
            .wprf-toggle-wrap {
                opacity: 0.6;
            }
            input[type="checkbox"] {
                background: #7c8db5;
                border-color: transparent;
                box-shadow: none;
                opacity: 0.6;
            }
        }
        .wprf-badge {
            // position: absolute;
            background: #5614d5;
            color: #fff;
            display: inline-flex;
            height: 13px;
            min-width: 25px;
            width: 25px;
            align-items: center;
            justify-content: center;
            border-radius: 2px;
            margin-left: 5px;
            .wprf-badge-item {
                display: inline-flex;
                font-size: 9px;
                line-height: 1;
                text-transform: uppercase;
            }
        }
    }
    &.wprf-inline-label {
        .wprf-control-label {
            flex: 30%;
            min-height: 20px;
            margin-top: 10px;
            margin-bottom: 10px;
            padding-right: 10px;
            box-sizing: border-box;
            @media only screen and (max-width: 1599px) {
                flex: 20%;
            }
            @media only screen and (max-width: 700px) {
                margin-top: 0px;
                flex: 100%;
            }
            label {
                font-size: 15px;
                font-weight: 500;
                color: #7c8db5;
                line-height: 1.2;
                margin-top: 5px;
                margin-bottom: 5px;
                letter-spacing: -0.03px;
                @media only screen and (max-width: 1599px) {
                    font-size: 13px;
                }
            }
        }
        .wprf-control-field {
            min-height: 40px;
            flex: 70%;
            .wprf-async-select-wrapper {
                width: 100%;
            }
            // .wprf-type-textarea {
                textarea {
                    padding: 10px 15px;
                    border: 1.5px solid #e6effb;
                    border-radius: 5px;
                    color: #25396f;
                    font-size: 14px;
                    font-weight: 500;
                    width: 100%;
                    // max-width: 400px;
                    margin-right: 10px;
                    @media only screen and (max-width: 1599px) {
                        padding: 8px 10px;
                    }
                    &::placeholder {
                        color: #a7b6d8;
                    }
                    &:focus {
                        border-color: #7c8db5;
                        outline: none;
                        box-shadow: none;
                    }
                }
                textarea.readonly,
                textarea[readonly] {
                    background: rgba(#16453e, 0.1);
                }
            // }
            input[type="number"],
            input[type="text"] {
                min-height: 40px;
                padding: 2px 15px;
                border: 1.5px solid #e6effb;
                border-radius: 5px;
                color: #25396f;
                font-size: 14px;
                font-weight: 500;
                width: 100%;
                max-width: 400px;
                margin-right: 10px;
                @media only screen and (max-width: 1599px) {
                    padding: 2px 10px;
                }
                &::placeholder {
                    color: #a7b6d8;
                }
                &:focus {
                    border-color: #7c8db5;
                    outline: none;
                    box-shadow: none;
                }
            }
            input[type="number"] {
                max-width: 150px;
                padding-right: 5px;
            }
            input.readonly,
            input[readonly] {
                background: rgba(#16453e, 0.1);
            }
            .wprf-colorpicker-wrap {
                .wprf-picker-display {
                    margin-top: 3px;
                    margin-bottom: 0;
                    box-shadow: 0 0 2px rgba(#000, 0.3);
                }
                .wprf-colorpicker-reset {
                    bottom: 8px;
                    display: inline-flex;
                    width: initial;
                    min-height: 25px;
                    background: transparent;
                    border: 1.5px solid #16453e;
                    border-radius: 4px;
                    padding: 2px 15px;
                    font-size: 13px;
                    font-weight: 500;
                    align-items: center;
                    justify-content: center;
                    text-decoration: none;
                    outline: none;
                    background: #16453e;
                    color: #fff;
                    line-height: 1.15;
                    transition: 0.3s ease-in-out;
                    box-sizing: border-box;
                    &:hover,
                    &:focus {
                        color: #fff;
                        background-color: darken(#5614d5, 1%);
                        border-color: darken(#5614d5, 1%);
                        outline: none;
                        box-shadow: 0 15px 25px -5px rgba(#5614d5, 0.3);
                    }
                }
                .components-color-picker__inputs-toggle-wrapper {
                    .components-button {
                        min-height: 40px;
                        cursor: pointer;
                    }
                }
            }
            .wprf-select-wrapper {
                width: 100%;
                max-width: 400px;
                min-width: 170px;
                margin-right: 10px;
                .wprf-select__control {
                    min-height: 40px;
                    padding: 2px 0px 2px 15px;
                    border: 1.5px solid #e6effb;
                    border-radius: 5px;
                    color: #25396f;
                    font-size: 14px;
                    font-weight: 500;
                    @media only screen and (max-width: 1599px) {
                        padding: 2px 0px 2px 10px;
                    }
                    .wprf-select__value-container {
                        padding-left: 0px;
                        .wprf-select__single-value {
                            color: #25396f;
                            margin-left: 0px;
                        }
                        .wprf-select__multi-value {
                            background: rgba(#16453e, 0.1);
                            border: 1px solid rgba(#16453e, 0.02);
                            border-radius: 30px;
                            padding-left: 3px;
                            padding-right: 2px;
                            .wprf-select__multi-value__label {
                                color: #16453e;
                            }
                            .wprf-select__multi-value__remove {
                                svg {
                                    fill: #16453e;
                                }
                                &:hover {
                                    background: transparent;
                                    cursor: pointer;
                                    svg {
                                        fill: red;
                                    }
                                }
                            }
                        }
                        &.wprf-select__value-container--is-multi {
                            margin-left: -4px;
                        }
                    }

                    .wprf-select__indicators {
                        .wprf-select__indicator-separator {
                            display: none;
                        }
                        .wprf-select__indicator {
                            padding-top: 2px;
                            padding-bottom: 2px;
                            svg {
                                color: #9ca6bc;
                            }
                            &.wprf-select__clear-indicator {
                                padding-left: 0;
                                padding-right: 0;
                            }
                        }
                    }
                    &:focus-within {
                        border-color: #7c8db5;
                        outline: none;
                        box-shadow: none;
                    }
                }
            }
            input[type="checkbox"] {
                margin-left: 0;
                width: 30px;
                min-width: 30px;
                height: 30px;
                border-width: 2px;
                border-radius: 5px;
                border-color: #e6effb;
                margin-right: 10px;
                &:before {
                    width: 24px;
                    margin: 0;
                    height: 27px;
                    margin-top: 1px;
                }
                &:focus {
                    border-color: #7c8db5;
                    box-shadow: none;
                    outline: none;
                }
            }
            > input[type="checkbox"] {
                margin-top: 5px;
                margin-bottom: 5px;
            }
            .wprf-toggle-wrap {
                width: 40px;
                height: 20px;
                margin-right: 10px;
                label {
                    width: 40px;
                    height: 20px;
                    min-height: 20px;
                    position: relative;
                    cursor: pointer;
                    display: flex;
                    font-size: 24px;
                    &:before,
                    &:after {
                        content: "";
                        position: absolute;
                    }
                    &:before {
                        width: 40px;
                        height: 20px;
                        left: 0;
                        transition: background 0.1s 0.1s ease;
                        background: #7c8db5;
                        border: 1px solid #7c8db5;
                        border-radius: 50px;
                        margin: 0;
                        box-sizing: border-box;
                    }
                    &:after {
                        width: 18px;
                        height: 18px;
                        border-radius: 50px;
                        left: 1px;
                        top: 1px;
                        transition: all 0.2s ease;
                        box-shadow: 0 0 0 5px #fcfff4 inset;
                        background: #fff;
                        // z-index: 2;
                        animation: switch-off 0.3s ease-out;
                    }
                }
                input[type="checkbox"] {
                    visibility: hidden;
                    display: none;
                    &:checked + label::before {
                        background: #16453e;
                        border: 1px solid #16453e;
                    }
                    &:checked + label::after {
                        left: 21px;
                        background: #ffffff;
                        animation: switch-on 0.3s ease-out;
                    }
                }
                &.wprf-checked {
                    label::before {
                        background: #16453e;
                        border: 1px solid #16453e;
                    }
                    label::after {
                        left: 21px;
                        background: #ffffff;
                        animation: switch-on 0.3s ease-out;
                    }
                }
            }
            .wprf-help,
            .wprf-description {
                font-size: 14px;
                font-weight: 400;
                margin-top: 8px;
                margin-bottom: 8px;
                color: #7c8db5;
                margin-left: 0;
                line-height: 24px;
                margin-right: 10px;
                mark {
                    color: #16453e;
                    background-color: transparent;
                }
                a {
                    text-decoration: underline;
                    color: #16453e;
                }
            }
            .wprf-group-control {
                .wprf-group-control-inner {
                    display: flex;
                    width: 100%;
                    flex-wrap: wrap;
                    &.wprf-display-inline {
                        margin-bottom: -10px;
                        > * {
                            width: 180px;
                            max-width: 180px;
                            min-width: 180px;
                            margin-bottom: 10px;
                        }
                    }
                    .wprf-control-wrapper.wprf-type-number.wprf-inline-label {
                        flex: 1;
                        margin-bottom: 5px !important;
                        align-items: center;
                        .wprf-control-label {
                            margin-top: 0;
                            flex: 0 0 100%;
                            margin-bottom: 5px;
                        }
                    }
                }
            }
            .wprf-image-preview {
                img {
                    border-radius: 5px;
                }
            }
            .wprf-image-uploader {
                .wprf-image-remove-btn {
                    background: #f85656;
                    border-radius: 3px;
                    box-sizing: border-box;
                    min-height: 36px;
                    transition: 0.3s ease-in-out;
                    &:hover {
                        background: darken(#f85656, 3%);
                        box-shadow: 0 15px 25px -5px rgba(#f85656, 0.3);
                    }
                }
                .wprf-image-upload-btn {
                    background: transparent;
                    border: 1px solid #16453e;
                    color: #16453e;
                    border-radius: 3px;
                    box-sizing: border-box;
                    min-height: 36px;
                    transition: 0.3s ease-in-out;
                    &:hover {
                        background: #16453e;
                        color: #fff;
                        box-shadow: 0 15px 25px -5px rgba(#5614d5, 0.3);
                    }
                }
            }
        }
    }
    &.wprf-style-card {
        background-color: #f6f7fe;
        border: 2px solid rgba(#0ccfc2, 0.05);
        font-size: 18px;
        font-weight: 500;
        color: #25396f;
        border-radius: 10px;
        flex-grow: 1;
        min-height: 80px;
        box-sizing: border-box;
        display: flex;
        flex-direction: row !important;
        align-items: center;
        justify-content: space-between;
        padding: 5px 20px !important;
        height: 100%;
        position: relative;
        @media only screen and (max-width: 1599px) {
            padding: 5px 15px;
        }
        .wprf-badge-wrapper {
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            height: 100%;
            z-index: 1;
        }
        .wprf-badge {
            position: absolute;
            width: 100%;
            height: 100%;
            top: -2px;
            left: -2px;
            overflow: hidden;
            z-index: -1;
            .wprf-badge-item {
                height: 24px;
                display: flex;
                justify-content: center;
                align-items: center;
                line-height: 1;
                background: #16453e;
                color: #fff;
                top: 5px;
                left: -32px;
                box-shadow: none;
                font-size: 14px;
                text-transform: uppercase;
                text-align: center;
                transform: rotate(-45deg);
                position: absolute;
                width: 100px;
                z-index: 9;
            }
        }
        .wprf-control-label {
            flex: 1;
            margin-right: 10px;
            margin-top: 5px;
            margin-bottom: 5px;
            label {
                font-size: 16px;
                font-weight: 600;
                line-height: 1;
                color: #25396f;
                @media only screen and (max-width: 1599px) {
                    font-size: 16px;
                }
            }
            & > a {
                border: 1px solid #000;
                border-radius: 50%;
                width: 10px;
                height: 10px;
                display: inline-block;
                text-align: center;
                line-height: 10px;
                margin-left: 10px;
                opacity: 0.3;
                transition: opacity 0.3s ease 0s;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                svg {
                    width: 6px;
                    height: 6px;
                }
            }
        }
        .wprf-control-field {
            flex: 0;
            margin-right: auto;
            .wprf-toggle-wrap {
                margin-right: 0;
            }
        }
    }
}
.wprf-code-viewer {
    width: 100%;
    max-width: 700px;
}
.wprf-reporting_monthly_help_text-message {
    padding-left: 30%;
    font-size: 14px;
    font-weight: 400;
    color: #7c8db5;
    line-height: 24px;
    margin-right: 10px;
    margin-top: -15px;
    @media only screen and (max-width: 1399px) {
        padding-left: 25%;
    }
    @media only screen and (max-width: 700px) {
        padding-left: 0;
    }
    p {
        margin-top: 0;
        margin-bottom: 0;
        font-size: 14px;
    }
    mark {
        color: #16453e;
        background-color: transparent;
    }
    a {
        text-decoration: underline;
        color: #16453e;
    }
}
.wprf-repeater-label {
    .wprf-repeater-button {
        margin-top: 15px;
        display: inline-flex;
        width: initial;
        min-height: 40px;
        background: transparent;
        border: 1.5px solid #16453e;
        border-radius: 4px;
        padding: 2px 25px;
        font-size: 16px;
        font-weight: 500;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        outline: none;
        background: #16453e;
        color: #fff;
        line-height: 1.15;
        transition: 0.3s ease-in-out;
        box-sizing: border-box;
        cursor: pointer;
        &:hover,
        &:focus {
            color: #fff;
            background-color: darken(#0b3831, 1%);
            border-color: darken(#0b3831, 1%);
            outline: none;
            box-shadow: 0 15px 25px -5px rgba(#0b3831, 0.3);
        }
    }
}

.wprf-message {
    box-sizing: border-box;
    p {
        font-size: 16px;
        a {
            text-decoration: underline;
            color: #16453e;
        }
        .rb-on-click-install {
            display: inline-flex;
            width: initial;
            min-height: 36px;
            background: transparent;
            border: 1.5px solid #16453e;
            border-radius: 4px;
            padding: 2px 25px;
            font-size: 16px;
            font-weight: 500;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            outline: none;
            background: #16453e;
            color: #fff;
            line-height: 1.15;
            transition: 0.3s ease-in-out;
            box-sizing: border-box;
            cursor: pointer;
            &:hover,
            &:focus {
                color: #fff;
                background-color: darken(#0b3831, 1%);
                border-color: darken(#0b3831, 1%);
                outline: none;
                box-shadow: 0 5px 10px -5px rgba(#0b3831, 0.2);
            }
        }
    }
    &.wprf-error-message {
        background: #fff3cd;
        color: #856404;
        border: 2px solid #ffeeba;
        border-radius: 10px;
        padding: 15px 25px;
        p {
            margin-top: 0;
            margin-bottom: 0;
        }
    }
}
.wprf-name-rb-bar_with_elementor_install {
    .wprf-description {
        margin-left: 0px;
    }
    p {
        font-size: 16px;
        a {
            text-decoration: underline;
            color: #16453e;
        }
        .rb-on-click-install {
            display: inline-flex;
            width: initial;
            min-height: 36px;
            background: transparent;
            border: 1.5px solid #16453e;
            border-radius: 4px;
            padding: 2px 25px;
            font-size: 16px;
            font-weight: 500;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            outline: none;
            background: #16453e;
            color: #fff;
            line-height: 1.15;
            transition: 0.3s ease-in-out;
            box-sizing: border-box;
            cursor: pointer;
            &:hover,
            &:focus {
                color: #fff;
                background-color: darken(#5614d5, 1%);
                border-color: darken(#5614d5, 1%);
                outline: none;
                box-shadow: 0 15px 25px -5px rgba(#5614d5, 0.3);
            }
        }
    }
}

.template-options {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-left: -3px;
    margin-right: -3px;

    > * {
        margin-bottom: 5px;
        margin-left: 3px;
        margin-right: 3px;
    }
}

.wprf-editor {
    display: flex;
    flex-direction: column;
    .wprf-editor-main {
        padding-top: 5px;
        padding-bottom: 5px;
        flex-grow: 1;
        .public-DraftStyleDefault-block {
            margin-bottom: 5px;
            margin-top: 5px;
        }
    }
}

.wprf-slider-wrap {
    align-items: flex-start;
    display: flex;
    .wprf-slider-control-head {
        flex: 30%;
        min-height: 20px;
        margin-top: 10px;
        margin-bottom: 10px;
        label {
            font-size: 16px;
            font-weight: 500;
            color: #7c8db5;
            line-height: 1.2;
            margin-top: 5px;
            margin-bottom: 5px;
        }
    }
    .wprf-slider-control {
        min-height: 40px;
        flex: 70%;
        .components-range-control__root {
            .components-range-control__wrapper {
                height: 40px;
                width: calc(100% - 20px);
                max-width: calc(400px - 20px);
                margin: 0 10px;
                .components-range-control__slider {
                    margin: 0;
                    margin-left: -10px;
                    margin-right: 10px;
                    width: 100%;
                    height: 10px;
                    margin-top: 15px;
                    border-radius: 10px;
                }
                .exqw8y24 {
                    background: #e6effb;
                    padding: 2px;
                    height: 10px;
                    margin-top: 15px;
                    border-radius: 10px;
                    margin-left: -10px;
                    margin-right: -10px;
                }
                .components-range-control__track {
                    margin: 0;
                    height: 6px;
                    top: 17px;
                    border-radius: 10px;
                    background-color: #16453e;
                    margin-left: -8px;
                }
                .exqw8y29 {
                    span {
                        margin-top: 10px;
                        border: 2px solid #16453e;
                    }
                }
            }
            .components-input-control {
                width: 100px;
                input[type="number"] {
                    min-height: 36px;
                    // padding: 2px 15px;
                    border: 1.5px solid #e6effb;
                    border-radius: 5px;
                    color: #25396f;
                    font-size: 14px;
                    font-weight: 500;
                    margin-right: 10px;
                    margin-right: 0;
                    &::placeholder {
                        color: #a7b6d8;
                    }
                    &:focus {
                        border-color: #7c8db5;
                        outline: none;
                        box-shadow: none;
                    }
                }
                .components-input-control__backdrop {
                    display: none;
                }
            }
            .components-range-control__reset {
                background: transparent;
                border: 1px solid #16453e;
                color: #16453e;
                border-radius: 3px;
                box-sizing: border-box;
                min-height: 36px;
                transition: 0.3s ease-in-out;
                padding: 2px 20px;
                font-size: 15px;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                &:hover {
                    background: #16453e;
                    color: #fff;
                    box-shadow: 0 15px 25px -5px rgba(#5614d5, 0.3);
                }
            }
        }
    }
}
