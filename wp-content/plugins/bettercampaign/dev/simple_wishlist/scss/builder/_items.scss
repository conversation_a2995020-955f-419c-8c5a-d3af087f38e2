.simple_wishlist-items {
    padding: 0;
    .simple_wishlist-dashboard-menu {
        margin-top: 30px;
        margin-bottom: 0px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        @media only screen and (max-width: 1399px) {
            margin-top: 20px;
        }
        @media only screen and (max-width: 960px) {
            margin-top: 0px;
        }
        & > ul {
            margin-bottom: 0px;
            margin-top: 0;
            &:not(:last-child) {
                margin-right: 10px;
            }
            li {
                display: inline-block;
                a {
                    background: rgba(#6a4bff, 0.05);
                    border: 2px solid rgba(#6a4bff, 0.05);
                    border-radius: 10px;
                    color: #25396f;
                    margin-right: 20px;
                    text-decoration: none;
                    text-transform: uppercase;
                    display: inline-flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 50px;
                    padding: 2px 25px;
                    font-size: 16px;
                    font-weight: 500;
                    box-sizing: border-box;
                    box-shadow: none;
                    transition: 0.3s ease-in-out;
                    outline: none;
                    @media only screen and (max-width: 1599px) {
                        font-size: 12px;
                        min-height: 42px;
                        padding: 3px 15px;
                        border-radius: 10px;
                    }
                    @media only screen and (max-width: 1399px) {
                        font-size: 12px;
                        min-height: 40px;
                        padding: 3px 15px;
                        border-radius: 8px;
                    }
                }
                &.rb-active {
                    a {
                        background: #6a4bff;
                        border-color: #6a4bff;
                        color: #fff;
                    }
                }
                &.rb-empty-trash-btn {
                    a {
                        background-color: #fafafa;
                        color: #dd7474;
                        padding: 5px 10px;
                    }
                }
            }
        }
        .rb-bulk-action-wrapper {
            display: flex;
            flex-wrap: wrap;
            .bulk-action-select {
                height: 40px;
                .components-input-control__container {
                    .components-select-control__input {
                        background: #fff;
                        border: 2px solid #e8e7fe;
                        border-radius: 5px;
                        padding-left: 12px;
                        padding-right: 30px;
                        font-size: 14px;
                        font-weight: 500;
                        outline: none;
                        box-shadow: none;
                        min-width: 150px;
                        color: #555555;
                        height: 40px;
                    }
                    .components-input-control__suffix {
                        > div {
                            right: 5px;
                            svg {
                                fill: #7c8db5;
                            }
                        }
                    }
                    .components-input-control__backdrop {
                        display: none;
                    }
                }
                .bulk-action-select__control {
                    height: 40px;
                    background: #fff;
                    border: 2px solid #e8e7fe;
                    border-radius: 5px;
                    min-width: 175px;
                    .bulk-action-select__value-container {
                        height: 37px;
                        .bulk-action-select__input {
                            input {
                                min-height: 18px;
                                box-shadow: none;
                            }
                        }
                    }
                    .bulk-action-select__indicators {
                        height: 37px;
                        .bulk-action-select__indicator-separator {
                            display: none;
                        }
                    }
                }
            }
            .rb-bulk-action-button {
                background-color: #6a4bff;
                color: #fff;
                border: none;
                padding: 2px 30px;
                font-size: 14px;
                font-weight: 500;
                border-radius: 7px;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 40px;
                box-sizing: border-box;
                transition: 0.3s ease-in-out;
                cursor: pointer;
                &:hover {
                    color: #fff;
                    background-color: #08352f;
                    outline: none;
                    box-shadow: 0 15px 25px -5px rgb(86 20 213 / 30%);
                }
                &:not(:first-child) {
                    margin-left: 10px;
                }
            }
        }
    }
    .simple_wishlist-dashboard-not-found,
    .simple_wishlist-dashboard-not-found p {
        color: #c4434e;
        margin: 0px;
    }
    .simple_wishlist-dashboard-items {
        .rb-list-table-wrapper {
            margin-top: 20px;
            display: block;
            width: 100%;
            overflow-x: auto;
            @media only screen and (max-width: 1599px) {
                margin-top: 10px;
            }
        }
        table {
            border-spacing: 0 5px;
            td {
                padding: 10px 25px;
                font-size: 18px;
                @media only screen and (max-width: 1599px) {
                    font-size: 14px;
                }
            }
            thead {
                background-color: transparent;
                tr {
                    td {
                        vertical-align: middle;
                        border-bottom: none;
                        color: #7c8db5;
                        background-color: #fff;
                        height: 60px;
                        padding: 10px 20px;
                        @media only screen and (max-width: 1599px) {
                            padding: 5px 10px;
                            height: 50px;
                        }
                    }
                    & > td:nth-child(1) {
                        border-top-left-radius: 10px;
                        width: 20px;
                        padding-right: 0;
                        @media only screen and (max-width: 782px) {
                            width: 20px !important;
                        }
                        .rb-all-selector {
                            display: flex;
                            align-items: center;
                            align-self: center;
                            input[type="checkbox"] {
                                margin-left: 0;
                                width: 20px;
                                min-width: 20px;
                                height: 20px;
                                border-color: #e6effb;
                                &:before {
                                    width: 17px;
                                    margin: 1px 0 0;
                                }
                                &:focus {
                                    border-color: #7c8db5;
                                    box-shadow: none;
                                    outline: none;
                                }
                            }
                        }
                    }
                    & > td:nth-child(2) {
                        width: 300px;
                        @media only screen and (max-width: 1599px) {
                            width: 250px;
                        }
                        @media only screen and (max-width: 782px) {
                            width: 150px !important;
                        }
                    }
                    & > td:nth-child(3) {
                        width: 190px;
                        @media only screen and (max-width: 1599px) {
                            width: 170px;
                        }
                        @media only screen and (max-width: 782px) {
                            width: 170px !important;
                        }
                    }
                    & > td:nth-child(4) {
                        width: 50px;
                        @media only screen and (max-width: 782px) {
                            width: 50px !important;
                        }
                    }
                    & > td:nth-child(5) {
                        width: 120px;
                        @media only screen and (max-width: 1599px) {
                            width: 100px;
                        }
                        @media only screen and (max-width: 782px) {
                            width: 120px !important;
                        }
                    }
                    & > td:nth-child(6) {
                        width: 60px;
                        @media only screen and (max-width: 1599px) {
                            width: 50px;
                        }
                        @media only screen and (max-width: 782px) {
                            width: 70px !important;
                        }
                    }
                    & > td:nth-child(7) {
                        width: 130px;
                        @media only screen and (max-width: 1599px) {
                            width: 110px;
                        }
                        @media only screen and (max-width: 782px) {
                            width: 130px !important;
                        }
                    }
                    & > td:last-child {
                        border-top-right-radius: 10px;
                        width: 175px;
                        @media only screen and (max-width: 782px) {
                            width: 175px !important;
                        }
                    }
                }
            }
            tbody {
                transform: translateY(0px);
                tr {
                    border-spacing: 0 30px;
                    box-shadow: none;
                    vertical-align: middle;
                    & > td {
                        margin-bottom: 20px;
                        background-color: #fff;
                        vertical-align: middle;
                        color: #25396f;
                        font-size: 18px;
                        padding: 10px 20px;
                        @media only screen and (max-width: 1599px) {
                            font-size: 14px;
                            padding: 5px 10px;
                        }
                        &:nth-child(1) {
                            width: 20px;
                            padding-right: 0;
                            .rb-item-selector {
                                display: flex;
                                align-items: center;
                                align-self: center;
                                height: 20px;
                                transform: translate(0px, 3px);
                                input[type="checkbox"] {
                                    margin-left: 0;
                                    width: 20px;
                                    min-width: 20px;
                                    height: 20px;
                                    border-color: #e6effb;
                                    &:before {
                                        width: 17px;
                                        margin: 1px 0 0;
                                    }
                                    &:focus {
                                        border-color: #7c8db5;
                                        box-shadow: none;
                                        outline: none;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        .simple_wishlist-dashboard-title {
            a {
                font-weight: 500;
                color: #25396f;
                font-size: 18px;
                line-height: 1.1;
                display: inline-flex;
                @media only screen and (max-width: 1599px) {
                    font-size: 14px;
                }
                &:hover,
                &:focus {
                    color: darken(#25396f, 1%);
                    outline: none;
                    box-shadow: none;
                }
            }
        }
        .simple_wishlist-dashboard-preview {
            height: 100px;
            border: 2px solid rgba(#0ccfc2, 0.05);
            background-color: #f6f7fe;
            display: inline-flex;
            padding: 12px;
            box-sizing: border-box;
            border-radius: 10px;
            align-items: center;
            justify-content: center;
            width: 200px;
            @media only screen and (max-width: 1599px) {
                width: 170px;
                height: 70px;
                padding: 5px;
            }
            img {
                display: block;
                max-width: 100%;
                background-color: transparent;
                padding: 0;
                box-shadow: none;
                box-sizing: border-box;
                max-height: 100%;
            }
        }
        .simple_wishlist-dashboard-actions {
            margin-top: 15px;
            display: flex;
            & > a,
            & > button {
                font-size: 14px;
                background-repeat: no-repeat;
                background-size: 13px 15px;
                padding-left: 20px;
                margin-right: 10px;
                color: #555555;
                border: 0;
                background-color: #fff;
                cursor: pointer;
                height: 20px;
                width: 20px;
                padding-left: 0;
                background-position: center center;
                box-shadow: none;
                position: relative;
                cursor: pointer;
                @media only screen and (max-width: 1599px) {
                    font-size: 12px;
                    height: 15px;
                    width: 15px;
                }
                span {
                    display: none;
                }
                &.simple_wishlist-dashboard-title-xss:before{
                    width: 95px;
                }
                &:before {
                    content: attr(title);
                    position: absolute;
                    bottom: calc(100% + 5px);
                    left: 50%;
                    transform: translateX(-50%);
                    padding: 5px 10px;
                    line-height: 1.2;
                    border-radius: 3px;
                    background: #6a4bff;
                    color: #fff;
                    text-transform: uppercase;
                    font-size: 12px;
                    font-weight: 500;
                    display: inline-flex;
                    justify-content: center;
                    text-align: center;
                    visibility: hidden;
                    @media only screen and (max-width: 1599px) {
                        font-size: 10px;
                    }
                    opacity: 0;
                }
                &:after {
                    content: "";
                    border-top: 5px solid #6a4bff;
                    border-right: 5px solid transparent;
                    border-left: 5px solid transparent;
                    position: absolute;
                    bottom: calc(100% + 1px);
                    left: 50%;
                    transform: translateX(-50%);
                    visibility: hidden;
                    opacity: 0;
                }
                &:hover {
                    &::after,
                    &::before {
                        visibility: visible;
                        opacity: 1;
                    }
                }
            }
            .simple_wishlist-dashboard-title-translate {
                background-image: url("../icons/translate.png");
            }
            .simple_wishlist-dashboard-title-edit {
                background-image: url("../icons/edit.png");
            }
            .simple_wishlist-dashboard-title-regenerate {
                background-image: url("../icons/regenerate.png");
            }
            .simple_wishlist-dashboard-title-duplicate {
                background-image: url("../icons/duplicate.png");
            }
            .simple_wishlist-dashboard-title-restore {
                background-size: 16px 16px;
                padding-left: 22px;
                background-image: url("../icons/restore.png");
            }
            .simple_wishlist-dashboard-title-trash {
                color: #c4434e;
                background-image: url("../icons/trash.png");
            }
            .simple_wishlist-dashboard-title-shortcode {
                background-image: url("../icons/shortcode.png");
                color: #844dee;
            }
            .simple_wishlist-dashboard-title-xss {
                background-image: url('../icons/xss.png');
                color: #844dee;
                background-size: 16px;
            }
        }
    }
    .simple_wishlist-dashboard-items-footer {
        display: flex;
        margin-top: 20px;
        align-items: center;
        > .components-base-control {
            margin-right: 10px;
            display: flex;
            align-items: center;
            .components-base-control__field {
                margin-bottom: 0;
                .components-input-control__label {
                    margin-right: 5px;
                }
            }
            .components-flex {
                flex-direction: row;
                align-items: center;
                .components-input-control__container {
                    min-height: 36px;
                    border-radius: 5px;
                    select {
                        min-height: 34px;
                        border-radius: 5px;
                    }
                    .components-input-control__backdrop {
                        min-height: 36px;
                        padding: 2px 0px 2px 15px;
                        border: 1.5px solid #e6effb;
                        border-radius: 5px;
                        color: #25396f;
                        font-size: 14px;
                        font-weight: 500;
                    }
                }
            }
        }
        .rc-pagination {
            margin-left: auto;
            display: flex;
            align-items: center;
            margin-top: 0;
            margin-bottom: 0;
            > *:not(:last-child) {
                margin-right: 5px;
            }
            &:after {
                display: block;
                clear: both;
                height: 0;
                overflow: hidden;
                visibility: hidden;
                content: " ";
            }
            &-total-text {
                display: flex;
                height: 36px;
                // @include respond-below(hl){
                //     height: 36px;
                // }
                // @include respond-below(xxl){
                //     height: 30px;
                // }
                align-items: center;
                justify-content: center;
            }
            &-item {
                display: flex;
                min-width: 36px;
                height: 36px;
                margin-bottom: 0;
                // @include respond-below(hl){
                //     height: 36px;
                //     min-width: 36px;
                // }
                // @include respond-below(xxl){
                //     height: 30px;
                //     min-width: 30px;
                // }
                background-color: rgba(#6a4bff, 0.15);
                border-radius: 10px;
                cursor: pointer;
                user-select: none;
                button,
                a {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 100%;
                    color: #6a4bff;
                    transition: none;
                    box-shadow: none;
                    cursor: pointer;
                    text-decoration: none;
                }
                &:focus,
                &:hover {
                    background: #6a4bff;
                    a {
                        color: #fff;
                    }
                }
                &-active {
                    background: #6a4bff;
                    a {
                        color: #fff;
                    }
                    &:focus a,
                    &:hover a {
                        color: #fff;
                    }
                }
            }

            &-jump-prev,
            &-jump-next {
                background: rgba(#6a4bff, 0.15);
                border-radius: 10px;
                cursor: pointer;
                user-select: none;
                button,
                a {
                    text-decoration: none;
                    display: flex;
                    cursor: pointer;
                    user-select: none;
                    height: 100%;
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 10px;
                    color: transparent;
                    box-shadow: none;
                    text-decoration: none;
                    position: relative;
                    &:after {
                        position: absolute !important;
                        content: "•••";
                        top: 0;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        color: #6a4bff;
                        cursor: pointer;
                        user-select: none;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 10px;
                        box-shadow: none;
                        text-decoration: none;
                        position: relative;
                        font-size: 0.75rem;
                    }
                }
                &:focus,
                &:hover {
                    background: #6a4bff;
                    button,
                    a {
                        text-decoration: none;
                        &:after {
                            color: #fff;
                        }
                    }
                }
            }
            &-prev,
            &-next,
            &-jump-prev,
            &-jump-next {
                display: flex;
                justify-content: center;
                align-items: center;
                min-width: 36px;
                height: 36px;
                margin-bottom: 0;
                // @include respond-below(hl){
                //     height: 36px;
                //     min-width: 36px;
                // }
                // @include respond-below(xxl){
                //     height: 30px;
                //     min-width: 30px;
                // }
                color: #6a4bff;
                border-radius: 10px;
                font-size: 0.75rem;
                cursor: pointer;
            }

            &-prev,
            &-next {
                button,
                a {
                    cursor: pointer;
                    user-select: none;
                    height: 100%;
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(#6a4bff, 0.15);
                    border-radius: 10px;
                    color: transparent;
                    box-shadow: none;
                    text-decoration: none;
                    position: relative;
                    &:after {
                        position: absolute !important;
                        top: 0;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        color: #6a4bff;
                        cursor: pointer;
                        user-select: none;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background-color: rgba(#6a4bff, 0.15);
                        border-radius: 10px;
                        box-shadow: none;
                        text-decoration: none;
                        position: relative;
                        font-size: 0.75rem;
                    }
                }
                &:focus button,
                &:focus a,
                &:hover button,
                &:hover a {
                    background: #6a4bff;
                    color: transparent !important;
                    &:after {
                        background: #6a4bff;
                        color: #fff;
                    }
                }
            }
            &-prev {
                button,
                a {
                    &:after {
                        content: "<";
                    }
                }
            }
            &-next {
                button,
                a {
                    &:after {
                        content: ">";
                    }
                }
            }

            &-disabled {
                button,
                a {
                    text-decoration: none;
                    cursor: not-allowed !important;
                    color: transparent !important;
                    background: rgba(#ccc, 0.15) !important;
                    &:after {
                        cursor: not-allowed !important;
                        color: #a3a3a3 !important;
                        background: rgba(#ccc, 0.15) !important;
                    }
                }
                &:hover,
                &:focus {
                    button,
                    a {
                        cursor: not-allowed !important;
                        color: transparent !important;
                        background: rgba(#ccc, 0.15) !important;
                        &:after {
                            cursor: not-allowed !important;
                            color: #a3a3a3 !important;
                            background: rgba(#ccc, 0.15) !important;
                        }
                    }
                }
            }

            &-simple &-prev,
            &-simple &-next {
                height: 36px;
                // @include respond-below(hl){
                //     height: 36px;
                // }
                // @include respond-below(xxl){
                //     height: 30px;
                // }
                display: flex;
                align-items: center;
                justify-content: center;
                button,
                a {
                    text-decoration: none;
                    height: 36px;
                    // @include respond-below(hl){
                    //     height: 36px;
                    // }
                    // @include respond-below(xxl){
                    //     height: 30px;
                    // }
                    &::after {
                        height: 36px;
                        // @include respond-below(hl){
                        //     height: 36px;
                        // }
                        // @include respond-below(xxl){
                        //     height: 30px;
                        // }
                    }
                }
            }

            // @include respond-below(hl){
            //     height: 36px;
            // }
            // @include respond-below(xxl){
            //     height: 30px;
            // }
        }
    }
    .simple_wishlist-dashboard-status {
        label {
            width: 40px;
            height: 20px;
            min-height: 20px;
            position: relative;
            cursor: pointer;
            display: flex;
            font-size: 24px;
            &:before,
            &:after {
                content: "";
                position: absolute;
            }
            &:before {
                width: 40px;
                height: 20px;
                left: 0;
                transition: background 0.1s 0.1s ease;
                background: #7c8db5;
                border: 1px solid #7c8db5;
                border-radius: 50px;
                margin: 0;
                box-sizing: border-box;
            }
            &:after {
                width: 18px;
                height: 18px;
                border-radius: 50px;
                left: 1px;
                top: 1px;
                transition: all 0.2s ease;
                box-shadow: 0 0 0 5px #fcfff4 inset;
                background: #fff;
                z-index: 2;
                animation: switch-off 0.3s ease-out;
            }
        }
        input[type="checkbox"] {
            visibility: hidden;
            display: none;
            &:checked + label::before {
                background: #6a4bff;
                border: 1px solid #6a4bff;
            }
            &:checked + label::after {
                left: 21px;
                background: #ffffff;
                animation: switch-on 0.3s ease-out;
            }
        }
    }
    .simple_wishlist-dashboard-stats {
        color: #6a4bff;
    }
    .simple_wishlist-dashboard-date {
        span {
            color: #7c8db5;
            font-size: 14px;
        }
    }
}
