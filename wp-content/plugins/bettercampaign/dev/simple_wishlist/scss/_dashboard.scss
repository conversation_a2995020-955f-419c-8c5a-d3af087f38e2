
.simple_wishlist-main .widgets-wrapper {
    display: block;
    overflow: hidden;
    margin-top: 15px;
    width: 100%;
  }
  .simple_wishlist-main .widgets-wrapper .left-side,
  .simple_wishlist-main .widgets-wrapper .right-side {
    float: left;
    width: 48%;
  }
  .simple_wishlist-main .widgets-wrapper .left-side {
    margin-right: 3%;
  }
  .simple_wishlist-main .simple_wishlist-postbox .loading {
    display: block;
    width: 100%;
    margin: 15px auto;
    text-align: center;
  }
  .simple_wishlist-main .subscribe-box {
    margin: 20px -12px -11px -12px;
    padding: 0 15px 15px;
    background: #fafafa;
    border-top: 1px solid #efefef;
    position: relative;
  }
  .simple_wishlist-main .subscribe-box h3 {
    margin: 10px 0;
  }
  .simple_wishlist-main .subscribe-box p {
    margin-bottom: 10px !important;
  }
  .simple_wishlist-main .subscribe-box .thank-you {
    background: #4fa72b;
    margin-top: 10px;
    padding: 15px;
    border-radius: 3px;
    color: #fff;
  }
  .simple_wishlist-main .subscribe-box .form-wrap {
    display: flex;
  }
  .simple_wishlist-main .subscribe-box .form-wrap input[type="email"] {
    width: 100%;
    padding: 3px 0 3px 6px;
    margin: 0px -1px 0 0;
  }
  .simple_wishlist-main .subscribe-box .form-wrap button.button {
    box-shadow: none;
    background: #FF5722;
    color: #fff;
    border-color: #FF5722;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .simple_wishlist-main .subscribe-box .form-wrap button.button:hover {
    background: #ff6a3c;
  }
  .simple_wishlist-main .subscribe-box .loading {
    position: absolute;
    height: 100%;
    margin: 0 0 0 -15px;
    background: rgba(0, 0, 0, 0.2);
  }
  .simple_wishlist-main .subscribe-box .loading .simple_wishlist-loader {
    margin-top: 30px;
  }
  @media only screen and (max-width: 770px) {
  .simple_wishlist-main .widgets-wrapper .left-side {
      margin-right: 0;
  }
  .simple_wishlist-main .widgets-wrapper .left-side,
    .simple_wishlist-main .widgets-wrapper .right-side {
      width: auto;
  }
  }
  @media only screen and (max-width: 500px) {
  .simple_wishlist-main .widgets-wrapper .left-side {
      margin-right: 0;
  }
  .simple_wishlist-main .widgets-wrapper .left-side,
    .simple_wishlist-main .widgets-wrapper .right-side {
      width: auto;
  }
  .simple_wishlist-main .postbox.simple_wishlist-status ul li a .details span.up,
    .simple_wishlist-main .postbox.simple_wishlist-status ul li a .details span.down {
      display: none;
  }
  .simple_wishlist-main .postbox.simple_wishlist-status ul li a strong {
      font-size: 16px;
  }
  }
  @media only screen and (max-width: 360px) {
  .simple_wishlist-main .postbox.simple_wishlist-status ul li a .details {
      display: none;
  }
  }
.simple_wishlist-main .widgets-wrapper {
  display: block;
  overflow: hidden;
  margin-top: 15px;
  width: 100%;
}
.simple_wishlist-main .widgets-wrapper .left-side,
.simple_wishlist-main .widgets-wrapper .right-side {
  float: left;
  width: 48%;
}
.simple_wishlist-main .widgets-wrapper .left-side {
  margin-right: 3%;
}
.simple_wishlist-main .simple_wishlist-postbox .loading {
  display: block;
  width: 100%;
  margin: 15px auto;
  text-align: center;
}
.simple_wishlist-main .subscribe-box {
  margin: 20px -12px -11px -12px;
  padding: 0 15px 15px;
  background: #fafafa;
  border-top: 1px solid #efefef;
  position: relative;
}
.simple_wishlist-main .subscribe-box h3 {
  margin: 10px 0;
}
.simple_wishlist-main .subscribe-box p {
  margin-bottom: 10px !important;
}
.simple_wishlist-main .subscribe-box .thank-you {
  background: #4fa72b;
  margin-top: 10px;
  padding: 15px;
  border-radius: 3px;
  color: #fff;
}
.simple_wishlist-main .subscribe-box .form-wrap {
  display: flex;
}
.simple_wishlist-main .subscribe-box .form-wrap input[type="email"] {
  width: 100%;
  padding: 3px 0 3px 6px;
  margin: 0px -1px 0 0;
}
.simple_wishlist-main .subscribe-box .form-wrap button.button {
  box-shadow: none;
  background: #FF5722;
  color: #fff;
  border-color: #FF5722;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.simple_wishlist-main .subscribe-box .form-wrap button.button:hover {
  background: #ff6a3c;
}
.simple_wishlist-main .subscribe-box .loading {
  position: absolute;
  height: 100%;
  margin: 0 0 0 -15px;
  background: rgba(0, 0, 0, 0.2);
}
.simple_wishlist-main .subscribe-box .loading .simple_wishlist-loader {
  margin-top: 30px;
}
@media only screen and (max-width: 770px) {
.simple_wishlist-main .widgets-wrapper .left-side {
    margin-right: 0;
}
.simple_wishlist-main .widgets-wrapper .left-side,
  .simple_wishlist-main .widgets-wrapper .right-side {
    width: auto;
}
}
@media only screen and (max-width: 500px) {
  .simple_wishlist-main .widgets-wrapper .left-side {
    margin-right: 0;
  }
  .simple_wishlist-main .widgets-wrapper .left-side,
  .simple_wishlist-main .widgets-wrapper .right-side {
      width: auto;
  }
  .simple_wishlist-main .postbox.simple_wishlist-status ul li a .details span.up,
  .simple_wishlist-main .postbox.simple_wishlist-status ul li a .details span.down {
      display: none;
  }
  .simple_wishlist-main .postbox.simple_wishlist-status ul li a strong {
    font-size: 16px;
  }
}
@media only screen and (max-width: 360px) {
  .simple_wishlist-main .postbox.simple_wishlist-status ul li a .details {
    display: none;
  }
}
.simple_wishlist-postbox .toggle-indicator:before {
  content: "\f142";
  display: inline-block;
  font: 400 20px/1 dashicons;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-decoration: none !important;
}
.simple_wishlist-postbox.closed .toggle-indicator:before {
  content: "\f140";
}
.simple_wishlist-postbox {
  position: relative;
}
.simple_wishlist-postbox h2.hndle {
  font-size: 14px;
  padding: 8px 12px;
  margin: 0;
  line-height: 1.4;
}
.upcoming-postbox .main img {
  max-width: 100%;
}
.upcoming-postbox .main {
  position: relative;
}
.upcoming-postbox .main::before{
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #16453e;
  opacity: 0.6;
  content: '';
}
.upcoming-postbox .main::before{
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #16453e;
  opacity: 0.6;
  content: '';
}
.upcoming-postbox .main span {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  left: 22%;
  top: 44%;
  color: white;
  text-transform: uppercase;
  letter-spacing: 8px;
}