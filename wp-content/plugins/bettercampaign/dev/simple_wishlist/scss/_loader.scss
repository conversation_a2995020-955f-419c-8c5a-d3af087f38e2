@keyframes nxloadunload {
    10% {
        opacity: 0.7;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.rb-preloader {
    text-align: center;
    padding: 40px 50px 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    & img {
        width: 200px;
        animation-duration: 1s;
        animation-name: nxloadunload;
        animation-iteration-count: infinite;
        -webkit-animation-duration: 1s;
        -webkit-animation-name: nxloadunload;
        -webkit-animation-iteration-count: infinite;
    }
}