import React, { useEffect } from 'react'
import { useAppContext } from '../hooks';
import useDocumentTitle from './useDocumentTitle';

const withDocumentTitle = (WrappedComponent, title) => {
    const WithDocumentTitle = (props) => {
        const builderContext = useAppContext();
        useEffect(() => {
            useDocumentTitle({ title: title + builderContext.title });
        }, [])
        return <WrappedComponent {...props} />
    }
    return WithDocumentTitle;
}

export default withDocumentTitle;