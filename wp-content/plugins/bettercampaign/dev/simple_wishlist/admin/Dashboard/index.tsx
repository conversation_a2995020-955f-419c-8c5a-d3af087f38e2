import { __ } from '@wordpress/i18n';
import React, { Fragment, useEffect, useState } from 'react'
import { FormBuilder, useBuilderContext } from 'quickbuilder';
import GearIcon from '../../icons/GearIcon';
import Equalizer from '../../icons/Equalizer';
import Labels from '../../icons/Labels';

const Dashboard = (props) => {
    const builderContext = useBuilderContext();

    useEffect(() => {
        let iconLists = {};
        iconLists['gear'] = <GearIcon />
        iconLists['labels'] = <Labels />
        iconLists['equalizer'] = <Equalizer />
        builderContext.registerIcons('tabs', iconLists);
    }, []);

    return (
        <Fragment>
            <FormBuilder {...props} />
        </Fragment>
    )
}
export default Dashboard;