import React from 'react';
import simple_wishlistHelper from '../../core/functions';
import { Link } from 'react-router-dom';
import { __ } from '@wordpress/i18n';

const menuItems = [
  { label: __('Dashboard', 'simple_wishlist'), page: 'simple_wishlist-dashboard' },
  { label: __('Settings', 'simple_wishlist'), page: 'simple_wishlist-settings' },
  { label: __('Email Settings', 'simple_wishlist'), page: 'simple_wishlist-email-settings' },
  { label: __('Customization', 'simple_wishlist'), page: 'simple_wishlist-customization' },
  { label: __('Tools', 'simple_wishlist'), page: 'simple_wishlist-tools' },
  { label: __('Help', 'simple_wishlist'), page: 'simple_wishlist-help' },
];

const Menu = ({builder}) => {    
  return (
    <div className='rb-menu-wrapper'>
      <div className="rb-setting-header">
        <div className="rb-setting-logo">
          <img 
            className="rb-setting-header-img" 
            loading="lazy" 
            src="https://wpdev.test/wp-content/plugins/product-blocks/assets/img/logo-option.svg" 
            alt="WowStore"
          />
          <span>{builder?.version}</span>
        </div>
        <ul className="rb-settings-tab" id="rb-dashboard-rb-settings-tab">
          {menuItems.map((item, index) => (
            <li key={index}>
              <Link className="rb-add-new-btn" to={simple_wishlistHelper.getRedirect({ page: item.page })}>
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default Menu;
