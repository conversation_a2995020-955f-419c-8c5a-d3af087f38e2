import { __ } from '@wordpress/i18n';
import React, { useCallback } from 'react'
import { BuilderProvider, useBuilder } from 'quickbuilder';
import { useApp } from "./hooks";
import { ToastContainer } from "react-toastify";
import { Dashboard } from './admin';
import apiFetch from '@wordpress/api-fetch';
import Swal from 'sweetalert2';
import { ToastAlert } from './core/ToasterMsg';
import { settingsSavedAlert, SweetAlert } from './core/functions';

const SettingsWrapper = () => {
    // @ts-ignore
    const appContext = useApp({ ...simple_wishlistTabs });
    const builder = useBuilder(appContext.settings);

    builder.submit.onSubmit = useCallback((event, context) => {
        context.setSubmitting(true);
        apiFetch( {
            path  : 'simple-wishlist/v1/settings',
            method: 'POST',
            data  : context.values,
        } ).then( ( res ) => {
            if( res ) {
                ToastAlert('success',__('Settings saved successfully!!','simple_wishlist'));
            }
        } );
      }, []);
    return (
        <>
            <BuilderProvider value={builder}>
                <Dashboard {...appContext} />
                <ToastContainer />
            </BuilderProvider>
        </>
    )
}
export default SettingsWrapper;