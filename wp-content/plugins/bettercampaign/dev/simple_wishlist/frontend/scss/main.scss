@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap');

button.sw-wishlist-button {
    margin: 10px 0;
    background-color: transparent;
    padding: 0;
    font-size: 16px;
    border-radius: 10px;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 8px;
    // border: 1px solid #ddd;
    box-shadow: none;
    // box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    cursor: pointer;
    color: #000;
    &:focus {
        // border: 1px solid #ddd;
        outline: none;
    }
    svg {
        width: 18px;
        height: 18px;
        path {
            fill: #ddd;
        }
    }
    &.added {
        svg path {
            fill: #000;
        }
    }
}
.sc-product-item  .sw-wishlist-button {
    margin: 0;
}
.sw-wishlist-container {
    width: 100%;
}
.sw-wishlist-button.wishlist-button-inside_image {
    position: absolute;
    left: 20px;
    top: 20px;
    span{
        display: none;
    }
    z-index: 999999;
}
#sw-table {
    table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }
    thead {
        background-color: #f3f3f3;
    }
    th, td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #ddd;
        text-align: center;
    }
    th {
        font-weight: bold;
    }
    .product-name {
        text-align: left;
    }
    .product-name img {
        width: 50px;
        height: 50px;
        margin-right: 10px;
        border-radius: 5px;
    }
    .discounted {
        text-decoration: line-through;
        color: gray;
        margin-right: 5px;
    }
    .stock {
        color: green;
    }
    .add-to-cart {
        color: blue;
        text-decoration: none;
        font-weight: bold;
    }
    .remove-btn {
        border: none;
        background: none;
        cursor: pointer;
        font-size: 8px;
        color: #888;
        background: #efefef;
        border-radius: 50%;
        gap: 10px;
        margin-right: 10px;
        width: 30px;
        height: 30px;
        padding: 0;
        svg {
            fill: #00000085;
        }
    }
}
#sw-toast-container {
    position: fixed;
    top: 40px;
    right: 40px;
    z-index: 1000;
}

.sw-toast {
    background: #28a745;
    color: #fff;
    padding: 10px 20px;
    margin: 5px;
    border-radius: 5px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
}

.sw-toast.error {
    background: #dc3545;
}