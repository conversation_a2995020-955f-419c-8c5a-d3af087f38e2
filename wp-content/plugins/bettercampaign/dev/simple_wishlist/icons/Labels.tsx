import React from 'react'

const Labels = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
        <g clip-path="url(#clip0_109_19)">
        <path d="M12.8588 0.0841907C13.0109 0.0962626 13.1631 0.107892 13.3152 0.119584C13.5698 0.139211 13.8244 0.159143 14.0789 0.179288C14.3716 0.202457 14.6644 0.225204 14.9573 0.247714C15.2721 0.271924 15.587 0.29643 15.9018 0.321026C15.9914 0.328006 16.081 0.334933 16.1706 0.341848C16.3118 0.352758 16.4529 0.36387 16.5941 0.375129C16.6696 0.381109 16.7451 0.386907 16.8206 0.392696C17.6371 0.458528 18.359 0.615619 18.9429 1.23291C19.4646 1.84945 19.5589 2.54825 19.6186 3.32712C19.6229 3.38052 19.6271 3.43391 19.6314 3.48731C19.6428 3.63091 19.6539 3.77453 19.665 3.91815C19.672 4.00851 19.679 4.09887 19.6861 4.18922C19.7109 4.50626 19.7354 4.82331 19.7598 5.14038C19.7823 5.43287 19.8052 5.72532 19.8284 6.01776C19.8484 6.27115 19.8681 6.52456 19.8876 6.77799C19.8991 6.9283 19.9108 7.07861 19.9228 7.22889C19.9685 7.80245 20.008 8.37432 20.0097 8.9501C20.0099 8.99293 20.0101 9.03576 20.0103 9.07989C20.0081 9.38531 19.9765 9.64281 19.8438 9.92187C19.8272 9.95868 19.8106 9.99549 19.7936 10.0334C19.5814 10.4619 19.2191 10.7867 18.8848 11.121C18.8489 11.157 18.813 11.193 18.776 11.2302C18.6775 11.3291 18.5789 11.4278 18.4803 11.5266C18.3735 11.6335 18.2668 11.7406 18.1601 11.8476C17.9031 12.1053 17.646 12.3629 17.3888 12.6204C17.2918 12.7175 17.1948 12.8147 17.0978 12.9118C16.6698 13.3406 16.2416 13.7694 15.8134 14.198C15.7088 14.3025 15.6043 14.4071 15.4998 14.5117C15.4738 14.5377 15.4479 14.5637 15.4211 14.5905C14.9999 15.012 14.5791 15.4338 14.1584 15.8558C13.7257 16.2898 13.2927 16.7234 12.8594 17.1569C12.6165 17.3999 12.3737 17.6431 12.1311 17.8866C11.9248 18.0938 11.7181 18.3008 11.5111 18.5074C11.4057 18.6127 11.3003 18.7181 11.1953 18.8239C11.0812 18.9388 10.9665 19.0531 10.8517 19.1673C10.8027 19.217 10.8027 19.217 10.7526 19.2677C10.208 19.8065 9.63953 20.0108 8.88738 20.0125C8.57988 20.0085 8.33125 19.9667 8.04689 19.8437C8.0127 19.8301 7.97851 19.8164 7.94328 19.8022C7.41516 19.5679 7.00405 19.0415 6.6017 18.6398C6.53513 18.5735 6.46855 18.5072 6.40197 18.4409C6.20335 18.243 6.00489 18.045 5.80647 17.8469C5.73156 17.7721 5.65663 17.6974 5.5817 17.6226C5.25075 17.2924 4.91985 16.9621 4.58912 16.6317C4.5085 16.5511 4.42788 16.4706 4.34725 16.39C4.32722 16.37 4.30719 16.35 4.28654 16.3294C3.96167 16.0048 3.63647 15.6806 3.31113 15.3565C2.97619 15.0229 2.64155 14.6889 2.30719 14.3547C2.11982 14.1674 1.93232 13.9803 1.74449 13.7934C1.5679 13.6178 1.39168 13.4417 1.21574 13.2654C1.1513 13.2009 1.08673 13.1366 1.02201 13.0724C0.412959 12.4681 -0.00595761 11.9267 -0.0121925 11.0352C-0.0129646 10.6857 0.0167617 10.4019 0.156265 10.0781C0.169054 10.0448 0.181844 10.0116 0.195022 9.97726C0.4565 9.37128 1.10645 8.88723 1.56516 8.42803C1.6715 8.32153 1.77773 8.21493 1.88398 8.10835C2.13957 7.85199 2.39534 7.59581 2.65114 7.33966C2.74766 7.24301 2.84416 7.14635 2.94066 7.04969C3.36665 6.62298 3.79269 6.19632 4.21889 5.76982C4.3228 5.66583 4.42672 5.56184 4.53063 5.45786C4.55645 5.43202 4.58227 5.40617 4.60888 5.37955C5.02791 4.96019 5.44662 4.54049 5.86519 4.12066C6.29581 3.68874 6.72674 3.25711 7.15795 2.82577C7.39971 2.58393 7.64133 2.34196 7.88263 2.09966C8.08789 1.89355 8.29342 1.68771 8.4993 1.48222C8.60421 1.37748 8.709 1.27262 8.81345 1.16741C8.92688 1.05317 9.0409 0.939541 9.155 0.825961C9.18761 0.792918 9.22023 0.759875 9.25383 0.72583C9.5241 0.458485 9.81551 0.267317 10.1685 0.124509C10.2062 0.108949 10.2439 0.09339 10.2828 0.0773591C11.0483 -0.194441 12.0577 0.0201986 12.8588 0.0841907ZM10.136 1.68497C10.0702 1.75008 10.0046 1.81551 9.93935 1.88119C9.90299 1.91732 9.86662 1.95345 9.82915 1.99067C9.7288 2.09039 9.62881 2.19046 9.52888 2.29061C9.42082 2.39877 9.31243 2.50659 9.20408 2.61446C8.99211 2.82561 8.78046 3.03708 8.56894 3.24868C8.39698 3.42068 8.22493 3.59259 8.0528 3.76442C8.00317 3.81397 7.95354 3.86353 7.9039 3.91308C7.86684 3.95008 7.86684 3.95008 7.82903 3.98782C7.36218 4.45394 6.89571 4.92045 6.42935 5.38707C6.00288 5.81376 5.5759 6.23993 5.14867 6.66586C4.70971 7.10351 4.2711 7.54152 3.83288 7.97992C3.58697 8.22593 3.34091 8.47178 3.09446 8.71725C2.88471 8.92619 2.67529 9.13546 2.46632 9.34518C2.35974 9.45211 2.25301 9.55889 2.14585 9.66524C2.02958 9.78064 1.91408 9.89679 1.79865 10.013C1.7479 10.063 1.7479 10.063 1.69612 10.1141C1.43541 10.3787 1.26666 10.6312 1.2671 11.0132C1.26625 11.0562 1.26539 11.0993 1.26451 11.1436C1.32319 11.491 1.52112 11.7453 1.76651 11.9866C1.79451 12.0149 1.8225 12.0432 1.85134 12.0724C1.94447 12.1663 2.03827 12.2595 2.13209 12.3527C2.19932 12.4202 2.26651 12.4877 2.33366 12.5553C2.49675 12.7191 2.66029 12.8826 2.82406 13.0458C2.95721 13.1785 3.09016 13.3114 3.22298 13.4444C3.25141 13.4729 3.25141 13.4729 3.28041 13.5019C3.31892 13.5405 3.35743 13.5791 3.39594 13.6177C3.75643 13.9787 4.11752 14.3392 4.47882 14.6994C4.78844 15.0081 5.09755 15.3174 5.40628 15.627C5.76536 15.9872 6.12475 16.347 6.48465 16.7063C6.52307 16.7446 6.56148 16.783 6.59989 16.8213C6.61878 16.8402 6.63768 16.8591 6.65715 16.8785C6.78994 17.0111 6.92249 17.144 7.05497 17.2769C7.23341 17.4559 7.41238 17.6345 7.59169 17.8126C7.65735 17.878 7.72284 17.9436 7.78815 18.0094C7.87747 18.0992 7.96736 18.1884 8.05743 18.2775C8.08302 18.3036 8.10861 18.3296 8.13498 18.3564C8.36101 18.5777 8.62375 18.7258 8.94411 18.7512C9.42973 18.7437 9.73381 18.4477 10.0589 18.1206C10.0951 18.0846 10.1313 18.0487 10.1686 18.0116C10.2684 17.9124 10.3679 17.8129 10.4673 17.7133C10.5748 17.6057 10.6826 17.4984 10.7904 17.3911C11.0013 17.181 11.2118 16.9706 11.4223 16.7601C11.5934 16.589 11.7646 16.4179 11.9359 16.2469C11.9852 16.1976 12.0346 16.1483 12.084 16.099C12.1086 16.0745 12.1332 16.0499 12.1585 16.0246C12.6231 15.5608 13.0874 15.0965 13.5515 14.6321C13.9758 14.2076 14.4006 13.7836 14.8256 13.3599C15.2624 12.9244 15.6989 12.4885 16.1349 12.0523C16.3796 11.8075 16.6244 11.5629 16.8696 11.3187C17.0783 11.1108 17.2867 10.9026 17.4947 10.6939C17.6007 10.5875 17.7069 10.4813 17.8135 10.3755C17.9292 10.2606 18.0442 10.145 18.1591 10.0292C18.1927 9.99617 18.2262 9.96309 18.2608 9.92901C18.5284 9.65745 18.7347 9.37709 18.7326 8.98357C18.7313 8.95078 18.73 8.91799 18.7287 8.88421C18.728 8.84782 18.7272 8.81143 18.7265 8.77394C18.7122 8.24855 18.6697 7.72398 18.6293 7.20006C18.62 7.07852 18.6109 6.95698 18.6017 6.83543C18.5801 6.54857 18.5581 6.26174 18.536 5.97493C18.5132 5.68074 18.4911 5.3865 18.4694 5.09223C18.4505 4.8375 18.4312 4.5828 18.4116 4.32813C18.3999 4.17691 18.3884 4.02568 18.3774 3.87441C18.367 3.73242 18.356 3.59048 18.3447 3.44857C18.3387 3.37261 18.3333 3.29662 18.328 3.22062C18.2912 2.77425 18.2209 2.35196 17.8961 2.02362C17.5265 1.712 17.053 1.70038 16.5917 1.66561C16.5315 1.66087 16.4712 1.65612 16.411 1.65134C16.2482 1.6385 16.0853 1.626 15.9224 1.6136C15.7861 1.6032 15.6498 1.59265 15.5136 1.58211C15.1919 1.55724 14.8702 1.53268 14.5485 1.50829C14.2173 1.48317 13.8862 1.45746 13.555 1.43146C13.2701 1.40912 12.9851 1.38719 12.7001 1.36558C12.5302 1.35269 12.3603 1.33963 12.1904 1.32616C12.0306 1.3135 11.8707 1.30136 11.7108 1.28961C11.6524 1.28523 11.594 1.28066 11.5356 1.2759C10.9542 1.22871 10.5685 1.27297 10.136 1.68497Z" fill="black"/>
        <path d="M16.1804 3.4906C16.6252 3.87432 16.9658 4.44686 17.0312 5.03906C17.0772 5.68279 16.9697 6.30471 16.5583 6.8222C16.4717 6.92155 16.382 7.01593 16.289 7.10937C16.2655 7.1343 16.2419 7.15922 16.2176 7.18491C15.802 7.59058 15.1764 7.7668 14.6093 7.77344C13.9432 7.73492 13.3123 7.49892 12.848 7.00607C12.3916 6.44327 12.1756 5.79781 12.2485 5.07568C12.33 4.41316 12.6476 3.86276 13.164 3.4375C14.0985 2.78023 15.2595 2.81557 16.1804 3.4906ZM13.75 4.6875C13.5461 4.99758 13.4815 5.26088 13.5547 5.62256C13.6416 5.93277 13.7994 6.15868 14.0771 6.32568C14.4064 6.4859 14.6857 6.51566 15.039 6.40625C15.3096 6.28825 15.5339 6.0773 15.668 5.8139C15.7923 5.48506 15.7785 5.17558 15.6349 4.85626C15.477 4.56416 15.2427 4.37325 14.9238 4.27292C14.4548 4.18448 14.0628 4.32966 13.75 4.6875Z" fill="black"/>
        </g>
        <defs>
        <clipPath id="clip0_109_19">
        <rect width="20" height="20" fill="white"/>
        </clipPath>
        </defs>
    </svg>
  )
}

export default Labels