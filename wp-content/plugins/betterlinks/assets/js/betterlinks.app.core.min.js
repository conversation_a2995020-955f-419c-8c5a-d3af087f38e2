(()=>{"use strict";function t(t,n){(null==n||n>t.length)&&(n=t.length);for(var r=0,e=Array(n);r<n;r++)e[r]=t[r];return e}var n;n=jQuery,{init:function(){this.trackUnCloakedLinksWithLinkID(),this.trackUnCloakedLinksWithoutLinkID()},trackUnCloakedLinksWithoutLinkID:function(){var t=this;n(document).on("click","body a.betterlinks-linked-text:not([data-link-id])",(function(n){var r,e;if(null===(r=n.target)||void 0===r||null===(r=r.href)||void 0===r||!r.startsWith(null===(e=betterLinksApp)||void 0===e?void 0:e.site_url)){!1 in navigator&&(n.preventDefault(),t.redirectToTarget(n.target));var i={target_url:n.target.href};t.initTracking(i)}}))},trackUnCloakedLinksWithLinkID:function(){var t=this;n(document).on("click","body a.betterlinks-linked-text[data-link-id]",(function(n){var r,e;if(null===(r=n.target)||void 0===r||null===(r=r.href)||void 0===r||!r.startsWith(null===(e=betterLinksApp)||void 0===e?void 0:e.site_url)){var i;!1 in navigator&&(n.preventDefault(),t.redirectToTarget(n.target));var a={linkId:null===(i=n.target.dataset)||void 0===i?void 0:i.linkId};t.initTracking(a)}}))},redirectToTarget:function(t){""===t.target?window.location.href=t.href:window.open(t.href,t.target)},initTracking:function(n){var r=new FormData;r.append("action","betterlinks__js_analytics_tracking"),r.append("security",betterLinksApp.betterlinks_nonce),Object.entries(n).forEach((function(n){var e,i,a=(i=2,function(t){if(Array.isArray(t))return t}(e=n)||function(t,n){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var e,i,a,o,l=[],c=!0,u=!1;try{if(a=(r=r.call(t)).next,0===n){if(Object(r)!==r)return;c=!1}else for(;!(c=(e=a.call(r)).done)&&(l.push(e.value),l.length!==n);c=!0);}catch(t){u=!0,i=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return l}}(e,i)||function(n,r){if(n){if("string"==typeof n)return t(n,r);var e={}.toString.call(n).slice(8,-1);return"Object"===e&&n.constructor&&(e=n.constructor.name),"Map"===e||"Set"===e?Array.from(n):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?t(n,r):void 0}}(e,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),o=a[0],l=a[1];r.append(o,l)})),r.append("location",location.pathname),"sendBeacon"in navigator?navigator.sendBeacon(betterLinksApp.ajaxurl,r):fetch(betterLinksApp.ajaxurl,{method:"POST",body:r})}}.init()})();