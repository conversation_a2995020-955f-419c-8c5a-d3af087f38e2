"use strict";(globalThis.webpackChunkbetterlinks=globalThis.webpackChunkbetterlinks||[]).push([[37],{77925:(e,t,l)=>{l.d(t,{A:()=>o});var r=l(51609),n=l.n(r),a=l(5556),c=l.n(a),i={type:c().string,label:c().string,onClickHandler:c().func};function o(e){var t=e.type,l=void 0===t?"":t,a=e.label,c=void 0===a?"":a,i=e.onClickHandler,o=void 0===i?function(){}:i,s=e.children;return(0,r.createElement)(n().Fragment,null,(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("button",{className:"dnd-link-button",onClick:o},s||(0,r.createElement)("span",{className:"icon"},(0,r.createElement)("i",{className:"btl btl-".concat(l)}))),(0,r.createElement)("span",{className:"btl-tooltiptext"},c)))}o.propTypes=i},60907:(e,t,l)=>{l.d(t,{A:()=>i});var r=l(64467),n=l(51609),a=l(43516);function c(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,r)}return l}const i=function(e){return(0,n.createElement)(a.Ay,function(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?c(Object(l),!0).forEach((function(t){(0,r.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):c(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}({speed:2,width:"100%",height:"100%",viewBox:"0 0 532 148",backgroundColor:"#e8e8e8",foregroundColor:"#c2c2c2"},e),(0,n.createElement)("rect",{x:"2",y:"2",rx:"0",ry:"0",width:"530",height:"11"}),(0,n.createElement)("rect",{x:"3",y:"21",rx:"0",ry:"0",width:"527",height:"10"}),(0,n.createElement)("rect",{x:"2",y:"46",rx:"0",ry:"0",width:"530",height:"3"}),(0,n.createElement)("rect",{x:"2",y:"61",rx:"0",ry:"0",width:"530",height:"3"}),(0,n.createElement)("rect",{x:"2",y:"76",rx:"0",ry:"0",width:"530",height:"3"}),(0,n.createElement)("rect",{x:"2",y:"91",rx:"0",ry:"0",width:"530",height:"3"}),(0,n.createElement)("rect",{x:"2",y:"106",rx:"0",ry:"0",width:"530",height:"3"}),(0,n.createElement)("rect",{x:"1",y:"2",rx:"0",ry:"0",width:"3",height:"107"}),(0,n.createElement)("rect",{x:"529",y:"2",rx:"0",ry:"0",width:"3",height:"106"}),(0,n.createElement)("rect",{x:"2",y:"8",rx:"0",ry:"0",width:"8",height:"16"}),(0,n.createElement)("rect",{x:"25",y:"8",rx:"0",ry:"0",width:"84",height:"17"}),(0,n.createElement)("rect",{x:"144",y:"10",rx:"0",ry:"0",width:"83",height:"14"}),(0,n.createElement)("rect",{x:"264",y:"9",rx:"0",ry:"0",width:"77",height:"16"}),(0,n.createElement)("rect",{x:"476",y:"8",rx:"0",ry:"0",width:"54",height:"18"}),(0,n.createElement)("rect",{x:"374",y:"8",rx:"0",ry:"0",width:"71",height:"16"}),(0,n.createElement)("rect",{x:"10",y:"39",rx:"0",ry:"0",width:"16",height:"2"}),(0,n.createElement)("rect",{x:"110",y:"38",rx:"0",ry:"0",width:"39",height:"2"}),(0,n.createElement)("rect",{x:"227",y:"38",rx:"0",ry:"0",width:"65",height:"3"}),(0,n.createElement)("rect",{x:"341",y:"38",rx:"0",ry:"0",width:"39",height:"2"}),(0,n.createElement)("rect",{x:"445",y:"38",rx:"0",ry:"0",width:"54",height:"3"}),(0,n.createElement)("rect",{x:"10",y:"55",rx:"0",ry:"0",width:"16",height:"2"}),(0,n.createElement)("rect",{x:"110",y:"54",rx:"0",ry:"0",width:"39",height:"2"}),(0,n.createElement)("rect",{x:"227",y:"54",rx:"0",ry:"0",width:"58",height:"3"}),(0,n.createElement)("rect",{x:"341",y:"54",rx:"0",ry:"0",width:"39",height:"2"}),(0,n.createElement)("rect",{x:"445",y:"54",rx:"0",ry:"0",width:"49",height:"3"}),(0,n.createElement)("rect",{x:"10",y:"70",rx:"0",ry:"0",width:"16",height:"2"}),(0,n.createElement)("rect",{x:"110",y:"69",rx:"0",ry:"0",width:"39",height:"2"}),(0,n.createElement)("rect",{x:"227",y:"69",rx:"0",ry:"0",width:"60",height:"3"}),(0,n.createElement)("rect",{x:"341",y:"69",rx:"0",ry:"0",width:"39",height:"2"}),(0,n.createElement)("rect",{x:"445",y:"68",rx:"0",ry:"0",width:"56",height:"3"}),(0,n.createElement)("rect",{x:"10",y:"85",rx:"0",ry:"0",width:"16",height:"2"}),(0,n.createElement)("rect",{x:"110",y:"84",rx:"0",ry:"0",width:"39",height:"2"}),(0,n.createElement)("rect",{x:"227",y:"84",rx:"0",ry:"0",width:"54",height:"3"}),(0,n.createElement)("rect",{x:"341",y:"84",rx:"0",ry:"0",width:"39",height:"2"}),(0,n.createElement)("rect",{x:"445",y:"84",rx:"0",ry:"0",width:"45",height:"3"}),(0,n.createElement)("rect",{x:"10",y:"100",rx:"0",ry:"0",width:"16",height:"2"}),(0,n.createElement)("rect",{x:"110",y:"99",rx:"0",ry:"0",width:"39",height:"2"}),(0,n.createElement)("rect",{x:"227",y:"99",rx:"0",ry:"0",width:"58",height:"3"}),(0,n.createElement)("rect",{x:"341",y:"99",rx:"0",ry:"0",width:"39",height:"2"}),(0,n.createElement)("rect",{x:"445",y:"99",rx:"0",ry:"0",width:"54",height:"3"}))}},16560:(e,t,l)=>{l.d(t,{A:()=>O});var r=l(3453),n=l(80045),a=l(51609),c=l.n(a),i=l(49924),o=l(68238),s=l(27723),u=l(19735),m=l(74086),d=l(67154),b=l(58766),h=l(7400),p=l(20312),y=l.n(p),g=l(46005),v=[{label:(0,s.__)("Delete All","betterlinks"),value:!1},{label:(0,s.__)("Delete clicks older than 30 days","betterlinks"),value:30},{label:(0,s.__)("Delete clicks older than 90 days","betterlinks"),value:90}];const f=(0,i.Ng)((function(){return{}}),(function(e){return{fetchCustomClicksData:(0,o.zH)(b.lC,e),dispatch_new_links_data:(0,o.zH)(h.jT,e)}}))((function(e){var t=e.fetchCustomClicksData,l=e.dispatch_new_links_data,n=(e.propsForAnalytics||{}).customDateFilter,c=(0,a.useState)(0),i=(0,r.A)(c,2),o=i[0],s=i[1],m=(0,a.useState)(!1),d=(0,r.A)(m,2),b=d[0],h=d[1],p=(0,a.useState)(0),f=(0,r.A)(p,2),E=f[0],_=f[1],k=(0,a.useState)("reset_modal_step_1"),w=(0,r.A)(k,2),x=w[0],N=w[1],O=(0,a.useState)(v[0]),C=(0,r.A)(O,2),A=C[0],j=C[1];(0,a.useEffect)((function(){var e,t;return b?null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.add("betterlinks-delete-clicks-modal-popup-opened"):null===(t=document)||void 0===t||null===(t=t.body)||void 0===t||null===(t=t.classList)||void 0===t||t.remove("betterlinks-delete-clicks-modal-popup-opened"),function(){var e;null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.remove("betterlinks-delete-clicks-modal-popup-opened")}}),[b]);var S=function(){clearTimeout(o),N("reset_modal_step_1"),h(!1),j(v[0])};return(0,a.createElement)("div",{className:"btl-analytic-reset-wrapeer betterlinks"},(0,a.createElement)("button",{className:"button-primary btl-reset-analytics-initial-button",onClick:function(){h(!0),N("reset_modal_step_1")}},"Reset"),(0,a.createElement)(y(),{isOpen:b,onRequestClose:S,ariaHideApp:!1},(0,a.createElement)("div",{className:"btl-reset-modal-popup-wrapper "},(0,a.createElement)("span",{className:"btl-close-modal",onClick:S},(0,a.createElement)("i",{className:"btl btl-cancel"})),"reset_modal_step_1"===x&&(0,a.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-1 betterlinks-body"},(0,a.createElement)("h2",null,"Pick the range of BetterLinks Analytics that you want to reset."),(0,a.createElement)("div",{className:"select_apply"},(0,a.createElement)(g.Ay,{className:"btl-modal-select--full ",classNamePrefix:"btl-react-select",onChange:function(e){j(e)},options:v,value:A,isMulti:!1}),(0,a.createElement)("button",{className:"button-primary btl-btn-reset-analytics btl-btn-reset-apply-1",onClick:function(){N("reset_modal_step_2")}},"Apply"))),"reset_modal_step_2"===x&&(0,a.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-2 betterlinks-body"},(0,a.createElement)("h2",null,"This Action Cannot be undone. Are you sure you want to continue?"),(0,a.createElement)("h4",null,"Clicking ",(0,a.createElement)("span",{style:{fontWeight:700}},"Reset Clicks")," will permanently delete the clicks data from database and it cannot be restored again.",(0,a.createElement)("span",{style:{display:"Block"}},"Click 'cancel' to abort.")),(0,a.createElement)("div",{className:"btl-btn-reset-popup-step-2-buttons"},(0,a.createElement)("button",{className:"button-primary btl-btn-reset-apply-2",onClick:function(){if(n){var e=(0,u.Yq)(n[0].startDate,"yyyy-mm-dd"),r=(0,u.Yq)(n[0].endDate,"yyyy-mm-dd");N("deleting");var a=(null==A?void 0:A.value)||!1;(0,u.Xq)(a,e,r).then((function(e){var r,n,a,c,i=setTimeout((function(){S()}),3e3);s(i),null!=e&&null!==(r=e.data)&&void 0!==r&&r.success?(_(null==e||null===(n=e.data)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.count),t({data:null==e||null===(a=e.data)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.new_clicks_data}),l({data:null==e||null===(c=e.data)||void 0===c||null===(c=c.data)||void 0===c?void 0:c.new_links_data}),N("success")):N("failed")})).catch((function(e){console.log("---caught error on DeleteClicks",{err:e});var t=setTimeout((function(){S()}),3e3);s(t)}))}}},"Reset Clicks"),(0,a.createElement)("button",{className:"button-primary btl-btn-reset-cancel",onClick:function(){return N("reset_modal_step_1")}},"Cancel"))),"deleting"===x&&(0,a.createElement)("h2",null,"Deleting..."),"success"===x&&0!==E&&(0,a.createElement)("h2",null,"Success!!! ",(0,a.createElement)("span",{className:"success_delete_count"},E)," clicks record Deleted!!!"),"success"===x&&0===E&&(0,a.createElement)("h2",null,!1===(null==A?void 0:A.value)&&"You don't have any clicks data",30===(null==A?void 0:A.value)&&"You don't have clicks data older than 30 days",90===(null==A?void 0:A.value)&&"You don't have clicks data older than 90 days"),"failed"===x&&(0,a.createElement)("h2",null,"Failed!!"))))}));var E=l(5556),_=l.n(E),k=l(40150),w=["is_pro","render"],x={label:_().string,render:_().func},N=function(e){var t=e.is_pro,l=void 0!==t&&t,i=e.render,o=void 0===i?function(){}:i,m=(0,n.A)(e,w),d=m.propsForAnalytics,b=m.activity.darkMode,h=(0,a.useState)(b),p=(0,r.A)(h,2),y=p[0],g=p[1];(0,a.useEffect)((function(){b?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode")}),[]);var v=betterLinksQuery.get("page"),E=m.favouriteSort.sortByFav;return(0,a.createElement)("div",{className:"topbar"},(0,a.createElement)("div",{className:"topbar__logo_container"},(0,a.createElement)("div",{className:"topbar__logo"},(0,a.createElement)("img",{src:u.hq+"assets/images/logo-large".concat(y?"-white":"",".svg"),alt:"logo"}),(0,a.createElement)("span",{className:"topbar__logo__text"},m.label),l&&(0,a.createElement)(k.A,null)),o()),(0,a.createElement)("div",{className:"topbar-inner"},"betterlinks"===v&&(0,a.createElement)(c().Fragment,null,(0,a.createElement)("div",{className:"btl-view-control"},(0,a.createElement)("button",{title:(0,s.__)("Favorite Links","betterlinks"),className:"btl-link-view-toggler btl-sortby-fav ".concat(E?"active":""),onClick:function(){return m.sortFavourite(!E)}},(0,a.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"favorite-svg",viewBox:"0 0 512 512",xmlSpace:"preserve"},(0,a.createElement)("path",{className:"fav-icon-svg-path",d:"M392.2 317.5c-3 2.9-4.4 7.1-3.7 11.3L414 477.4c1.2 7-3.5 13.6-10.5 14.9-2.8.5-5.6 0-8.1-1.3L262 420.9c-3.7-2-8.2-2-12 0L116.6 491c-3.1 1.7-6.8 1.9-10.1.8-6-2.1-9.5-8.1-8.5-14.4l25.4-148.5c.7-4.2-.7-8.4-3.7-11.4L11.9 212.4c-5.1-5-5.2-13.1-.2-18.2 2-2 4.6-3.3 7.3-3.7l149.1-21.7c4.2-.6 7.8-3.2 9.7-7l66.7-135c2.6-5.3 8.4-8.1 14.2-6.9 3.9.7 7.2 3.3 8.9 6.9l66.7 135c1.9 3.8 5.5 6.4 9.7 7l149 21.6c7 1 11.9 7.6 10.9 14.6-.4 2.7-1.7 5.3-3.7 7.2l-108 105.3z"}))),(0,a.createElement)("button",{title:(0,s.__)("List View","betterlinks"),className:"btl-link-view-toggler ".concat("list"==m.activity.linksView?"active":""),onClick:function(){return m.linksView("list")}},(0,a.createElement)("i",{className:"btl btl-list"})),(0,a.createElement)("button",{title:(0,s.__)("Grid View","betterlinks"),className:"btl-link-view-toggler ".concat("grid"==m.activity.linksView?"active":""),onClick:function(){return m.linksView("grid")}},(0,a.createElement)("i",{className:"btl btl-grid"})))),(null==d?void 0:d.isResetAnalytics)&&(0,a.createElement)(f,{propsForAnalytics:d}),(0,a.createElement)("label",{className:"theme-mood-button",htmlFor:"theme-mood",title:(0,s.__)("Theme Mode","betterlinks")},(0,a.createElement)("input",{type:"checkbox",name:"theme-mood",id:"theme-mood",value:y,onChange:function(){return function(e){e?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode"),m.update_theme_mode(e),g(e)}(!y)},checked:y}),(0,a.createElement)("span",{className:"theme-mood"},(0,a.createElement)("span",{className:"icon"},(0,a.createElement)("i",{className:"btl btl-sun"})),(0,a.createElement)("span",{className:"icon"},(0,a.createElement)("i",{className:"btl btl-moon"}))))))};N.propTypes=x;const O=(0,i.Ng)((function(e){return{activity:e.activity,favouriteSort:e.favouriteSort}}),(function(e){return{linksView:(0,o.zH)(m.xb,e),sortFavourite:(0,o.zH)(d.sortFavourite,e),update_theme_mode:(0,o.zH)(m.Q7,e)}}))(N)},90880:(e,t,l)=>{l.r(t),l.d(t,{default:()=>j});var r=l(64467),n=l(3453),a=l(51609),c=l(49924),i=l(68238),o=l(61582),s=l(16560),u=l(27723),m=l(20312),d=l.n(m),b=l(19735),h=l(4949);function p(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,r)}return l}function y(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?p(Object(l),!0).forEach((function(t){(0,r.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):p(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}var g=y(y({},b.vu),{},{content:y(y({},b.vu.content),{},{maxWidth:"500px",padding:"60px 60px 30px 60px"})});const v=function(e){var t=e.open,l=e.errorMsg,r=e.closeModal,n=e.__handleChange,c=e.__handleSubmit,i=e.row;return(0,a.createElement)(d(),{isOpen:t,onRequestClose:r,style:g,ariaHideApp:!1},(0,a.createElement)("span",{className:"btl-close-modal",onClick:r},(0,a.createElement)("span",{className:"btl btl-cancel"})),(0,a.createElement)(h.l1,{initialValues:{term_slug:i.term_slug||"",term_name:i.term_name||"",term_id:i.id||null},onSubmit:function(e,t){return c(e,t)}},(function(e){return(0,a.createElement)(h.lV,{className:"w-100 btl-manage-tags-form",onSubmit:e.handleSubmit},(0,a.createElement)("div",{className:"btl-entry-content"},(0,a.createElement)("div",{className:"btl-entry-content-left",style:{marginBottom:"20px"}},(0,a.createElement)("div",{className:"btl-modal-form-group"},(0,a.createElement)("label",{className:"btl-modal-form-label",htmlFor:"tags"},(0,u.__)("Tag","betterlinks"),(0,a.createElement)("span",{style:{color:"#f97272",marginLeft:"2px"}},"*")),(0,a.createElement)("div",{style:{width:"100%"}},(0,a.createElement)(h.D0,{id:"term_slug",className:"btl-modal-form-control",type:"text",name:"term_slug",required:!0,onChange:function(t){return n(t,e)},autoFocus:!0}),(0,a.createElement)("span",{className:"btl_duplicate_tags",style:{color:"red",height:"5px",display:"block"}},l))),(0,a.createElement)("div",{className:"btl-modal-form-group"},(0,a.createElement)("label",{className:"btl-modal-form-label"}),(0,a.createElement)("button",{type:"submit",className:"btl-modal-submit-button",disabled:""!==l&&""!==e.values.term_slug},null!=i&&i.term_slug?(0,u.__)("Update","betterlinks"):(0,u.__)("Publish","betterlinks"))))))})))};var f=l(77925);const E=(0,c.Ng)(null,(function(e){return{add_new_tag:(0,i.zH)(o.mj,e)}}))((function(e){var t=(0,a.useState)(!1),l=(0,n.A)(t,2),r=l[0],c=l[1],i=(0,a.useState)(""),o=(0,n.A)(i,2),s=o[0],m=o[1],d=e.tags,b=e.icon,h=void 0!==b&&b,p=e.row,y=void 0===p?{}:p,g=e.children,E=function(){c(!0)},_=function(){c(!1)};return(0,a.createElement)(a.Fragment,null,(d||[]).length>0&&h?(0,a.createElement)(f.A,{type:"edit",label:(0,u.__)("Edit Tag","betterlinks"),onClickHandler:E},g):(0,a.createElement)("div",{className:"btl-create-autolinks btl-create-tags"},(0,a.createElement)("button",{className:"btl-create-autolink-button btl-create-tags-button",onClick:E},(0,u.__)("Add New Tag","betterlinks"))),(0,a.createElement)(v,{open:r,errorMsg:s,closeModal:_,__handleChange:function(e,t){var l=e.target.value,r=(d||[]).some((function(e){return e.term_slug===l}));if(t.setFieldValue("term_slug",l),r)return m((0,u.__)("Tag already exist","betterlinks"));m("")},__handleSubmit:function(t,l){if(""===t.term_slug)return m((0,u.__)("Tag field can't be empty","betterlinks"));var r={ID:(null==y?void 0:y.ID)||t.term_id,term_name:t.term_slug,term_slug:t.term_slug,term_type:"tags"};e.add_new_tag(r),_()},row:y}))}));var _=l(83757),k=l(60907);const w=function(e){var t=e.delete_tag,l=e.children,r=(0,a.useState)(!1),c=(0,n.A)(r,2),i=c[0],o=c[1];return(0,a.createElement)("div",{className:"btl-list-view-action-wrapper"},i?(0,a.createElement)("div",{className:"btl-confirm-message"},(0,a.createElement)("span",{className:"action-text"},(0,u.__)("Are You Sure?","betterlinks")),(0,a.createElement)("div",{className:"action-set"},(0,a.createElement)("button",{className:"action yes",onClick:t},(0,u.__)("Yes","betterlinks")),(0,a.createElement)("button",{className:"action no",onClick:function(){return o(!1)}},(0,u.__)("No","betterlinks")))):(0,a.createElement)(a.Fragment,null,l,(0,a.createElement)(f.A,{onClickHandler:o,type:"delete",label:(0,u.__)("Delete Tag","betterlinks")})))};var x=l(46005),N=l(79296);function O(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,r)}return l}function C(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?O(Object(l),!0).forEach((function(t){(0,r.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):O(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}var A=function(e){var t=(0,a.useState)({}),l=(0,n.A)(t,2),r=l[0],c=l[1],i=(0,a.useState)(!1),o=(0,n.A)(i,2),s=o[0],m=o[1];return(0,a.createElement)(React.Fragment,null,(0,a.createElement)("div",{className:"btl-links-filter"},e.bulkActionData&&e.bulkActionData.selectedCount>0&&(0,a.createElement)("div",{className:"btl-bulk-actions"},(0,a.createElement)(x.Ay,{className:"btl-list-view-select",classNamePrefix:"btl-react-select",defaultValue:{value:"",label:(0,u.__)("Bulk Actions","betterlinks")},value:null!=r&&r.value?r:{value:"",label:(0,u.__)("Bulk Actions","betterlinks")},options:[{value:"delete",label:(0,u.__)("Delete","betterlinks")}],onChange:function(e){return c(e)}}),(0,a.createElement)("div",{className:"btl-tooltip"},(0,a.createElement)("button",{className:"btl-link-apply-button",onClick:function(){return function(t){if("delete"!==r.value)return m(!0);m(!1);var l=t.selectedRows.map((function(e){return{tag_id:e.id||e.ID}}));return e.delete_tag(l,r),c({}),e.setToggledClearRows()}(e.bulkActionData)}},(0,u.__)("Apply","betterlinks")),s&&"delete"!==(null==r?void 0:r.value)&&(0,a.createElement)("span",{className:"btl-tooltiptext"},(0,u.__)("Please Select Action","betterlinks")))),e.children))};const j=(0,c.Ng)((function(e){return{terms:e.terms}}),(function(e){return{fetch_all_tags:(0,i.zH)(o.XN,e),fetch_terms_data:(0,i.zH)(o.M3,e),delete_tag:(0,i.zH)(o.t6,e)}}))((function(e){var t=(0,a.useState)({}),l=(0,n.A)(t,2),r=l[0],c=l[1],i=(0,a.useState)(!1),o=(0,n.A)(i,2),m=o[0],d=o[1],h=(0,a.useState)(""),p=(0,n.A)(h,2),y=p[0],g=p[1],v=(0,a.useState)(null),f=(0,n.A)(v,2),O=f[0],j=f[1],S=e.terms,D=S.tags,P=S.tag_analytics;(0,a.useEffect)((function(){D||(e.fetch_all_tags(),e.fetch_terms_data())}),[]);var T,F,L,H,R=[{name:(0,u.__)("Tags","betterlinks"),selector:"tags",sortable:!1,cell:function(e){return(0,a.createElement)(E,{tags:D||[],icon:!0,row:e},(0,a.createElement)("div",{style:{textDecoration:"underline"}},e.term_name))}},C(C({name:(0,u.__)("Link Count","betterlinks"),selector:"link_count"},b.uL&&{sortFunction:(0,b.PH)("link_count")}),{},{cell:function(e){return(0,a.createElement)("div",null,+((null==e?void 0:e.link_count)||0))}}),{name:(0,u.__)("Analytics","betterlinks"),selector:"link_count",sortable:!1,cell:function(e){var t,l,r=(null==P||null===(t=P.total_clicks)||void 0===t?void 0:t[e.id])||0,n=(null==P||null===(l=P.unique_clicks)||void 0===l?void 0:l[e.id])||0;return(0,a.createElement)("div",null,(0,a.createElement)("button",{className:"dnd-link-button btl-tooltip"},(0,a.createElement)("span",{className:"btl-tooltiptext"},"Clicks: ",r," / Unique Clicks: ",n),r>0?(0,a.createElement)(N.N_,{to:b.Y3+"admin.php?page=betterlinks-analytics&tag_id="+e.id},r,"/",n):(0,a.createElement)("span",null,r,"/",n)))}},{name:(0,u.__)("Action","betterlinks"),selector:"link_count",sortable:!1,cell:function(t){return(0,a.createElement)(w,{delete_tag:function(){return e.delete_tag([{tag_id:t.id||t.ID}])}},(0,a.createElement)(E,{tags:D||[],icon:!0,row:t}))}}],q=(0,a.createElement)(A,{bulkActionData:r,setToggledClearRows:function(){return d(!m)},delete_tag:e.delete_tag},(0,a.createElement)("div",{className:"btl-autolink-filter btl-click-filter"},(0,a.createElement)("input",{id:"search_autolink",type:"search",placeholder:"Search Tags",value:y,onChange:function(e){g(e.target.value)}})),(0,a.createElement)(x.Ay,{className:"btl-list-view-select btl-shortable-filter",classNamePrefix:"btl-react-select",placeholder:"Sort by Clicks",options:[{value:"total_clicks-desc",label:(0,u.__)("Most Clicks","betterlinks")},{value:"total_clicks-asc",label:(0,u.__)("Least Clicks","betterlinks")},{value:"unique_clicks-desc",label:(0,u.__)("Most Unique Clicks","betterlinks")},{value:"unique_clicks-asc",label:(0,u.__)("Least Unique Clicks","betterlinks")}],value:O,onChange:function(e){return j(e)},isClearable:!0}));return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(s.A,{label:(0,u.__)("Manage Tags","betterlinks"),render:function(){return(0,a.createElement)(E,{tags:D})}}),(0,a.createElement)("div",{className:"btl-list-view"},D?(0,a.createElement)(_.Ay,{className:"btl-list-view-table",columns:R,data:(F=new RegExp(y,"gi"),L=null===(T=D||[])||void 0===T?void 0:T.filter((function(e){return((null==e?void 0:e.term_slug)||"").match(F)})),H=(0,b.xv)(null==O?void 0:O.value,L,P),Array.isArray(H)?H:D),pagination:!0,subHeader:!0,highlightOnHover:!0,subHeaderComponent:q,persistTableHead:!0,selectableRows:!0,selectableRowsVisibleOnly:!0,onSelectedRowsChange:function(e){return c(e)},clearSelectedRows:m}):(0,a.createElement)(a.Fragment,null,(0,a.createElement)(k.A,null))))}))},43516:(e,t,l)=>{l.d(t,{Ay:()=>o});var r=l(51609),n=function(){return n=Object.assign||function(e){for(var t,l=1,r=arguments.length;l<r;l++)for(var n in t=arguments[l])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},n.apply(this,arguments)},a=function(e){var t=e.animate,l=void 0===t||t,a=e.animateBegin,c=e.backgroundColor,i=void 0===c?"#f5f6f7":c,o=e.backgroundOpacity,s=void 0===o?1:o,u=e.baseUrl,m=void 0===u?"":u,d=e.children,b=e.foregroundColor,h=void 0===b?"#eee":b,p=e.foregroundOpacity,y=void 0===p?1:p,g=e.gradientRatio,v=void 0===g?2:g,f=e.gradientDirection,E=void 0===f?"left-right":f,_=e.uniqueKey,k=e.interval,w=void 0===k?.25:k,x=e.rtl,N=void 0!==x&&x,O=e.speed,C=void 0===O?1.2:O,A=e.style,j=void 0===A?{}:A,S=e.title,D=void 0===S?"Loading...":S,P=e.beforeMask,T=void 0===P?null:P,F=function(e,t){var l={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(l[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(l[r[n]]=e[r[n]])}return l}(e,["animate","animateBegin","backgroundColor","backgroundOpacity","baseUrl","children","foregroundColor","foregroundOpacity","gradientRatio","gradientDirection","uniqueKey","interval","rtl","speed","style","title","beforeMask"]),L=_||Math.random().toString(36).substring(6),H=L+"-diff",R=L+"-animated-diff",q=L+"-aria",M=N?{transform:"scaleX(-1)"}:null,V="0; "+w+"; 1",B=C+"s",z="top-bottom"===E?"rotate(90)":void 0;return(0,r.createElement)("svg",n({"aria-labelledby":q,role:"img",style:n(n({},j),M)},F),D?(0,r.createElement)("title",{id:q},D):null,T&&(0,r.isValidElement)(T)?T:null,(0,r.createElement)("rect",{role:"presentation",x:"0",y:"0",width:"100%",height:"100%",clipPath:"url("+m+"#"+H+")",style:{fill:"url("+m+"#"+R+")"}}),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:H},d),(0,r.createElement)("linearGradient",{id:R,gradientTransform:z},(0,r.createElement)("stop",{offset:"0%",stopColor:i,stopOpacity:s},l&&(0,r.createElement)("animate",{attributeName:"offset",values:-v+"; "+-v+"; 1",keyTimes:V,dur:B,repeatCount:"indefinite",begin:a})),(0,r.createElement)("stop",{offset:"50%",stopColor:h,stopOpacity:y},l&&(0,r.createElement)("animate",{attributeName:"offset",values:-v/2+"; "+-v/2+"; "+(1+v/2),keyTimes:V,dur:B,repeatCount:"indefinite",begin:a})),(0,r.createElement)("stop",{offset:"100%",stopColor:i,stopOpacity:s},l&&(0,r.createElement)("animate",{attributeName:"offset",values:"0; 0; "+(1+v),keyTimes:V,dur:B,repeatCount:"indefinite",begin:a})))))},c=function(e){return e.children?(0,r.createElement)(a,n({},e)):(0,r.createElement)(i,n({},e))},i=function(e){return(0,r.createElement)(c,n({viewBox:"0 0 476 124"},e),(0,r.createElement)("rect",{x:"48",y:"8",width:"88",height:"6",rx:"3"}),(0,r.createElement)("rect",{x:"48",y:"26",width:"52",height:"6",rx:"3"}),(0,r.createElement)("rect",{x:"0",y:"56",width:"410",height:"6",rx:"3"}),(0,r.createElement)("rect",{x:"0",y:"72",width:"380",height:"6",rx:"3"}),(0,r.createElement)("rect",{x:"0",y:"88",width:"178",height:"6",rx:"3"}),(0,r.createElement)("circle",{cx:"20",cy:"20",r:"20"}))};const o=c}}]);