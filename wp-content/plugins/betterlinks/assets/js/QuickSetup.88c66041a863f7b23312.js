"use strict";(globalThis.webpackChunkbetterlinks=globalThis.webpackChunkbetterlinks||[]).push([[899],{34958:(e,t,n)=>{n.d(t,{A:()=>c});var l=n(3453),a=n(51609),r=n(5556),i=n.n(r),s=n(19735),o={siteUrl:i().string,shortUrl:i().string};function c(e){e.siteUrl;var t=e.shortUrl,n=(0,a.useState)(!1),r=(0,l.A)(n,2),i=r[0],o=r[1];return(0,a.createElement)("button",{type:"button",onClick:function(){return function(e){(0,s.Nj)(e),o(!0)}(t)},className:"btl-link-copy-button"},i?(0,a.createElement)("span",{className:"dashicons dashicons-yes"}):(0,a.createElement)("i",{className:"btl btl-copy"}))}c.propTypes=o},99255:(e,t,n)=>{n.d(t,{A:()=>p});var l=n(64467),a=n(3453),r=n(51609),i=n.n(r),s=n(4949),o=n(46005),c=n(27723),u=n(19735);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const p=function(e){var t=(0,s.Mt)(e.name),n=(0,a.A)(t,3),l=n[0],m=n[2].setValue,p=(0,r.useState)("cloak"!=(null==l?void 0:l.value)&&u.JT?l:{label:(0,c.__)("307 (Temporary)","betterlinks"),value:"307"}),b=(0,a.A)(p,2),f=b[0],_=b[1];return(0,r.useEffect)((function(){"pro"===(null==l?void 0:l.value)?(m(null==f?void 0:f.value,!1),e.setFieldValue(l.name,null==f?void 0:f.value)):_((e.value||[]).find((function(e){return e.value==l.value})))}),[]),(0,r.createElement)(i().Fragment,null,(0,r.createElement)(o.Ay,{className:"btl-modal-select--full ".concat(e.value&&e.value.find((function(e){return"pro"==e.value}))?"btl-modal-select-need-pro-teaser":""),classNamePrefix:"btl-react-select",id:l.id,name:l.name,defaultValue:e.value&&e.value.filter((function(t){return t.value==(e.defaultValue||"307")})),onChange:function(t){return null==t?e.setFieldValue(l.name,""):(null!=e&&e.isQuickSetup&&("pro"===(null==t?void 0:t.value)?(e.setUpgradeToProModal(!0),m(null==f?void 0:f.value,l.value),e.setFieldValue(l.name,l.value)):(e.setFieldValue(l.name,null==t?void 0:t.value),_((e.value||[]).find((function(e){return e.value==t.value})))),null==e||e.setSettings((function(n){return d(d({},n),{},{redirect_type:e.isMulti?t.map((function(e){return e.value})):"pro"!==t.value?t.value:l.value})}))),e.setFieldValue(l.name,e.isMulti?t.map((function(e){return e.value})):"pro"!==t.value?t.value:l.value))},options:e.value,value:f,isMulti:e.isMulti}))}},18767:(e,t,n)=>{n.d(t,{A:()=>m});var l=n(64467),a=n(3453),r=n(51609),i=n(4949),s=n(46005),o=n(19735);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const m=function(e){var t=(0,i.Mt)(e.name),n=(0,a.A)(t,3),l=n[0],c=n[2].setValue,m=["cloak","pro"].includes(l.value)&&!o.JT,d=m?"307":l.value;return m&&c("307"),(0,r.createElement)(React.Fragment,null,(0,r.createElement)(s.Ay,{className:"btl-modal-select--full ".concat(e.value&&e.value.find((function(e){return"pro"==e.value}))?"btl-modal-select-need-pro-teaser":""),classNamePrefix:"btl-react-select",id:l.id,name:l.name,defaultValue:e.value&&e.value.filter((function(e){return e.value==(d||"307")})),onChange:function(t){return null==t?e.setFieldValue(l.name,""):(null!=e&&e.isQuickSetup&&(null==e||e.setLinkOptions((function(n){return u(u({},n),{},{redirect_type:e.isMulti?t.map((function(e){return e.value})):t.value})}))),e.setFieldValue(l.name,e.isMulti?t.map((function(e){return e.value})):t.value))},options:e.value,isMulti:e.isMulti,isDisabled:e.disabled,isOptionDisabled:function(e){return e.disabled}}))}},45464:(e,t,n)=>{n.d(t,{A:()=>p});var l=n(64467),a=n(3453),r=n(51609),i=n.n(r),s=n(27723),o=n(86663),c=n(2078),u=n(19735);function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e){var t=e.targetUrl,n=e.saveValueHandler,l=e.closeModalHandler,m=(0,r.useState)(!1),p=(0,a.A)(m,2),b=p[0],f=p[1],_=o.parseUrl(t,{parseFragmentIdentifier:!0}),k=(0,r.useState)({utm_source:_.query.utm_source?_.query.utm_source:"",utm_medium:_.query.utm_medium?_.query.utm_medium:"",utm_campaign:_.query.utm_campaign?_.query.utm_campaign:"",utm_term:_.query.utm_term?_.query.utm_term:"",utm_content:_.query.utm_content?_.query.utm_content:""}),v=(0,a.A)(k,2),g=v[0],E=v[1],h=function(){f(!0)};return(0,r.createElement)(i().Fragment,null,(0,r.createElement)(c.A,{isOpenModal:b,closeModal:function(){f(!1)}}),(0,r.createElement)("div",{className:"btl-modal-utm-builder"},(0,r.createElement)("h3",{className:"btl-modal-utm-builder__title"},(0,s.__)("UTM Builder","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext",style:{width:"220px"}},(0,s.__)("Add Campaign Parameters to Track Custom Campaigns","betterlinks")))),(0,r.createElement)("div",{className:"btl-modal-utm-builder__body"},!betterLinksHooks.applyFilters("isActivePro",!1)&&(0,r.createElement)("div",{className:"btl-modal-utm-builder__form-group btl-modal-utm-templates"},(0,r.createElement)("label",{htmlFor:"savedtemplate"},(0,s.__)("Template","betterlinks")),(0,r.createElement)("div",null,(0,r.createElement)("div",{name:"savedtemplate",id:"savedtemplate",onClick:function(){return h()}},(0,s.__)("Pick a Template","betterlinks")," ",(0,r.createElement)("img",{src:u.hq+"assets/images/locked.svg",alt:"locked",style:{marginLeft:5}})))),(0,r.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,r.createElement)("label",{htmlFor:"utmCampaign"},(0,s.__)("Campaign","betterlinks")),(0,r.createElement)("div",null,(0,r.createElement)("input",{id:"utmCampaign",value:g.utm_campaign,onChange:function(e){return E(d(d({},g),{},{utm_campaign:e.target.value}))},type:"text",name:"utm_campaign",placeholder:(0,s.__)("e.g: Example-campaign","betterlinks")}))),(0,r.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,r.createElement)("label",{htmlFor:"utmMedium"},(0,s.__)("Medium","betterlinks")),(0,r.createElement)("div",null,(0,r.createElement)("input",{id:"utmMedium",value:g.utm_medium,onChange:function(e){return E(d(d({},g),{},{utm_medium:e.target.value}))},type:"text",name:"utm_medium",placeholder:(0,s.__)("e.g: cpc, banner, email","betterlinks")}))),(0,r.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,r.createElement)("label",{htmlFor:"utmSource"},(0,s.__)("Source","betterlinks")),(0,r.createElement)("div",null,(0,r.createElement)("input",{id:"utmSource",value:g.utm_source,onChange:function(e){return E(d(d({},g),{},{utm_source:e.target.value}))},type:"text",name:"utm_source",placeholder:(0,s.__)("e.g: Twitter, Facebook","betterlinks")}))),(0,r.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,r.createElement)("label",{htmlFor:"utmTerm"},(0,s.__)("Term","betterlinks")),(0,r.createElement)("div",null,(0,r.createElement)("input",{id:"utmTerm",value:g.utm_term,onChange:function(e){return E(d(d({},g),{},{utm_term:e.target.value}))},type:"text",name:"utm_term",placeholder:(0,s.__)("e.g: paid keywords","betterlinks")}))),(0,r.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,r.createElement)("label",{htmlFor:"utmContent"},(0,s.__)("Content","betterlinks")),(0,r.createElement)("div",null,(0,r.createElement)("input",{id:"utmContent",value:g.utm_content,onChange:function(e){return E(d(d({},g),{},{utm_content:e.target.value}))},type:"text",name:"utm_content",placeholder:(0,s.__)("e.g: text AD name","betterlinks")}))),(0,r.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,r.createElement)("button",{type:"button",onClick:function(){return e=o.exclude(t,["utm_source","utm_medium","utm_campaign","utm_term","utm_content"]),n("target_url",o.stringifyUrl({url:e,query:Object.entries(g).reduce((function(e,t){var n=(0,a.A)(t,2),l=n[0],r=n[1];return r&&""!=r?(e[l]=r,e):e}),{})})),void l();var e}},(0,s.__)("Save Link","betterlinks")),!betterLinksHooks.applyFilters("isActivePro",!1)&&(0,r.createElement)("button",{type:"button",onClick:function(e){return h()}},(0,s.__)("Save New Template","betterlinks")," ",(0,r.createElement)("img",{src:u.hq+"assets/images/locked-white.svg",alt:"locked"}))))))}p.propTypes={}},5679:(e,t,n)=>{n.r(t),n.d(t,{default:()=>pe});var l=n(64467),a=n(3453),r=n(51609),i=n(27723),s=n(2078),o=n(10467),c=n(89280),u=n.n(c),m=n(50378),d=n(65146),p=n(24434),b=n(68673),f=n(19735);const _=function(){var e=(0,r.useContext)(b.P),t=e.setActiveStep,n=e.setClientConsent,l=(0,r.useState)(!1),s=(0,a.A)(l,2),c=s[0],m=s[1],d=function(){var e=(0,o.A)(u().mark((function e(t){var l,a;return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,f.In)({action:"betterlinks__client_consent",opt_in_value:t});case 2:a=e.sent,n(null===(l=a.data)||void 0===l?void 0:l.success);case 4:case"end":return e.stop()}}),e)})));return function(_x){return e.apply(this,arguments)}}();return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"getting-started"},(0,r.createElement)("h3",null,(0,i.__)("Getting Started","betterlinks")),(0,r.createElement)("p",null,(0,i.__)("Easily get started with the easy setup wizard and complete setting up to streamline website's link management strategy.","betterlinks")),(0,r.createElement)("iframe",{width:450,height:258,src:"https://www.youtube-nocookie.com/embed/ZJqBrFhQC1A",title:"YouTube video player",frameBorder:0,allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",referrerPolicy:"strict-origin-when-cross-origin",allowFullScreen:!0}),(0,r.createElement)("div",{className:"actions"},(0,r.createElement)("p",null,(0,r.createElement)("span",null,(0,i.__)("By clicking this button I am allowing this app to collect my information.","betterlinks"),(0,r.createElement)("span",{className:"what-we-collect",onClick:function(e){e.preventDefault(),m(!c)}},(0,i.__)("What We Collect?","betterlinks"))),(0,r.createElement)("span",{className:"consent-info ".concat(c?"show":"hide")},(0,r.createElement)("span",null,(0,i.__)("We collect non-sensitive diagnostic data and plugin usage information. Your site URL, WordPress & PHP version, plugins & themes and email address to send you the discount coupon. This data lets us make sure this plugin always stays compatible with the most popular plugins and themes. No spam, I promise.","betterlinks")))),(0,r.createElement)("button",{className:"button button-primary",onClick:function(){d("yes"),t(1)}},(0,i.__)("Proceed to Next Step","betterlinks")),(0,r.createElement)("a",{href:"#",onClick:function(e){e.preventDefault(),d("no"),t(1)}},(0,i.__)("Skip This Step","betterlinks")))))};var k=n(45458),v=n(40150),g=n(99255),E=n(4949),h=n(67783);function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function N(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const O=function(){var e=(0,r.useContext)(b.P),t=e.settings,n=e.setSettings,a=e.modal,s=a.setUpgradeToProModal,o=a.openUpgradeToProModal,c=function(e,t){var a;e.setFieldValue(t,!(null!==(a=e.values)&&void 0!==a&&a[t])),n((function(n){var a;return N(N({},n),{},(0,l.A)({},t,!(null!==(a=e.values)&&void 0!==a&&a[t])))}))};return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"configuration"},(0,r.createElement)("div",{className:"header"},(0,r.createElement)("h3",null,(0,i.__)("Configuration","betterlinks")),(0,r.createElement)("p",null,(0,i.__)("Let’s adjust core settings to match your preferences, including link options, redirect types and tracking options, for a seamless setup experience.","betterlinks"))),(0,r.createElement)("div",{className:"option"},(0,r.createElement)(E.l1,{initialValues:N({},t)},(function(e){var l,a;return(0,r.createElement)(E.lV,null,(0,r.createElement)("div",{className:"btl-tab-panel-inner"},(0,r.createElement)("span",{className:"btl-form-group"},(0,r.createElement)("label",{className:"btl-form-label"},(0,i.__)("Link Options","betterlinks")),(0,r.createElement)("div",{className:"link-options__body"},(0,r.createElement)("label",{className:"btl-checkbox-field block"},(0,r.createElement)(E.D0,{className:"btl-check",name:"nofollow",type:"checkbox",onChange:function(){c(e,"nofollow")}}),(0,r.createElement)("span",{className:"text"},(0,i.__)("No Follow","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will add nofollow attribute to your link. (Recommended)","betterlinks"))))),(0,r.createElement)("label",{className:"btl-checkbox-field block"},(0,r.createElement)(E.D0,{className:"btl-check",name:"sponsored",type:"checkbox",onChange:function(){c(e,"sponsored")}}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Sponsored","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will add sponsored attribute to your link. (Recommended for Affiliate links)","betterlinks"))))),(0,r.createElement)("label",{className:"btl-checkbox-field block"},(0,r.createElement)(E.D0,{className:"btl-check",name:"param_forwarding",type:"checkbox",onChange:function(){return c(e,"param_forwarding")}}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Parameter Forwarding","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will pass the parameters you have set in the target URL","betterlinks"))))),(0,r.createElement)("label",{className:"btl-checkbox-field block"},(0,r.createElement)(E.D0,{className:"btl-check",name:"track_me",type:"checkbox",onChange:function(){return c(e,"track_me")}}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Tracking","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will let you check Analytics report of your links","betterlinks"))))))),(0,r.createElement)("span",{className:"btl-form-group btl-form-group--top"},(0,r.createElement)("label",{className:"btl-form-label"},(0,i.__)("Link Prefix","betterlinks")),(0,r.createElement)("div",{className:"link-options__body",style:{flexDirection:"column"}},(0,r.createElement)("div",{style:{maxWidth:"200px"}},(0,r.createElement)(E.D0,{className:"btl-text-field",name:"prefix",value:null!==(l=null===(a=e.values)||void 0===a?void 0:a.prefix)&&void 0!==l?l:"go",type:"text",onChange:function(t){e.setFieldValue("prefix",t.target.value),n((function(e){return N(N({},e),{},{prefix:t.target.value})}))}})),(0,r.createElement)("div",{className:"short-description"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"})," ",(0,i.__)("The prefix will be added before your Shortened URL’s slug eg.","betterlinks"),betterLinksHooks.applyFilters("site_url",f.IV),e.values.prefix&&(0,r.createElement)(r.Fragment,null,"/",(0,r.createElement)("strong",null,e.values.prefix)),(0,i.__)("/your-affiliate-link-name.","betterlinks")))),(0,r.createElement)("span",{className:"btl-form-group"},(0,r.createElement)("label",{className:"btl-form-label"},(0,i.__)("Redirect Type","betterlinks")),(0,r.createElement)(g.A,{className:"btl-modal-select--full",classNamePrefix:"btl-react-select",id:"redirect_type",name:"redirect_type",setUpgradeToProModal:s,value:[].concat((0,k.A)(h.XN),[{value:f.JT?"cloak":"pro",label:f.JT?(0,i.__)("Cloaked","betterlinks"):(0,r.createElement)(r.Fragment,null,(0,i.__)("Cloaked","betterlinks"),(0,r.createElement)(v.A,null))}]),defaultValue:"cloak"!=t.redirect_type||f.JT?t.redirect_type:"307",setFieldValue:e.setFieldValue,isMulti:!1,isQuickSetup:!0,setSettings:n})),(0,r.createElement)("span",{className:"btl-form-group"},(0,r.createElement)("label",{className:"btl-form-label"},(0,i.__)("Wildcards","betterlinks")),(0,r.createElement)("div",{className:"link-options__body"},(0,r.createElement)("label",{className:"btl-checkbox-field block"},(0,r.createElement)(E.D0,{className:"btl-check",name:"wildcards",type:"checkbox",onChange:function(){return c(e,"wildcards")}}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Use Wildcards?","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("To use wildcards, put an asterisk (*) after the folder name that you want to redirect.","betterlinks"))))))),betterLinksHooks.applyFilters("BetterLinksQuickSetupConfig",(0,r.createElement)("span",{className:"btl-form-group btl-form-group--teaser"},(0,r.createElement)("label",{className:"btl-form-label",onClick:o},(0,i.__)("Force HTTPS","betterlinks")," ",(0,r.createElement)(v.A,null)),(0,r.createElement)("div",{className:"link-options__body"},(0,r.createElement)("label",{className:"btl-checkbox-field block",onClick:o},(0,r.createElement)("input",{className:"btl-check",name:"force_https",type:"checkbox",disabled:!0}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Enable HTTPS Redirection","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will allow you to redirect your Target URLs in HTTPS.","betterlinks"))))))),N(N({},e),{},{handleOptions:c}))))})))))};var w=n(61383),P=function(){return(0,r.createElement)("svg",{className:"MuiSvgIcon-root jss360",focusable:"false",viewBox:"0 0 24 24","aria-hidden":"true"},(0,r.createElement)("path",{d:"M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"}))},j=function(){return(0,r.createElement)("svg",{className:"MuiSvgIcon-root jss360 MuiSvgIcon-fontSizeSmall",focusable:"false",viewBox:"0 0 24 24","aria-hidden":"true"},(0,r.createElement)("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}))},S=function(){return(0,r.createElement)("svg",{className:"MuiSvgIcon-root",focusable:"false",viewBox:"0 0 24 24","aria-hidden":"true"},(0,r.createElement)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"}))},C=function(e){var t=e.status;return{"in-progress":(0,r.createElement)(P,null),failed:(0,r.createElement)(j,null),complete:(0,r.createElement)(S,null)}[t]},x=n(20312),A=n.n(x),D=n(49924),M=n(68238),F=function(e){return function(t){t({type:"UPDATE_OPTIONS",payload:e})}},T=function(e){return function(t){t({type:"UPDATE_RESULTS",payload:e})}};const L=(0,D.Ng)((function(e){return{quickSetup:e.quickSetup}}),(function(e){return{update_quick_setup:(0,M.zH)(F,e)}}))((function(e){var t,n,l,a,s,o,c=e.quickSetup,u=(0,r.useContext)(b.P),m=u.modalIsOpen,d=u.setModalIsOpen,p=u.setModalConfirm,_=u.migrationSettings,k=(u.migrationStatus,u.setActiveStep),v=function(){d(!1),p(!1)},g=c.results,E=g.pl,h=g.ta,y=g.s3r,N=Object.keys(status).filter((function(e){return _[e]})).every(Boolean);return(0,r.createElement)(A(),{isOpen:m,onRequestClose:v,style:f.Qu,ariaHideApp:!1,parentSelector:function(){return document.querySelector(".migration")}},(0,r.createElement)("div",{className:"btl-manage-tags-form"},(0,r.createElement)("span",{className:"btl-close-modal",onClick:v},(0,r.createElement)("i",{className:"btl btl-cancel"})),(0,r.createElement)("div",{className:"btl-confirmation-alert"},(0,r.createElement)("h3",{className:"btl-modal-utm-builder__title",style:{textAlign:"center",fontSize:"22px"}},(0,i.__)("Migration Logs","betterlinks")),(0,r.createElement)("div",{className:"btl-migration-logs"},!(g.pl||g.ta||g.s3r)&&(0,r.createElement)("div",{className:"btl-migration-logs__item"},(0,r.createElement)("span",null,(0,i.__)("Running Migration....","betterlinks"))),h&&(0,r.createElement)("div",{className:"btl-migration-logs__item"},(0,r.createElement)("span",null,(0,r.createElement)("strong",null,(0,i.__)("Thirsty Affiliates:","betterlinks"))),null==h||null===(t=h.links)||void 0===t?void 0:t.map((function(e){return(0,r.createElement)("span",null,e)}))),E&&(0,r.createElement)("div",{className:"btl-migration-logs__item"},(0,r.createElement)("span",null,(0,r.createElement)("strong",null,(0,i.__)("Pretty Links:","betterlinks"))),null==E||null===(n=E.links)||void 0===n?void 0:n.map((function(e){return(0,r.createElement)("span",null,e)})),(null==E?void 0:E.duplicate_migration_detected__so_prevented_it_here)&&(0,r.createElement)("span",null,(0,i.__)("Pretty Links:Duplicate migration detected. So prevented it here.","betterlinks")),(null==E?void 0:E.btl_prettylinks_migration_running_in_background)&&(0,r.createElement)("span",null,(0,i.__)("Pretty Links migration running in background.","betterlinks"))),((null==y||null===(l=y.links)||void 0===l?void 0:l.length)>0||(null==y||null===(a=y.wildcard)||void 0===a?void 0:a.length)>0)&&(0,r.createElement)("div",{className:"btl-migration-logs__item"},(0,r.createElement)("span",null,(0,r.createElement)("strong",null,(0,i.__)("Simple 301 Redirects:","betterlinks"))),null==y||null===(s=y.links)||void 0===s?void 0:s.map((function(e){return(0,r.createElement)("span",null,e)})),null==y||null===(o=y.wildcard)||void 0===o?void 0:o.map((function(e){return(0,r.createElement)("span",null,e)})))),(0,r.createElement)("div",{className:"next-tab-confirmation"},(0,r.createElement)("button",{className:"button",onClick:v},(0,i.__)("Cancel","betterlinks")),(0,r.createElement)("button",{className:"button button-primary",disabled:!N,onClick:function(){k(3),d(!1)}},(0,i.__)("Continue","betterlinks"))))))}));function q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function V(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?q(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const H=function(){var e=(0,r.useContext)(b.P),t=e.migrationSettings,n=e.setMigrationSettings,a=e.migrationStatus,s=t.simple301redirects,o=t.thirstyaffiliates,c=t.prettylinks,u=function(e){n((function(t){return V(V({},t),{},(0,l.A)({},e,!t[e]))}))};return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"migration"},(0,r.createElement)(L,null),(0,r.createElement)("div",{className:"header"},(0,r.createElement)("h3",null,(0,i.__)("Migration","betterlinks")),(0,r.createElement)("p",null,(0,i.__)("Already using another link management tool? Let’s import existing links and data to BetterLinks without facing any hassle.","betterlinks"))),(0,r.createElement)("div",{className:"option"},f.MH.simple301redirects&&(0,r.createElement)(U,{title:(0,i.__)("Simple 301 Redirects","betterlinks"),show:s,onClick:function(){return u("simple301redirects")},status:a.simple301redirects}),f.MH.thirstyaffiliates&&(0,r.createElement)(U,{title:(0,i.__)("ThirstyAffiliates","betterlinks"),show:o,onClick:function(){return u("thirstyaffiliates")},status:a.thirstyaffiliates}),f.MH.prettylinks&&(0,r.createElement)(U,{title:(0,i.__)("Pretty Links","betterlinks"),show:c,onClick:function(){return u("prettylinks")},status:a.prettylinks}))))};var U=function(e){var t=e.title,n=e.show,l=void 0!==n&&n,a=e.onClick,s=e.status,o=void 0===s?"":s,c={complete:(0,i.__)("Complete","betterlinks"),"in-progress":(0,i.__)("In Progress","betterlinks"),failed:(0,i.__)("Failed","betterlinks")};return(0,r.createElement)("div",{className:"plugin-single",style:{cursor:"pointer",borderColor:l?"#3f51b5":"transparent"}},(0,r.createElement)("div",{className:"left-side"},(0,r.createElement)(w.A,{color:"primary",checked:l,onClick:a}),(0,r.createElement)("span",null,t),""!==o&&(0,r.createElement)("div",{className:"badge ".concat(o)},(0,r.createElement)(C,{status:o}),(0,r.createElement)("span",null,c[o]))),"failed"===o&&(0,r.createElement)("div",{className:"right-side failed"},(0,r.createElement)("button",{className:"button button-failed"},(0,r.createElement)(C,{status:"in-progress"}),(0,r.createElement)("span",null,(0,i.__)("Retry","betterlinks")))))},I=n(18767),R=n(45464),z=n(34958);function B(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?B(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):B(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Q=(0,D.Ng)((function(e){var t;return{duplicateLink:null===(t=e.quickSetup)||void 0===t?void 0:t.duplicateLink}}),(function(e){return{update_quick_setup:(0,M.zH)(F,e)}}))((function(e){var t=(0,r.useContext)(b.P),n=t.linkOptions,l=t.setLinkOptions,s=t.modal,o=t.settings,c=(0,r.useState)(!1),u=(0,a.A)(c,2),m=u[0],d=u[1],p=(0,r.useState)(!1),_=(0,a.A)(p,2),v=_[0],g=_[1],y=(0,r.useState)(!0),N=(0,a.A)(y,2),O=N[0],w=N[1],P=s.setUpgradeToProModal,j=s.openUpgradeToProModal,S=(0,r.useState)(J(J({},n),o)),C=(0,a.A)(S,2),x=C[0],D=(C[1],betterLinksHooks.applyFilters("isDisableLinkFormEditView",!1,x)),M=function(){g(!0)},F=function(){w(!0),g(!1)},T=function(){f.JT?(w(!1),M()):j()},L=function(){e.update_quick_setup({duplicateLink:!1})};return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"create-links"},(0,r.createElement)("div",{className:"header"},(0,r.createElement)("h3",null,(0,i.__)("Create Link","betterlinks")),(0,r.createElement)("p",null,(0,i.__)("Let’s create a new link in seconds! Just set the slug, choose redirect rules, adjust link options and start tracking right away.","betterlinks"))),(0,r.createElement)("div",{className:"option"},(null==e?void 0:e.duplicateLink)&&(0,r.createElement)("div",{className:"duplicate-link-modal"},(0,r.createElement)(A(),{isOpen:e.duplicateLink,onRequestClose:L,style:f.Qu,ariaHideApp:!1,parentSelector:function(){return document.querySelector(".create-links .option")}},(0,r.createElement)("span",{className:"btl-close-modal",onClick:L},(0,r.createElement)("i",{className:"btl btl-cancel"})),(0,r.createElement)("h3",null,(0,i.__)("Duplicate Link","betterlinks")),(0,r.createElement)("p",null,(0,i.__)("The short URL you entered is already exists. Please enter a different short URL.","betterlinks")))),(0,r.createElement)(E.l1,{initialValues:betterLinksHooks.applyFilters("linkFormInitialValues",x),onSubmit:function(){}},(function(e){var t=e.errors;return(0,r.createElement)(E.lV,{className:"ReactModal__Content"},(0,r.createElement)("div",{className:"btl-entry-content"},(0,r.createElement)(A(),{isOpen:v,onRequestClose:F,style:f.Qu,ariaHideApp:!1},(0,r.createElement)("span",{className:"btl-close-modal",onClick:F},(0,r.createElement)("i",{className:"btl btl-cancel"})),O?(0,r.createElement)(r.Fragment,null,betterLinksHooks.applyFilters("linksUTMBuilderField",(0,r.createElement)(R.A,{targetUrl:e.values.target_url,saveValueHandler:e.setFieldValue,closeModalHandler:F}),e.values.target_url,e.setFieldValue,F)):(0,r.createElement)(React.Fragment,null,betterLinksHooks.applyFilters("linksBuiltInUTMBuilderField","",e.values.target_url,e.setFieldValue,F))),(0,r.createElement)("div",{className:"btl-entry-content-left",style:{marginBottom:"20px"}},(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label btl-required",htmlFor:"link_title"},(0,i.__)("Title","betterlinks")),(0,r.createElement)("div",{className:"btl-modal-form-title-wrapper"},(0,r.createElement)("div",{style:{display:"flex",flexDirection:"column",width:"100%"}},(0,r.createElement)(E.D0,{className:"btl-modal-form-control",id:"link_title",name:"link_title",disabled:D,onChange:function(t){var n=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;e("link_title",t);var l=(0,f.dM)(o,n||t);return l.length>0?(e("short_url",l),d(!1),l):""}(e.setFieldValue,t.target.value);e.setFieldValue("short_url",n),l((function(e){return J(J({},e),{},{link_title:t.target.value,short_url:n})}))},required:!0}),(null==t?void 0:t.link_title)&&(0,r.createElement)("span",{style:{color:"red"}},null==t?void 0:t.link_title)))),(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label btl-required",htmlFor:"redirect_type"},(0,i.__)("Redirect Type","betterlinks")),(0,r.createElement)(I.A,{id:"redirect_type",name:"redirect_type",value:[].concat((0,k.A)(h.XN),[{value:f.JT?"cloak":"pro",label:(0,i.__)("Cloaked","betterlinks"),disabled:!f.JT}]),setUpgradeToProModal:P,setFieldValue:e.setFieldValue,disabled:D,isMulti:!1,enable_password:!1,isQuickSetup:!0,setLinkOptions:l})),(0,r.createElement)("div",{className:"btl-modal-form-group btl-has-utm-button"},(0,r.createElement)("label",{className:"btl-modal-form-label btl-required",htmlFor:"target_url"},(0,i.__)("Target URL","betterlinks")),(0,r.createElement)(E.D0,{className:"btl-modal-form-control",id:"target_url",name:"target_url",onChange:function(t){var n=t.target.value.replace(/\s+/g,"");e.setFieldValue("target_url",n),l((function(e){return J(J({},e),{},{target_url:n})}))},placeholder:"",disabled:D,required:!0}),(0,r.createElement)("div",{className:"btl-utm-button-group"},(0,r.createElement)("button",{type:"button",className:"btl-utm-button",onClick:M,disabled:D},(0,i.__)("UTM","betterlinks")),f.JT?(0,r.createElement)("button",{type:"button",className:"btl-share-button",onClick:T,disabled:D},(0,r.createElement)("i",{className:"btl btl-share"})):(0,r.createElement)("button",{type:"button",className:"btl-share-button btl-share-button--locked",onClick:T,disabled:D},(0,r.createElement)("i",{className:"btl btl-share"}),(0,r.createElement)("img",{className:"locked",src:f.hq+"assets/images/lock-round.svg",alt:"icon"})))),(0,r.createElement)("div",{className:"btl-modal-shorturl-wrap"},(0,r.createElement)("div",{className:"btl-modal-form-group shorturl"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"short_url"},(0,i.__)("Shortened URL","betterlinks")),(0,r.createElement)("div",{className:m?"btl-link-field-copyable is-invalid":"btl-link-field-copyable"},(0,r.createElement)("span",{className:"btl-static-link"},f.IV+"/"),(0,r.createElement)(E.D0,{className:"btl-dynamic-link",id:"short_url",name:"short_url",onChange:function(t){var n=t.target.value.replace(/\s+/g,"-");e.setFieldValue("short_url",n),l((function(e){return J(J({},e),{},{short_url:n})})),d(!1)},disabled:D,required:!0}),(0,r.createElement)(z.A,{siteUrl:f.IV,shortUrl:e.values.short_url}))),1==m&&(0,r.createElement)("div",{className:"errorlog"},(0,i.__)("Already Exists","betterlinks")))),(0,r.createElement)("div",{className:"btl-entry-content-right"},(0,r.createElement)("div",{className:"link-options link-options--open"},(0,r.createElement)("button",{className:"link-options__head",type:"button"},(0,r.createElement)("h4",{className:"link-options__head--title"},(0,i.__)("Link Options","betterlinks"))),(0,r.createElement)("div",{className:"link-options__body"},(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(E.D0,{className:"btl-check",name:"nofollow",type:"checkbox",onChange:function(){e.setFieldValue("nofollow",!e.values.nofollow),l((function(t){return J(J({},t),{},{nofollow:!e.values.nofollow})}))},disabled:D}),(0,r.createElement)("span",{className:"text"},(0,i.__)("No Follow","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will add nofollow attribute to your link. (Recommended)","betterlinks"))))),(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(E.D0,{className:"btl-check",name:"sponsored",type:"checkbox",onChange:function(){e.setFieldValue("sponsored",!e.values.sponsored),l((function(t){return J(J({},t),{},{sponsored:!e.values.sponsored})}))},disabled:D}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Sponsored","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will add sponsored attribute to your link. (Recommended for Affiliate links)","betterlinks"))))),(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(E.D0,{className:"btl-check",name:"param_forwarding",type:"checkbox",onChange:function(){e.setFieldValue("param_forwarding",!e.values.param_forwarding),l((function(t){return J(J({},t),{},{param_forwarding:!e.values.param_forwarding})}))},disabled:D}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Parameter Forwarding","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will pass the parameters you have set in the target URL","betterlinks"))))),(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(E.D0,{className:"btl-check",name:"track_me",type:"checkbox",onChange:function(){e.setFieldValue("track_me",!e.values.track_me),l((function(t){return J(J({},t),{},{track_me:!e.values.track_me})}))},disabled:D}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Tracking","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will let you check Analytics report of your links","betterlinks"))))))))))})))))}));var W=n(14685);function Y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function G(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const X=(0,D.Ng)((function(e){var t,n;return{isCreated:null===(t=e.quickSetup)||void 0===t?void 0:t.isCreated,createdLink:null===(n=e.quickSetup)||void 0===n?void 0:n.createdLink}}),(function(e){return{update_quick_setup:(0,M.zH)(F,e)}}))((function(e){var t=(0,r.useState)(!1),n=(0,a.A)(t,2),l=n[0],s=n[1];(0,r.useEffect)((function(){window.scrollTo(0,0),e.update_quick_setup({isCreated:!1,duplicateLink:!1});var t=setTimeout((function(){o()}),150);return function(){clearTimeout(t)}}),[]);var o=function(){var e={origin:{y:.5,x:.55}};function t(t,n){(0,W.A)(G(G(G({},e),n),{},{particleCount:Math.floor(500*t)}))}t(.25,{spread:26,startVelocity:55}),t(.2,{spread:60}),t(.35,{spread:100,decay:.91,scalar:.8}),t(.1,{spread:120,startVelocity:25,decay:.92,scalar:1.2}),t(.1,{spread:120,startVelocity:45})};return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"finish"},(0,r.createElement)("div",{id:"confetti"}),(0,r.createElement)("div",{className:"header"},(0,r.createElement)("h3",null,(0,i.__)("Great Job!","betterlinks")),(0,r.createElement)("p",null,(0,i.__)("Link creation is complete and ready to help you, share, track, manage and optimize your links efficiently.","betterlinks")),(null==e?void 0:e.createdLink)&&Object.keys(e.createdLink).length&&(0,r.createElement)("div",{className:"btl-shortened-url"},(0,r.createElement)("span",null,"".concat(f.IV,"/").concat(e.createdLink.short_url)),(0,r.createElement)("div",null,(0,r.createElement)("button",{className:"btl-short-url-copy-button btl-tooltip",onClick:function(){s(!0),(0,f.lW)("".concat(f.IV,"/").concat(e.createdLink.short_url)),setTimeout((function(){s(!1)}),1e3)}},(0,r.createElement)("span",{className:"icon"},!l&&(0,r.createElement)("img",{width:15,src:f.hq+"/assets/images/copy-icon-1.svg",alt:"icon"}),l&&(0,r.createElement)("span",{className:"dashicons dashicons-yes"}))),(0,r.createElement)("a",{href:"".concat(f.IV,"/").concat(e.createdLink.short_url),target:"_blank",className:"dashicons dashicons"},(0,r.createElement)("img",{width:15,src:f.hq+"/assets/images/icons/target.svg"})))),(0,r.createElement)("img",{src:f.hq+"/assets/images/finish-setup.png",alt:"Setup Finish"}))))}));var Z=n(72505),K=n.n(Z);function $(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var te=function(){var e=(0,o.A)(u().mark((function e(t,n,l){return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:Object.entries(t).forEach((function(e){var t=(0,a.A)(e,2),r=t[0];t[1]&&(le(r,n,l),"prettylinks"===r&&le(r,n,l,"clicks"))}));case 1:case"end":return e.stop()}}),e)})));return function(_x,t,n){return e.apply(this,arguments)}}(),ne={prettylinks:"pl",simple301redirects:"s3r",thirstyaffiliates:"ta"},le=function(e,t,n){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"links",r=new FormData;"prettylinks"===e?r.append("action","betterlinks/admin/run_prettylinks_migration"):"simple301redirects"===e?r.append("action","betterlinks/admin/run_simple301redirects_migration"):"thirstyaffiliates"===e&&r.append("action","betterlinks/admin/run_thirstyaffiliates_migration"),r.append("security",f.sL),r.append("type",a),t((function(t){return ee(ee({},t),{},(0,l.A)({},e,"in-progress"))})),K().post(ajaxurl,r).then((function(a){if(a.data)return t((function(t){return ee(ee({},t),{},(0,l.A)({},e,"complete"))})),n((0,l.A)({},ne[e],a.data.data)),a.data;t((function(t){return ee(ee({},t),{},(0,l.A)({},e,"failed"))}))}),(function(e){console.log(e)}))},ae=n(7400),re=n(93348);function ie(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function se(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ie(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ie(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const oe=(0,D.Ng)((function(e){var t,n,l,a,r;return{isCreated:null===(t=e.quickSetup)||void 0===t?void 0:t.isCreated,duplicateLink:null===(n=e.quickSetup)||void 0===n?void 0:n.duplicateLink,ta:null===(l=e.quickSetup)||void 0===l?void 0:l.ta,pl:null===(a=e.quickSetup)||void 0===a?void 0:a.pl,s3r:null===(r=e.quickSetup)||void 0===r?void 0:r.s3r}}),(function(e){return{update_quick_setup:(0,M.zH)(F,e),update_migration_result:(0,M.zH)(T,e),add_new_link:(0,M.zH)(ae.A4,e)}}))((function(e){var t=(0,re.useHistory)(),n=Object.values(f.MH).some((function(e){return e}))?[(0,i.__)("Getting Started","betterlinks"),(0,i.__)("Configuration","betterlinks"),(0,i.__)("Migration","betterlinks"),(0,i.__)("Create Link","betterlinks"),(0,i.__)("Finish","betterlinks")]:[(0,i.__)("Getting Started","betterlinks"),(0,i.__)("Configuration","betterlinks"),(0,i.__)("Create Link","betterlinks"),(0,i.__)("Finish","betterlinks")],l=(0,r.useContext)(b.P),s=l.activeStep,c=l.setActiveStep,k=l.clientConsent,v=l.update_option,g=l.settings,E=l.linkOptions,h=l.setLinkOptions,y=l.setErrors,N=l.terms,w=l.migrationSettings,P=l.setModalIsOpen,j=l.modalConfirm,S=l.setModalConfirm,C=l.setMigrationStatus,x=(0,r.useState)(!1),A=(0,a.A)(x,2),D=A[0],M=A[1],F=Object.values(f.MH).some((function(e){return e}));(0,r.useEffect)((function(){e.isCreated&&c(4),j&&te(w,C,e.update_migration_result)}),[e.isCreated,s,j]);var T,L=function(e,t){/<script\b[^>]*>[\s\S]*?<\/script\b[^>]*>/.test(e.link_title)?t((function(e){return se(se({},e),{},{link_title:(0,i.__)("Please ensure the link title does not contain any script.","betterlinks")})})):q(e)},q=function(t){var n=t.short_url;t.short_url=n.substring(0,n.length-+(n.lastIndexOf("/")==n.length-1)),(0,f.X4)(null==t?void 0:t.short_url,null==t?void 0:t.ID,(function(){})).then((function(n){if(!n){if(!t.cat_id){var l,a=null==N||null===(l=N.terms)||void 0===l?void 0:l.find((function(e){return"uncategorized"==(null==e?void 0:e.term_slug)}));t.cat_id=(null==a?void 0:a.ID)||1}if(t.link_slug||(t.link_slug=(0,f.z9)(t.link_title)),isNaN(null==t?void 0:t.cat_id)&&(t.cat_slug=(0,f.z9)(t.cat_id)),t.wildcards=Number(t.short_url.includes("*")),t.cat_id){var r=t.link_title.trim();r&&(t.link_title=r,e.add_new_link(t).then((function(t){var n;null!=t&&t.data&&(e.update_quick_setup({isCreated:!0,createdLink:(null===(n=t.data)||void 0===n?void 0:n.data)||null}),c(F?4:3))})).catch((function(e){return console.log("---error (submitHandler)--",{error:e})})))}}e.update_quick_setup({duplicateLink:n})}))},V=function(){var e=(0,o.A)(u().mark((function e(){var n,l,a;return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,f.In)({action:"betterlinks__complete_setup"});case 2:l=e.sent,"complete"===(null==(a=null===(n=l.data)||void 0===n?void 0:n.data)?void 0:a.result)&&(t.push(f.Y3+"admin.php?page=betterlinks"),t.go(0));case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return(0,r.useEffect)((function(){if(F&&2==s){var e=!Object.keys(f.MH).filter((function(e){return f.MH[e]})).some((function(e){return w[e]}));M(e)}else M(!!(F&&3==s||!F&&2==s)&&(""==E.link_title||""==E.target_url))}),[s,w,E.link_title,E.target_url]),(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"btl-quick-setup"},(0,r.createElement)(m.A,{activeStep:s,connector:(0,r.createElement)("span",{className:"dashicons dashicons-arrow-right-alt2"})},n.map((function(e,t){return(0,r.createElement)(d.A,se({key:e},{}),(0,r.createElement)(p.A,se({},{}),e))}))),(0,r.createElement)("div",{className:"btl-setup-steps"},(T=s,Object.values(f.MH).some((function(e){return e}))?{0:(0,r.createElement)(_,null),1:(0,r.createElement)(O,null),2:(0,r.createElement)(H,null),3:(0,r.createElement)(Q,null),4:(0,r.createElement)(X,null)}[T]:{0:(0,r.createElement)(_,null),1:(0,r.createElement)(O,null),2:(0,r.createElement)(Q,null),3:(0,r.createElement)(X,null)}[T])),(0,r.createElement)("div",{className:"btl-setup-slider"},(0,r.createElement)("div",null,[2,3].includes(s)&&(3!=s||F)&&(0,r.createElement)("a",{className:"skip",href:"#",disabled:0===s,onClick:function(e){e.preventDefault(),c(s+1)}},(0,i.__)("Skip This Step","betterlinks"))),s>0?(0,r.createElement)("div",null,(1!==s||!k)&&(0,r.createElement)("button",{className:"button",disabled:0===s,onClick:function(){return c(s-1)}},(0,r.createElement)("span",{className:"dashicons dashicons-arrow-left-alt2"}),(0,i.__)("Back","betterlinks")),(0,r.createElement)("button",{className:"button button-primary",onClick:function(){switch(s){case 1:v(g),h(se(se({},g),E)),c(2);break;case 2:F?(P(!0),S(!0)):L(E,y);break;case 3:F?L(E,y):V();break;case 4:V()}},disabled:D},s===n.length-1?(0,i.__)("Finish","betterlinks"):(0,r.createElement)(r.Fragment,null,(0,i.__)("Next","betterlinks"),(0,r.createElement)("span",{className:"dashicons dashicons-arrow-right-alt2"})))):(0,r.createElement)("div",null))))}));var ce=n(10138),ue=n(61582);function me(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function de(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?me(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):me(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const pe=(0,D.Ng)((function(e){return{terms:e.terms,links:e.links}}),(function(e){return{fetch_terms_data:(0,M.zH)(ue.M3,e),update_option:(0,M.zH)(ce.YD,e),add_new_link:(0,M.zH)(ae.A4,e),fetch_links_data:(0,M.zH)(ae.qh,e)}}))((function(e){var t=(0,r.useState)(f.hC?1:0),n=(0,a.A)(t,2),l=n[0],i=n[1],o=(0,r.useState)(+f.hC),c=(0,a.A)(o,2),u=c[0],m=c[1],d=(0,r.useState)(de(de({},f.sD),{},{redirect_type:(null===f.sD||void 0===f.sD?void 0:f.sD.redirect_type)||"307",nofollow:(null===f.sD||void 0===f.sD?void 0:f.sD.nofollow)||!0,track_me:(null===f.sD||void 0===f.sD?void 0:f.sD.track_me)||!0,prefix:(null===f.sD||void 0===f.sD?void 0:f.sD.prefix)||"go",is_allow_gutenberg:(null===f.sD||void 0===f.sD?void 0:f.sD.is_allow_gutenberg)||!0})),p=(0,a.A)(d,2),_=p[0],k=p[1],v=(0,r.useState)(f.MH),g=(0,a.A)(v,2),E=g[0],h=g[1],y=(0,r.useState)(be(_)),N=(0,a.A)(y,2),O=N[0],w=N[1],P=(0,r.useState)(!1),j=(0,a.A)(P,2),S=j[0],C=j[1],x=(0,r.useState)({isCreated:!1}),A=(0,a.A)(x,2),D=A[0],M=A[1],F=(0,r.useState)(!1),T=(0,a.A)(F,2),L=T[0],q=T[1],V=(0,r.useState)(!1),H=(0,a.A)(V,2),U=H[0],I=H[1],R=(0,r.useState)(de(de(de({},(null===f.MH||void 0===f.MH?void 0:f.MH.simple301redirects)&&{simple301redirects:""}),(null===f.MH||void 0===f.MH?void 0:f.MH.thirstyaffiliates)&&{thirstyaffiliates:""}),(null===f.MH||void 0===f.MH?void 0:f.MH.prettylinks)&&{prettylinks:""})),z=(0,a.A)(R,2),B=z[0],J=z[1];(0,r.useEffect)((function(){0===Object.keys(e.terms).length&&e.fetch_terms_data(),e.links.links||e.fetch_links_data()}),[]);var Q=function(){C(!1)},W={activeStep:l,setActiveStep:i,settings:_,setSettings:k,migrationSettings:E,setMigrationSettings:h,linkOptions:O,getInitialValues:be,setLinkOptions:w,modal:{isOpenUpgradeToProModal:S,setUpgradeToProModal:C,openUpgradeToProModal:function(){C(!0)},closeUpgradeToProModal:Q},terms:null==e?void 0:e.terms,clientConsent:u,setClientConsent:m,update_option:null==e?void 0:e.update_option,add_new_link:null==e?void 0:e.add_new_link,errors:D,setErrors:M,modalIsOpen:L,setModalIsOpen:q,modalConfirm:U,setModalConfirm:I,migrationStatus:B,setMigrationStatus:J};return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(b.P.Provider,{value:W},(0,r.createElement)(s.A,{isOpenModal:S,closeModal:Q}),(0,r.createElement)(oe,null)))}));var be=function(e){var t=(0,f.Yq)(new Date,"yyyy-mm-dd h:m:s");return de({link_title:"",link_slug:"",target_url:"",short_url:"",link_note:"",link_date:t,link_date_gmt:t,link_modified:t,link_modified_gmt:t,cat_id:null},e)}}}]);