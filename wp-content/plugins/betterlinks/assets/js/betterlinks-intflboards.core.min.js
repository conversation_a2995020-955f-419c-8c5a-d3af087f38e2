(()=>{var e={72505:(e,t,r)=>{e.exports=r(18015)},35592:(e,t,r)=>{"use strict";var n=r(9516),o=r(7522),a=r(33948),i=r(79106),s=r(99615),u=r(62012),l=r(64202),c=r(47763);e.exports=function(e){return new Promise((function(t,r){var f=e.data,d=e.headers,h=e.responseType;n.isFormData(f)&&delete d["Content-Type"];var p=new XMLHttpRequest;if(e.auth){var m=e.auth.username||"",g=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";d.Authorization="Basic "+btoa(m+":"+g)}var v=s(e.baseURL,e.url);function b(){if(p){var n="getAllResponseHeaders"in p?u(p.getAllResponseHeaders()):null,a={data:h&&"text"!==h&&"json"!==h?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:n,config:e,request:p};o(t,r,a),p=null}}if(p.open(e.method.toUpperCase(),i(v,e.params,e.paramsSerializer),!0),p.timeout=e.timeout,"onloadend"in p?p.onloadend=b:p.onreadystatechange=function(){p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))&&setTimeout(b)},p.onabort=function(){p&&(r(c("Request aborted",e,"ECONNABORTED",p)),p=null)},p.onerror=function(){r(c("Network Error",e,null,p)),p=null},p.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),r(c(t,e,e.transitional&&e.transitional.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",p)),p=null},n.isStandardBrowserEnv()){var y=(e.withCredentials||l(v))&&e.xsrfCookieName?a.read(e.xsrfCookieName):void 0;y&&(d[e.xsrfHeaderName]=y)}"setRequestHeader"in p&&n.forEach(d,(function(e,t){void 0===f&&"content-type"===t.toLowerCase()?delete d[t]:p.setRequestHeader(t,e)})),n.isUndefined(e.withCredentials)||(p.withCredentials=!!e.withCredentials),h&&"json"!==h&&(p.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&p.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){p&&(p.abort(),r(e),p=null)})),f||(f=null),p.send(f)}))}},18015:(e,t,r)=>{"use strict";var n=r(9516),o=r(69012),a=r(35155),i=r(85343);function s(e){var t=new a(e),r=o(a.prototype.request,t);return n.extend(r,a.prototype,t),n.extend(r,t),r}var u=s(r(96987));u.Axios=a,u.create=function(e){return s(i(u.defaults,e))},u.Cancel=r(31928),u.CancelToken=r(3191),u.isCancel=r(93864),u.all=function(e){return Promise.all(e)},u.spread=r(17980),u.isAxiosError=r(45019),e.exports=u,e.exports.default=u},31928:e=>{"use strict";function t(e){this.message=e}t.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},t.prototype.__CANCEL__=!0,e.exports=t},3191:(e,t,r)=>{"use strict";var n=r(31928);function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var r=this;e((function(e){r.reason||(r.reason=new n(e),t(r.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var e;return{token:new o((function(t){e=t})),cancel:e}},e.exports=o},93864:e=>{"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},35155:(e,t,r)=>{"use strict";var n=r(9516),o=r(79106),a=r(83471),i=r(64490),s=r(85343),u=r(34841),l=u.validators;function c(e){this.defaults=e,this.interceptors={request:new a,response:new a}}c.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=s(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=e.transitional;void 0!==t&&u.assertOptions(t,{silentJSONParsing:l.transitional(l.boolean,"1.0.0"),forcedJSONParsing:l.transitional(l.boolean,"1.0.0"),clarifyTimeoutError:l.transitional(l.boolean,"1.0.0")},!1);var r=[],n=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(n=n&&t.synchronous,r.unshift(t.fulfilled,t.rejected))}));var o,a=[];if(this.interceptors.response.forEach((function(e){a.push(e.fulfilled,e.rejected)})),!n){var c=[i,void 0];for(Array.prototype.unshift.apply(c,r),c=c.concat(a),o=Promise.resolve(e);c.length;)o=o.then(c.shift(),c.shift());return o}for(var f=e;r.length;){var d=r.shift(),h=r.shift();try{f=d(f)}catch(e){h(e);break}}try{o=i(f)}catch(e){return Promise.reject(e)}for(;a.length;)o=o.then(a.shift(),a.shift());return o},c.prototype.getUri=function(e){return e=s(this.defaults,e),o(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},n.forEach(["delete","get","head","options"],(function(e){c.prototype[e]=function(t,r){return this.request(s(r||{},{method:e,url:t,data:(r||{}).data}))}})),n.forEach(["post","put","patch"],(function(e){c.prototype[e]=function(t,r,n){return this.request(s(n||{},{method:e,url:t,data:r}))}})),e.exports=c},83471:(e,t,r)=>{"use strict";var n=r(9516);function o(){this.handlers=[]}o.prototype.use=function(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){n.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=o},99615:(e,t,r)=>{"use strict";var n=r(29137),o=r(84680);e.exports=function(e,t){return e&&!n(t)?o(e,t):t}},47763:(e,t,r)=>{"use strict";var n=r(5449);e.exports=function(e,t,r,o,a){var i=new Error(e);return n(i,t,r,o,a)}},64490:(e,t,r)=>{"use strict";var n=r(9516),o=r(82881),a=r(93864),i=r(96987);function s(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return s(e),e.headers=e.headers||{},e.data=o.call(e,e.data,e.headers,e.transformRequest),e.headers=n.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),n.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||i.adapter)(e).then((function(t){return s(e),t.data=o.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return a(t)||(s(e),t&&t.response&&(t.response.data=o.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},5449:e=>{"use strict";e.exports=function(e,t,r,n,o){return e.config=t,r&&(e.code=r),e.request=n,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},85343:(e,t,r)=>{"use strict";var n=r(9516);e.exports=function(e,t){t=t||{};var r={},o=["url","method","data"],a=["headers","auth","proxy","params"],i=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],s=["validateStatus"];function u(e,t){return n.isPlainObject(e)&&n.isPlainObject(t)?n.merge(e,t):n.isPlainObject(t)?n.merge({},t):n.isArray(t)?t.slice():t}function l(o){n.isUndefined(t[o])?n.isUndefined(e[o])||(r[o]=u(void 0,e[o])):r[o]=u(e[o],t[o])}n.forEach(o,(function(e){n.isUndefined(t[e])||(r[e]=u(void 0,t[e]))})),n.forEach(a,l),n.forEach(i,(function(o){n.isUndefined(t[o])?n.isUndefined(e[o])||(r[o]=u(void 0,e[o])):r[o]=u(void 0,t[o])})),n.forEach(s,(function(n){n in t?r[n]=u(e[n],t[n]):n in e&&(r[n]=u(void 0,e[n]))}));var c=o.concat(a).concat(i).concat(s),f=Object.keys(e).concat(Object.keys(t)).filter((function(e){return-1===c.indexOf(e)}));return n.forEach(f,l),r}},7522:(e,t,r)=>{"use strict";var n=r(47763);e.exports=function(e,t,r){var o=r.config.validateStatus;r.status&&o&&!o(r.status)?t(n("Request failed with status code "+r.status,r.config,null,r.request,r)):e(r)}},82881:(e,t,r)=>{"use strict";var n=r(9516),o=r(96987);e.exports=function(e,t,r){var a=this||o;return n.forEach(r,(function(r){e=r.call(a,e,t)})),e}},96987:(e,t,r)=>{"use strict";var n=r(9516),o=r(7018),a=r(5449),i={"Content-Type":"application/x-www-form-urlencoded"};function s(e,t){!n.isUndefined(e)&&n.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var u,l={transitional:{silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(u=r(35592)),u),transformRequest:[function(e,t){return o(t,"Accept"),o(t,"Content-Type"),n.isFormData(e)||n.isArrayBuffer(e)||n.isBuffer(e)||n.isStream(e)||n.isFile(e)||n.isBlob(e)?e:n.isArrayBufferView(e)?e.buffer:n.isURLSearchParams(e)?(s(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):n.isObject(e)||t&&"application/json"===t["Content-Type"]?(s(t,"application/json"),function(e){if(n.isString(e))try{return(0,JSON.parse)(e),n.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional,r=t&&t.silentJSONParsing,o=t&&t.forcedJSONParsing,i=!r&&"json"===this.responseType;if(i||o&&n.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(i){if("SyntaxError"===e.name)throw a(e,this,"E_JSON_PARSE");throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};n.forEach(["delete","get","head"],(function(e){l.headers[e]={}})),n.forEach(["post","put","patch"],(function(e){l.headers[e]=n.merge(i)})),e.exports=l},69012:e=>{"use strict";e.exports=function(e,t){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return e.apply(t,r)}}},79106:(e,t,r)=>{"use strict";var n=r(9516);function o(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,r){if(!t)return e;var a;if(r)a=r(t);else if(n.isURLSearchParams(t))a=t.toString();else{var i=[];n.forEach(t,(function(e,t){null!=e&&(n.isArray(e)?t+="[]":e=[e],n.forEach(e,(function(e){n.isDate(e)?e=e.toISOString():n.isObject(e)&&(e=JSON.stringify(e)),i.push(o(t)+"="+o(e))})))})),a=i.join("&")}if(a){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+a}return e}},84680:e=>{"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},33948:(e,t,r)=>{"use strict";var n=r(9516);e.exports=n.isStandardBrowserEnv()?{write:function(e,t,r,o,a,i){var s=[];s.push(e+"="+encodeURIComponent(t)),n.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),n.isString(o)&&s.push("path="+o),n.isString(a)&&s.push("domain="+a),!0===i&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},29137:e=>{"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},45019:e=>{"use strict";e.exports=function(e){return"object"==typeof e&&!0===e.isAxiosError}},64202:(e,t,r)=>{"use strict";var n=r(9516);e.exports=n.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function o(e){var n=e;return t&&(r.setAttribute("href",n),n=r.href),r.setAttribute("href",n),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return e=o(window.location.href),function(t){var r=n.isString(t)?o(t):t;return r.protocol===e.protocol&&r.host===e.host}}():function(){return!0}},7018:(e,t,r)=>{"use strict";var n=r(9516);e.exports=function(e,t){n.forEach(e,(function(r,n){n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,delete e[n])}))}},62012:(e,t,r)=>{"use strict";var n=r(9516),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,r,a,i={};return e?(n.forEach(e.split("\n"),(function(e){if(a=e.indexOf(":"),t=n.trim(e.substr(0,a)).toLowerCase(),r=n.trim(e.substr(a+1)),t){if(i[t]&&o.indexOf(t)>=0)return;i[t]="set-cookie"===t?(i[t]?i[t]:[]).concat([r]):i[t]?i[t]+", "+r:r}})),i):i}},17980:e=>{"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},34841:(e,t,r)=>{"use strict";var n=r(64198),o={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){o[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}}));var a={},i=n.version.split(".");function s(e,t){for(var r=t?t.split("."):i,n=e.split("."),o=0;o<3;o++){if(r[o]>n[o])return!0;if(r[o]<n[o])return!1}return!1}o.transitional=function(e,t,r){var o=t&&s(t);function i(e,t){return"[Axios v"+n.version+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return function(r,n,s){if(!1===e)throw new Error(i(n," has been removed in "+t));return o&&!a[n]&&(a[n]=!0,console.warn(i(n," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,n,s)}},e.exports={isOlderVersion:s,assertOptions:function(e,t,r){if("object"!=typeof e)throw new TypeError("options must be an object");for(var n=Object.keys(e),o=n.length;o-- >0;){var a=n[o],i=t[a];if(i){var s=e[a],u=void 0===s||i(s,a,e);if(!0!==u)throw new TypeError("option "+a+" must be "+u)}else if(!0!==r)throw Error("Unknown option "+a)}},validators:o}},9516:(e,t,r)=>{"use strict";var n=r(69012),o=Object.prototype.toString;function a(e){return"[object Array]"===o.call(e)}function i(e){return void 0===e}function s(e){return null!==e&&"object"==typeof e}function u(e){if("[object Object]"!==o.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function l(e){return"[object Function]"===o.call(e)}function c(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),a(e))for(var r=0,n=e.length;r<n;r++)t.call(null,e[r],r,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}e.exports={isArray:a,isArrayBuffer:function(e){return"[object ArrayBuffer]"===o.call(e)},isBuffer:function(e){return null!==e&&!i(e)&&null!==e.constructor&&!i(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:s,isPlainObject:u,isUndefined:i,isDate:function(e){return"[object Date]"===o.call(e)},isFile:function(e){return"[object File]"===o.call(e)},isBlob:function(e){return"[object Blob]"===o.call(e)},isFunction:l,isStream:function(e){return s(e)&&l(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:c,merge:function e(){var t={};function r(r,n){u(t[n])&&u(r)?t[n]=e(t[n],r):u(r)?t[n]=e({},r):a(r)?t[n]=r.slice():t[n]=r}for(var n=0,o=arguments.length;n<o;n++)c(arguments[n],r);return t},extend:function(e,t,r){return c(t,(function(t,o){e[o]=r&&"function"==typeof t?n(t,r):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}}},2694:(e,t,r)=>{"use strict";var n=r(6925);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,a,i){if(i!==n){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return r.PropTypes=r,r}},5556:(e,t,r)=>{e.exports=r(2694)()},6925:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},17790:(e,t,r)=>{var n=r(19852);function o(e){this.mode=n.MODE_8BIT_BYTE,this.data=e}o.prototype={getLength:function(e){return this.data.length},write:function(e){for(var t=0;t<this.data.length;t++)e.put(this.data.charCodeAt(t),8)}},e.exports=o},10046:e=>{function t(){this.buffer=new Array,this.length=0}t.prototype={get:function(e){var t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)},put:function(e,t){for(var r=0;r<t;r++)this.putBit(1==(e>>>t-r-1&1))},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}},e.exports=t},41537:e=>{e.exports={L:1,M:0,Q:3,H:2}},30501:(e,t,r)=>{var n=r(39341);function o(e,t){if(null==e.length)throw new Error(e.length+"/"+t);for(var r=0;r<e.length&&0==e[r];)r++;this.num=new Array(e.length-r+t);for(var n=0;n<e.length-r;n++)this.num[n]=e[n+r]}o.prototype={get:function(e){return this.num[e]},getLength:function(){return this.num.length},multiply:function(e){for(var t=new Array(this.getLength()+e.getLength()-1),r=0;r<this.getLength();r++)for(var a=0;a<e.getLength();a++)t[r+a]^=n.gexp(n.glog(this.get(r))+n.glog(e.get(a)));return new o(t,0)},mod:function(e){if(this.getLength()-e.getLength()<0)return this;for(var t=n.glog(this.get(0))-n.glog(e.get(0)),r=new Array(this.getLength()),a=0;a<this.getLength();a++)r[a]=this.get(a);for(a=0;a<e.getLength();a++)r[a]^=n.gexp(n.glog(e.get(a))+t);return new o(r,0).mod(e)}},e.exports=o},46641:(e,t,r)=>{var n=r(17790),o=r(12835),a=r(10046),i=r(38759),s=r(30501);function u(e,t){this.typeNumber=e,this.errorCorrectLevel=t,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}var l=u.prototype;l.addData=function(e){var t=new n(e);this.dataList.push(t),this.dataCache=null},l.isDark=function(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(e+","+t);return this.modules[e][t]},l.getModuleCount=function(){return this.moduleCount},l.make=function(){if(this.typeNumber<1){var e=1;for(e=1;e<40;e++){for(var t=o.getRSBlocks(e,this.errorCorrectLevel),r=new a,n=0,s=0;s<t.length;s++)n+=t[s].dataCount;for(s=0;s<this.dataList.length;s++){var u=this.dataList[s];r.put(u.mode,4),r.put(u.getLength(),i.getLengthInBits(u.mode,e)),u.write(r)}if(r.getLengthInBits()<=8*n)break}this.typeNumber=e}this.makeImpl(!1,this.getBestMaskPattern())},l.makeImpl=function(e,t){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++){this.modules[r]=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++)this.modules[r][n]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=u.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)},l.setupPositionProbePattern=function(e,t){for(var r=-1;r<=7;r++)if(!(e+r<=-1||this.moduleCount<=e+r))for(var n=-1;n<=7;n++)t+n<=-1||this.moduleCount<=t+n||(this.modules[e+r][t+n]=0<=r&&r<=6&&(0==n||6==n)||0<=n&&n<=6&&(0==r||6==r)||2<=r&&r<=4&&2<=n&&n<=4)},l.getBestMaskPattern=function(){for(var e=0,t=0,r=0;r<8;r++){this.makeImpl(!0,r);var n=i.getLostPoint(this);(0==r||e>n)&&(e=n,t=r)}return t},l.createMovieClip=function(e,t,r){var n=e.createEmptyMovieClip(t,r);this.make();for(var o=0;o<this.modules.length;o++)for(var a=1*o,i=0;i<this.modules[o].length;i++){var s=1*i;this.modules[o][i]&&(n.beginFill(0,100),n.moveTo(s,a),n.lineTo(s+1,a),n.lineTo(s+1,a+1),n.lineTo(s,a+1),n.endFill())}return n},l.setupTimingPattern=function(){for(var e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=e%2==0);for(var t=8;t<this.moduleCount-8;t++)null==this.modules[6][t]&&(this.modules[6][t]=t%2==0)},l.setupPositionAdjustPattern=function(){for(var e=i.getPatternPosition(this.typeNumber),t=0;t<e.length;t++)for(var r=0;r<e.length;r++){var n=e[t],o=e[r];if(null==this.modules[n][o])for(var a=-2;a<=2;a++)for(var s=-2;s<=2;s++)this.modules[n+a][o+s]=-2==a||2==a||-2==s||2==s||0==a&&0==s}},l.setupTypeNumber=function(e){for(var t=i.getBCHTypeNumber(this.typeNumber),r=0;r<18;r++){var n=!e&&1==(t>>r&1);this.modules[Math.floor(r/3)][r%3+this.moduleCount-8-3]=n}for(r=0;r<18;r++)n=!e&&1==(t>>r&1),this.modules[r%3+this.moduleCount-8-3][Math.floor(r/3)]=n},l.setupTypeInfo=function(e,t){for(var r=this.errorCorrectLevel<<3|t,n=i.getBCHTypeInfo(r),o=0;o<15;o++){var a=!e&&1==(n>>o&1);o<6?this.modules[o][8]=a:o<8?this.modules[o+1][8]=a:this.modules[this.moduleCount-15+o][8]=a}for(o=0;o<15;o++)a=!e&&1==(n>>o&1),o<8?this.modules[8][this.moduleCount-o-1]=a:o<9?this.modules[8][15-o-1+1]=a:this.modules[8][15-o-1]=a;this.modules[this.moduleCount-8][8]=!e},l.mapData=function(e,t){for(var r=-1,n=this.moduleCount-1,o=7,a=0,s=this.moduleCount-1;s>0;s-=2)for(6==s&&s--;;){for(var u=0;u<2;u++)if(null==this.modules[n][s-u]){var l=!1;a<e.length&&(l=1==(e[a]>>>o&1)),i.getMask(t,n,s-u)&&(l=!l),this.modules[n][s-u]=l,-1==--o&&(a++,o=7)}if((n+=r)<0||this.moduleCount<=n){n-=r,r=-r;break}}},u.PAD0=236,u.PAD1=17,u.createData=function(e,t,r){for(var n=o.getRSBlocks(e,t),s=new a,l=0;l<r.length;l++){var c=r[l];s.put(c.mode,4),s.put(c.getLength(),i.getLengthInBits(c.mode,e)),c.write(s)}var f=0;for(l=0;l<n.length;l++)f+=n[l].dataCount;if(s.getLengthInBits()>8*f)throw new Error("code length overflow. ("+s.getLengthInBits()+">"+8*f+")");for(s.getLengthInBits()+4<=8*f&&s.put(0,4);s.getLengthInBits()%8!=0;)s.putBit(!1);for(;!(s.getLengthInBits()>=8*f||(s.put(u.PAD0,8),s.getLengthInBits()>=8*f));)s.put(u.PAD1,8);return u.createBytes(s,n)},u.createBytes=function(e,t){for(var r=0,n=0,o=0,a=new Array(t.length),u=new Array(t.length),l=0;l<t.length;l++){var c=t[l].dataCount,f=t[l].totalCount-c;n=Math.max(n,c),o=Math.max(o,f),a[l]=new Array(c);for(var d=0;d<a[l].length;d++)a[l][d]=255&e.buffer[d+r];r+=c;var h=i.getErrorCorrectPolynomial(f),p=new s(a[l],h.getLength()-1).mod(h);for(u[l]=new Array(h.getLength()-1),d=0;d<u[l].length;d++){var m=d+p.getLength()-u[l].length;u[l][d]=m>=0?p.get(m):0}}var g=0;for(d=0;d<t.length;d++)g+=t[d].totalCount;var v=new Array(g),b=0;for(d=0;d<n;d++)for(l=0;l<t.length;l++)d<a[l].length&&(v[b++]=a[l][d]);for(d=0;d<o;d++)for(l=0;l<t.length;l++)d<u[l].length&&(v[b++]=u[l][d]);return v},e.exports=u},12835:(e,t,r)=>{var n=r(41537);function o(e,t){this.totalCount=e,this.dataCount=t}o.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],o.getRSBlocks=function(e,t){var r=o.getRsBlockTable(e,t);if(null==r)throw new Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);for(var n=r.length/3,a=new Array,i=0;i<n;i++)for(var s=r[3*i+0],u=r[3*i+1],l=r[3*i+2],c=0;c<s;c++)a.push(new o(u,l));return a},o.getRsBlockTable=function(e,t){switch(t){case n.L:return o.RS_BLOCK_TABLE[4*(e-1)+0];case n.M:return o.RS_BLOCK_TABLE[4*(e-1)+1];case n.Q:return o.RS_BLOCK_TABLE[4*(e-1)+2];case n.H:return o.RS_BLOCK_TABLE[4*(e-1)+3];default:return}},e.exports=o},39341:e=>{for(var t={glog:function(e){if(e<1)throw new Error("glog("+e+")");return t.LOG_TABLE[e]},gexp:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return t.EXP_TABLE[e]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},r=0;r<8;r++)t.EXP_TABLE[r]=1<<r;for(r=8;r<256;r++)t.EXP_TABLE[r]=t.EXP_TABLE[r-4]^t.EXP_TABLE[r-5]^t.EXP_TABLE[r-6]^t.EXP_TABLE[r-8];for(r=0;r<255;r++)t.LOG_TABLE[t.EXP_TABLE[r]]=r;e.exports=t},19852:e=>{e.exports={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8}},38759:(e,t,r)=>{var n=r(19852),o=r(30501),a=r(39341),i={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(e){for(var t=e<<10;i.getBCHDigit(t)-i.getBCHDigit(i.G15)>=0;)t^=i.G15<<i.getBCHDigit(t)-i.getBCHDigit(i.G15);return(e<<10|t)^i.G15_MASK},getBCHTypeNumber:function(e){for(var t=e<<12;i.getBCHDigit(t)-i.getBCHDigit(i.G18)>=0;)t^=i.G18<<i.getBCHDigit(t)-i.getBCHDigit(i.G18);return e<<12|t},getBCHDigit:function(e){for(var t=0;0!=e;)t++,e>>>=1;return t},getPatternPosition:function(e){return i.PATTERN_POSITION_TABLE[e-1]},getMask:function(e,t,r){switch(e){case 0:return(t+r)%2==0;case 1:return t%2==0;case 2:return r%3==0;case 3:return(t+r)%3==0;case 4:return(Math.floor(t/2)+Math.floor(r/3))%2==0;case 5:return t*r%2+t*r%3==0;case 6:return(t*r%2+t*r%3)%2==0;case 7:return(t*r%3+(t+r)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial:function(e){for(var t=new o([1],0),r=0;r<e;r++)t=t.multiply(new o([1,a.gexp(r)],0));return t},getLengthInBits:function(e,t){if(1<=t&&t<10)switch(e){case n.MODE_NUMBER:return 10;case n.MODE_ALPHA_NUM:return 9;case n.MODE_8BIT_BYTE:case n.MODE_KANJI:return 8;default:throw new Error("mode:"+e)}else if(t<27)switch(e){case n.MODE_NUMBER:return 12;case n.MODE_ALPHA_NUM:return 11;case n.MODE_8BIT_BYTE:return 16;case n.MODE_KANJI:return 10;default:throw new Error("mode:"+e)}else{if(!(t<41))throw new Error("type:"+t);switch(e){case n.MODE_NUMBER:return 14;case n.MODE_ALPHA_NUM:return 13;case n.MODE_8BIT_BYTE:return 16;case n.MODE_KANJI:return 12;default:throw new Error("mode:"+e)}}},getLostPoint:function(e){for(var t=e.getModuleCount(),r=0,n=0;n<t;n++)for(var o=0;o<t;o++){for(var a=0,i=e.isDark(n,o),s=-1;s<=1;s++)if(!(n+s<0||t<=n+s))for(var u=-1;u<=1;u++)o+u<0||t<=o+u||0==s&&0==u||i==e.isDark(n+s,o+u)&&a++;a>5&&(r+=3+a-5)}for(n=0;n<t-1;n++)for(o=0;o<t-1;o++){var l=0;e.isDark(n,o)&&l++,e.isDark(n+1,o)&&l++,e.isDark(n,o+1)&&l++,e.isDark(n+1,o+1)&&l++,0!=l&&4!=l||(r+=3)}for(n=0;n<t;n++)for(o=0;o<t-6;o++)e.isDark(n,o)&&!e.isDark(n,o+1)&&e.isDark(n,o+2)&&e.isDark(n,o+3)&&e.isDark(n,o+4)&&!e.isDark(n,o+5)&&e.isDark(n,o+6)&&(r+=40);for(o=0;o<t;o++)for(n=0;n<t-6;n++)e.isDark(n,o)&&!e.isDark(n+1,o)&&e.isDark(n+2,o)&&e.isDark(n+3,o)&&e.isDark(n+4,o)&&!e.isDark(n+5,o)&&e.isDark(n+6,o)&&(r+=40);var c=0;for(o=0;o<t;o++)for(n=0;n<t;n++)e.isDark(n,o)&&c++;return r+Math.abs(100*c/t/t-50)/5*10}};e.exports=i},15286:(e,t,r)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(){return o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o.apply(this,arguments)}function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(r,!0).forEach((function(t){g(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function s(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function c(e,t,r){return t&&l(e.prototype,t),r&&l(e,r),e}function f(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?h(e):t}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&m(e,t)}function m(e,t){return m=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},m(e,t)}function g(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var v=r(51609),b=(r(5556),r(46641)),y=r(41537);function _(e){for(var t="",r=0;r<e.length;r++){var n=e.charCodeAt(r);n<128?t+=String.fromCharCode(n):n<2048?(t+=String.fromCharCode(192|n>>6),t+=String.fromCharCode(128|63&n)):n<55296||n>=57344?(t+=String.fromCharCode(224|n>>12),t+=String.fromCharCode(128|n>>6&63),t+=String.fromCharCode(128|63&n)):(r++,n=65536+((1023&n)<<10|1023&e.charCodeAt(r)),t+=String.fromCharCode(240|n>>18),t+=String.fromCharCode(128|n>>12&63),t+=String.fromCharCode(128|n>>6&63),t+=String.fromCharCode(128|63&n))}return t}var w={size:128,level:"L",bgColor:"#FFFFFF",fgColor:"#000000",includeMargin:!1};function E(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=[];return e.forEach((function(e,n){var o=null;e.forEach((function(a,i){if(!a&&null!==o)return r.push("M".concat(o+t," ").concat(n+t,"h").concat(i-o,"v1H").concat(o+t,"z")),void(o=null);if(i!==e.length-1)a&&null===o&&(o=i);else{if(!a)return;null===o?r.push("M".concat(i+t,",").concat(n+t," h1v1H").concat(i+t,"z")):r.push("M".concat(o+t,",").concat(n+t," h").concat(i+1-o,"v1H").concat(o+t,"z"))}}))})),r.join("")}function k(e,t){return e.slice().map((function(e,r){return r<t.y||r>=t.y+t.h?e:e.map((function(e,r){return(r<t.x||r>=t.x+t.w)&&e}))}))}function x(e,t){var r=e.imageSettings,n=e.size,o=e.includeMargin;if(null==r)return null;var a=o?4:0,i=t.length+2*a,s=Math.floor(.1*n),u=i/n,l=(r.width||s)*u,c=(r.height||s)*u,f=null==r.x?t.length/2-l/2:r.x*u,d=null==r.y?t.length/2-c/2:r.y*u,h=null;if(r.excavate){var p=Math.floor(f),m=Math.floor(d);h={x:p,y:m,w:Math.ceil(l+f-p),h:Math.ceil(c+d-m)}}return{x:f,y:d,h:c,w:l,excavation:h}}var O=function(){try{(new Path2D).addPath(new Path2D)}catch(e){return!1}return!0}(),C=function(e){function t(){var e,r;u(this,t);for(var n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];return g(h(r=f(this,(e=d(t)).call.apply(e,[this].concat(o)))),"_canvas",void 0),g(h(r),"_image",void 0),g(h(r),"state",{imgLoaded:!1}),g(h(r),"handleImageLoad",(function(){r.setState({imgLoaded:!0})})),r}return p(t,e),c(t,[{key:"componentDidMount",value:function(){this._image&&this._image.complete&&this.handleImageLoad(),this.update()}},{key:"componentWillReceiveProps",value:function(e){var t,r;(null===(t=this.props.imageSettings)||void 0===t?void 0:t.src)!==(null===(r=e.imageSettings)||void 0===r?void 0:r.src)&&this.setState({imgLoaded:!1})}},{key:"componentDidUpdate",value:function(){this.update()}},{key:"update",value:function(){var e=this.props,t=e.value,r=e.size,n=e.level,o=e.bgColor,a=e.fgColor,i=e.includeMargin,s=e.imageSettings,u=new b(-1,y[n]);if(u.addData(_(t)),u.make(),null!=this._canvas){var l=this._canvas,c=l.getContext("2d");if(!c)return;var f=u.modules;if(null===f)return;var d=i?4:0,h=f.length+2*d,p=x(this.props,f);null!=s&&null!=p&&null!=p.excavation&&(f=k(f,p.excavation));var m=window.devicePixelRatio||1;l.height=l.width=r*m;var g=r/h*m;c.scale(g,g),c.fillStyle=o,c.fillRect(0,0,h,h),c.fillStyle=a,O?c.fill(new Path2D(E(f,d))):f.forEach((function(e,t){e.forEach((function(e,r){e&&c.fillRect(r+d,t+d,1,1)}))})),this.state.imgLoaded&&this._image&&null!=p&&c.drawImage(this._image,p.x+d,p.y+d,p.w,p.h)}}},{key:"render",value:function(){var e=this,t=this.props,r=(t.value,t.size),n=(t.level,t.bgColor,t.fgColor,t.style),a=(t.includeMargin,t.imageSettings),u=s(t,["value","size","level","bgColor","fgColor","style","includeMargin","imageSettings"]),l=i({height:r,width:r},n),c=null,f=a&&a.src;return null!=a&&null!=f&&(c=v.createElement("img",{src:f,style:{display:"none"},onLoad:this.handleImageLoad,ref:function(t){return e._image=t}})),v.createElement(v.Fragment,null,v.createElement("canvas",o({style:l,height:r,width:r,ref:function(t){return e._canvas=t}},u)),c)}}]),t}(v.PureComponent);g(C,"defaultProps",w);var S=function(e){function t(){return u(this,t),f(this,d(t).apply(this,arguments))}return p(t,e),c(t,[{key:"render",value:function(){var e=this.props,t=e.value,r=e.size,n=e.level,a=e.bgColor,i=e.fgColor,u=e.includeMargin,l=e.imageSettings,c=s(e,["value","size","level","bgColor","fgColor","includeMargin","imageSettings"]),f=new b(-1,y[n]);f.addData(_(t)),f.make();var d=f.modules;if(null===d)return null;var h=u?4:0,p=d.length+2*h,m=x(this.props,d),g=null;null!=l&&null!=m&&(null!=m.excavation&&(d=k(d,m.excavation)),g=v.createElement("image",{xlinkHref:l.src,height:m.h,width:m.w,x:m.x+h,y:m.y+h,preserveAspectRatio:"none"}));var w=E(d,h);return v.createElement("svg",o({shapeRendering:"crispEdges",height:r,width:r,viewBox:"0 0 ".concat(p," ").concat(p)},c),v.createElement("path",{fill:a,d:"M0,0 h".concat(p,"v").concat(p,"H0z")}),v.createElement("path",{fill:i,d:w}),g)}}]),t}(v.PureComponent);g(S,"defaultProps",w);var P=function(e){var t=e.renderAs,r=s(e,["renderAs"]),n="svg"===t?S:C;return v.createElement(n,r)};P.defaultProps=i({renderAs:"canvas"},w),e.exports=P},5338:(e,t,r)=>{"use strict";var n=r(75795);t.H=n.createRoot,n.hydrateRoot},7420:function(e,t,r){"use strict";var n=this&&this.__assign||function(){return n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},n.apply(this,arguments)},o=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&o(t,e,r);return a(t,e),t},s=this&&this.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};Object.defineProperty(t,"__esModule",{value:!0});var u=i(r(51609)),l=r(11665),c=(0,r(79489).createAnimation)("ClipLoader","0% {transform: rotate(0deg) scale(1)} 50% {transform: rotate(180deg) scale(0.8)} 100% {transform: rotate(360deg) scale(1)}","clip");t.default=function(e){var t=e.loading,r=void 0===t||t,o=e.color,a=void 0===o?"#000000":o,i=e.speedMultiplier,f=void 0===i?1:i,d=e.cssOverride,h=void 0===d?{}:d,p=e.size,m=void 0===p?35:p,g=s(e,["loading","color","speedMultiplier","cssOverride","size"]),v=n({background:"transparent !important",width:(0,l.cssValue)(m),height:(0,l.cssValue)(m),borderRadius:"100%",border:"2px solid",borderTopColor:a,borderBottomColor:"transparent",borderLeftColor:a,borderRightColor:a,display:"inline-block",animation:"".concat(c," ").concat(.75/f,"s 0s infinite linear"),animationFillMode:"both"},h);return r?u.createElement("span",n({style:v},g)):null}},79489:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createAnimation=void 0,t.createAnimation=function(e,t,r){var n="react-spinners-".concat(e,"-").concat(r);if("undefined"==typeof window||!window.document)return n;var o=document.createElement("style");document.head.appendChild(o);var a=o.sheet,i="\n    @keyframes ".concat(n," {\n      ").concat(t,"\n    }\n  ");return a&&a.insertRule(i,0),n}},11665:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.cssValue=t.parseLengthAndUnit=void 0;var r={cm:!0,mm:!0,in:!0,px:!0,pt:!0,pc:!0,em:!0,ex:!0,ch:!0,rem:!0,vw:!0,vh:!0,vmin:!0,vmax:!0,"%":!0};function n(e){if("number"==typeof e)return{value:e,unit:"px"};var t,n=(e.match(/^[0-9.]*/)||"").toString();t=n.includes(".")?parseFloat(n):parseInt(n,10);var o=(e.match(/[^0-9]*$/)||"").toString();return r[o]?{value:t,unit:o}:(console.warn("React Spinners: ".concat(e," is not a valid css value. Defaulting to ").concat(t,"px.")),{value:t,unit:"px"})}t.parseLengthAndUnit=n,t.cssValue=function(e){var t=n(e);return"".concat(t.value).concat(t.unit)}},51609:e=>{"use strict";e.exports=window.React},75795:e=>{"use strict";e.exports=window.ReactDOM},64198:e=>{"use strict";e.exports=JSON.parse('{"_from":"axios@^0.21.1","_id":"axios@0.21.4","_inBundle":false,"_integrity":"sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg==","_location":"/axios","_phantomChildren":{},"_requested":{"type":"range","registry":true,"raw":"axios@^0.21.1","name":"axios","escapedName":"axios","rawSpec":"^0.21.1","saveSpec":null,"fetchSpec":"^0.21.1"},"_requiredBy":["/"],"_resolved":"https://registry.npmjs.org/axios/-/axios-0.21.4.tgz","_shasum":"c67b90dc0568e5c1cf2b0b858c43ba28e2eda575","_spec":"axios@^0.21.1","_where":"/home/<USER>/work/betterlinks/betterlinks","author":{"name":"Matt Zabriskie"},"browser":{"./lib/adapters/http.js":"./lib/adapters/xhr.js"},"bugs":{"url":"https://github.com/axios/axios/issues"},"bundleDependencies":false,"bundlesize":[{"path":"./dist/axios.min.js","threshold":"5kB"}],"dependencies":{"follow-redirects":"^1.14.0"},"deprecated":false,"description":"Promise based HTTP client for the browser and node.js","devDependencies":{"coveralls":"^3.0.0","es6-promise":"^4.2.4","grunt":"^1.3.0","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.1.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^23.0.0","grunt-karma":"^4.0.0","grunt-mocha-test":"^0.13.3","grunt-ts":"^6.0.0-beta.19","grunt-webpack":"^4.0.2","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1","karma":"^6.3.2","karma-chrome-launcher":"^3.1.0","karma-firefox-launcher":"^2.1.0","karma-jasmine":"^1.1.1","karma-jasmine-ajax":"^0.1.13","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^4.3.6","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.8","karma-webpack":"^4.0.2","load-grunt-tasks":"^3.5.2","minimist":"^1.2.0","mocha":"^8.2.1","sinon":"^4.5.0","terser-webpack-plugin":"^4.2.3","typescript":"^4.0.5","url-search-params":"^0.10.0","webpack":"^4.44.2","webpack-dev-server":"^3.11.0"},"homepage":"https://axios-http.com","jsdelivr":"dist/axios.min.js","keywords":["xhr","http","ajax","promise","node"],"license":"MIT","main":"index.js","name":"axios","repository":{"type":"git","url":"git+https://github.com/axios/axios.git"},"scripts":{"build":"NODE_ENV=production grunt build","coveralls":"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js","examples":"node ./examples/server.js","fix":"eslint --fix lib/**/*.js","postversion":"git push && git push --tags","preversion":"npm test","start":"node ./sandbox/server.js","test":"grunt test","version":"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json"},"typings":"./index.d.ts","unpkg":"dist/axios.min.js","version":"0.21.4"}')}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n].call(a.exports,a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=r(51609),t=r(5338);function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t,r){return(t=function(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function a(e,t,r,n,o,a,i){try{var s=e[a](i),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,o)}function i(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function s(e){a(i,n,o,s,u,"next",e)}function u(e){a(i,n,o,s,u,"throw",e)}s(void 0)}))}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,s=[],u=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);u=!0);}catch(e){l=!0,o=e}finally{try{if(!u&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}const l=window.regeneratorRuntime;var c=r.n(l);const f=window.wp.i18n;var d=r(7420),h=r.n(d),p=r(72505),m=r.n(p),g=window.betterLinksFlbIntegration,v=g.site_url,b=g.admin_url,y=g.fbs_settings,_=g.plugin_root_url,w=g.TASKS,E=g.betterlinks_nonce,k=function(e,t){return Object.entries(t).forEach((function(t){var r=u(t,2),o=r[0],a=r[1];"object"===n(a)&&null!==a?e.append(o,JSON.stringify(a)):e.append(o,a)})),e},x=function(e,t,r,n){return e&&n(e),setTimeout((function(){t&&n(t),r&&setTimeout((function(){n(r)}),3e3)}),1e3),!0},O=r(15286),C=r.n(O);const S=function(t){var r,n=t.short_url,o=u((0,e.useState)(!1),2),a=o[0],i=o[1];return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"btl-fbs-qr-section"},(0,e.createElement)("a",{href:"#",className:"btl-fbs-show-hide-qr",onClick:function(e){e.preventDefault(),i(!a)}},a?(0,f.__)("Hide QR Code","betterlinks"):(0,f.__)("Show QR Code","betterlinks"),(0,e.createElement)("span",{className:"dashicons dashicons-arrow-".concat(a?"up":"down","-alt2")})),a&&(0,e.createElement)("div",{className:"betterlinksqrcode"},(0,e.createElement)(C(),{value:(r=n,"/"===r[0]?v+r:v+"/"+r),size:100,level:"H"}),(0,e.createElement)("div",{className:"btl-fbs-qr-sidebar"},(0,e.createElement)("p",null,(0,f.__)("Scan/Download the QR code to Share this task: ","betterlinks")),(0,e.createElement)("a",{className:"btl-fbs-qr-download",onClick:function(e){var t=document.querySelector(".betterlinksqrcode > canvas");e.target.href=t.toDataURL(),e.target.download="betterlinks-".concat(n,"-QR.png")},title:(0,f.__)("Download","betterlinks")},(0,f.__)("Download","betterlinks"))))))};function P(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?P(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}const L=function(){var t=u((0,e.useState)({boardUrl:"",taskName:"",taskId:""}),2),r=t[0],n=t[1],o=u((0,e.useState)(!1),2),a=o[0],s=o[1],l=u((0,e.useState)(!0),2),d=l[0],p=l[1],g=u((0,e.useState)((0,f.__)("Update","betterlinks")),2),v=g[0],b=g[1];(0,e.useEffect)((function(){var e=setTimeout((function(){var e,t=window.location.href.split(w),r=(null==t?void 0:t[0])||"",n=(null==t?void 0:t[1])||"",o=(null===(e=n.split("-"))||void 0===e?void 0:e[0])||"";o&&y({boardUrl:r,taskName:n,taskId:o})}),1e3);return function(){clearTimeout(e)}}),[]);var y=function(){var e=i(c().mark((function e(t){var r,o,a,i,s,u,l,f,d;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return p(!0),o=t.taskId,a=t.taskName,i=t.boardUrl,(s=new FormData).append("security",E),s=k(s,{action:"betterlinks__check_fbs_link",taskId:o,boardUrl:i}),e.next=7,m().post(ajaxurl,s).then((function(e){return e})).catch((function(e){return e}));case 7:if(u=e.sent,l=null===(r=u.data)||void 0===r?void 0:r.data,f=l.result,d=l.is_exists,p(!1),!d){e.next=14;break}return null!=f&&f.task_slug&&(location.href+="-".concat(f.task_slug)),n((function(e){return j(j(j({},e),f),{},{old_short_url:null==f?void 0:f.short_url})})),e.abrupt("return");case 14:if(!f){e.next=17;break}return n((function(e){return j(j(j({},e),f),{},{old_short_url:null==f?void 0:f.short_url,taskId:o,taskName:a,boardUrl:i})})),e.abrupt("return");case 17:case"end":return e.stop()}}),e)})));return function(_x){return e.apply(this,arguments)}}(),O=function(){var e=i(c().mark((function e(t){var r,o,a,i,s,u;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(o=new FormData).append("security",E),o=k(o,j({action:"betterlinks__update_fbs_link"},t)),b((0,f.__)("Updating...","betterlinks")),e.next=6,m().post(ajaxurl,o).then((function(e){return e})).catch((function(e){return e}));case 6:a=e.sent,i=null===(r=a.data)||void 0===r?void 0:r.data,s=i.result,u=i.message,n((function(e){return j(j({},e),{},{short_url:(null==s?void 0:s.short_url)||(null==e?void 0:e.short_url),old_short_url:(null==s?void 0:s.short_url)||(null==e?void 0:e.old_short_url),updateMessage:!s&&u,status:!!s})})),s?x(null,(0,f.__)("Updated!","betterlinks"),(0,f.__)("Update","betterlinks"),b):b((0,f.__)("Update","betterlinks")),S();case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),C=function(){var e=i(c().mark((function e(){var t,o,a,i,u,l;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(o=new FormData).append("security",E),o=k(o,j({action:"betterlinks__create_fbs_link"},r)),p(!0),e.next=6,m().post(ajaxurl,o).then((function(e){return e})).catch((function(e){return e}));case 6:if(a=e.sent,i=null===(t=a.data)||void 0===t?void 0:t.data,u=i.result,l=i.status,p(!1),s(!0),!l){e.next=14;break}return n((function(e){return j(j({},e),{},{short_url:null==u?void 0:u.short_url,old_short_url:null==u?void 0:u.short_url,id:null==u?void 0:u.ID,status:l,updateMessage:(0,f.__)("Short Link created successfully.","betterlinks")})})),S(),e.abrupt("return");case 14:l||n((function(e){return j(j({},e),{},{short_url:(null==u?void 0:u.short_url)||(null==r?void 0:r.old_short_url),status:l,updateMessage:(0,f.__)("Link already exists","betterlinks")})}));case 15:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),S=function(){var e=setTimeout((function(){n((function(e){return j(j({},e),{},{updateMessage:null})}))}),5e3);clearTimeout(e)};return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("button",{className:"el-button",onClick:function(){if(!d&&!function(e){return!(null!=e&&e.short_url||null==e||!e.old_short_url||(n((function(t){return j(j({},t),{},{short_url:null==e?void 0:e.old_short_url,status:!1,updateMessage:(0,f.__)("Field can't be empty","betterlinks")})})),0))}(r))return null!=r&&r.short_url?(s(!a),void n((function(e){return j(j({},e),{},{status:null,updateMessage:null})}))):void C()}},(0,e.createElement)("i",{className:"el-icon"},(0,e.createElement)("img",{width:"16",src:_+"assets/images/logo-black&white.svg",alt:(0,f.__)("BetterLinks Logo","betterlinks")})),(0,e.createElement)("span",null,(0,f.__)("Share Task","betterlinks")),d&&(0,e.createElement)(h(),{className:"btl-fbs-loader",color:"#242f3e",size:18})),a&&(0,e.createElement)(A,{task:r,setTask:n,__updateBetterLinks:O,__createBetterLinks:C,closeModal:function(){return s(!1)},updateText:v}))};var A=function(t){var r=t.task,n=t.setTask,o=t.__updateBetterLinks,a=t.__createBetterLinks,i=t.closeModal,s=t.updateText,l=u((0,e.useState)(!1),2),c=l[0],d=l[1],h=u((0,e.useState)(!1),2),p=h[0],m=h[1];return(0,e.createElement)("div",{className:"el-popper is-light el-popover fbs-task-add-popover-box",tabIndex:-1,"aria-hidden":"false",role:"tooltip","data-popper-placement":"bottom"},(0,e.createElement)("div",{className:"btl-fbs-top-bar"},(0,e.createElement)("span",{className:"btl-title"},(0,f.__)("Share and more...","betterlinks")),(0,e.createElement)("span",{className:"dashicons dashicons-no-alt close-button el-button",onClick:i})),(0,e.createElement)("div",{className:"btl-fbs-link-data"},(0,e.createElement)("div",{className:"btl-form-group"},(0,e.createElement)("label",{htmlFor:"short_url"},(0,f.__)("Link for this task","betterlinks")),(0,e.createElement)("div",null,(0,e.createElement)("p",{className:"btl-fbs-link-text"},(0,e.createElement)("div",null,(0,e.createElement)("span",{className:"btl-site-url"},v,"/"),(0,e.createElement)("input",{type:"text",value:"".concat(null==r?void 0:r.short_url),onChange:function(e){!c&&d(!0),n((function(t){return j(j({},t),{},{short_url:e.target.value})}))}})),(0,e.createElement)("div",{className:"btl-fbs-icon"},p?(0,e.createElement)("span",{className:"dashicons dashicons-yes"}):(0,e.createElement)("img",{width:20,className:"btl-copy-icon",onClick:function(){e="".concat(v,"/").concat(null==r?void 0:r.short_url),(t=document.createElement("input")).value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),m(!0);var e,t,n=setTimeout((function(){m(!1),clearTimeout(n)}),3e3)},src:_+"assets/images/copy-icon-1.svg"})))),(null==r?void 0:r.updateMessage)&&(0,e.createElement)("span",{className:"btl-fbs-message ".concat(null!=r&&r.status?"success":"error")},r.updateMessage)),""!==(null==r?void 0:r.short_url)&&(0,e.createElement)(e.Fragment,null,(null==r?void 0:r.old_short_url)&&(0,e.createElement)(S,{short_url:r.old_short_url}),(0,e.createElement)("div",{className:"btl-form-group fbs_task_mover_actions"},(0,e.createElement)("a",{href:"".concat(b,"?page=betterlinks"),target:"_blank",title:(0,f.__)("Manage All Your Links with BetterLinks","betterlinks")},(0,f.__)("Manage All Your Links with BetterLinks","betterlinks")),(0,e.createElement)("button",j({onClick:function(){if(d(!1),null!=r&&r.id){if((null==r?void 0:r.short_url)===(null==r?void 0:r.old_short_url))return;o({id:null==r?void 0:r.id,short_url:null==r?void 0:r.short_url,old_short_url:null==r?void 0:r.old_short_url})}else a()}},(null==r?void 0:r.short_url)===(null==r?void 0:r.old_short_url)&&{className:"btl-btn-disable"}),null!=r&&r.id?s:(0,f.__)("Create","betterlinks"))))),(0,e.createElement)("span",{className:"el-popper__arrow","data-popper-arrow":!0}))};function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}document.addEventListener("DOMContentLoaded",(function(){null!=y&&y.enable_fbs&&new MutationObserver((function(r,n){var o=!1;return function(){var n,a=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return T(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?T(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var _n=0,n=function(){};return{s:n,n:function(){return _n>=e.length?{done:!0}:{done:!1,value:e[_n++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){i=!0,o=e},f:function(){try{a||null==r.return||r.return()}finally{if(i)throw o}}}}(r);try{for(a.s();!(n=a.n()).done;){var i=n.value;if("childList"===i.type||"subtree"===i.type){var s=document.querySelector(".fbs-task-sidebar-action-btns");if(s&&!o&&!document.getElementById("btl-intflboards-btn-wrapper")){o=!0;var u=document.createElement("div");u.id="btl-intflboards-btn-wrapper",s.append(u),(0,t.H)(document.getElementById("btl-intflboards-btn-wrapper")).render((0,e.createElement)(L,null))}}}}catch(e){a.e(e)}finally{a.f()}}()})).observe(document.body,{childList:!0,subtree:!0})}))})()})();