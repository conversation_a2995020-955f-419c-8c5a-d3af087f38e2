"use strict";(globalThis.webpackChunkbetterlinks=globalThis.webpackChunkbetterlinks||[]).push([[693],{77925:(e,t,l)=>{l.d(t,{A:()=>i});var a=l(51609),r=l.n(a),n=l(5556),o=l.n(n),s={type:o().string,label:o().string,onClickHandler:o().func};function i(e){var t=e.type,l=void 0===t?"":t,n=e.label,o=void 0===n?"":n,s=e.onClickHandler,i=void 0===s?function(){}:s,c=e.children;return(0,a.createElement)(r().Fragment,null,(0,a.createElement)("div",{className:"btl-tooltip"},(0,a.createElement)("button",{className:"dnd-link-button",onClick:i},c||(0,a.createElement)("span",{className:"icon"},(0,a.createElement)("i",{className:"btl btl-".concat(l)}))),(0,a.createElement)("span",{className:"btl-tooltiptext"},o)))}i.propTypes=s},50011:(e,t,l)=>{l.d(t,{A:()=>o});var a=l(51609),r=(l(27723),l(19735)),n=l(16602);const o=function(e){var t=betterLinksHooks.applyFilters("site_url",r.IV);return(0,a.createElement)(React.Fragment,null,(0,a.createElement)("div",{className:"btl-short-url-wrapper"},(0,a.createElement)("span",{className:"btl-short-url"},t+"/"+e.shortUrl),(0,a.createElement)(n.A,{shortUrl:e.shortUrl})))}},60907:(e,t,l)=>{l.d(t,{A:()=>s});var a=l(64467),r=l(51609),n=l(43516);function o(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}const s=function(e){return(0,r.createElement)(n.Ay,function(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?o(Object(l),!0).forEach((function(t){(0,a.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):o(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}({speed:2,width:"100%",height:"100%",viewBox:"0 0 532 148",backgroundColor:"#e8e8e8",foregroundColor:"#c2c2c2"},e),(0,r.createElement)("rect",{x:"2",y:"2",rx:"0",ry:"0",width:"530",height:"11"}),(0,r.createElement)("rect",{x:"3",y:"21",rx:"0",ry:"0",width:"527",height:"10"}),(0,r.createElement)("rect",{x:"2",y:"46",rx:"0",ry:"0",width:"530",height:"3"}),(0,r.createElement)("rect",{x:"2",y:"61",rx:"0",ry:"0",width:"530",height:"3"}),(0,r.createElement)("rect",{x:"2",y:"76",rx:"0",ry:"0",width:"530",height:"3"}),(0,r.createElement)("rect",{x:"2",y:"91",rx:"0",ry:"0",width:"530",height:"3"}),(0,r.createElement)("rect",{x:"2",y:"106",rx:"0",ry:"0",width:"530",height:"3"}),(0,r.createElement)("rect",{x:"1",y:"2",rx:"0",ry:"0",width:"3",height:"107"}),(0,r.createElement)("rect",{x:"529",y:"2",rx:"0",ry:"0",width:"3",height:"106"}),(0,r.createElement)("rect",{x:"2",y:"8",rx:"0",ry:"0",width:"8",height:"16"}),(0,r.createElement)("rect",{x:"25",y:"8",rx:"0",ry:"0",width:"84",height:"17"}),(0,r.createElement)("rect",{x:"144",y:"10",rx:"0",ry:"0",width:"83",height:"14"}),(0,r.createElement)("rect",{x:"264",y:"9",rx:"0",ry:"0",width:"77",height:"16"}),(0,r.createElement)("rect",{x:"476",y:"8",rx:"0",ry:"0",width:"54",height:"18"}),(0,r.createElement)("rect",{x:"374",y:"8",rx:"0",ry:"0",width:"71",height:"16"}),(0,r.createElement)("rect",{x:"10",y:"39",rx:"0",ry:"0",width:"16",height:"2"}),(0,r.createElement)("rect",{x:"110",y:"38",rx:"0",ry:"0",width:"39",height:"2"}),(0,r.createElement)("rect",{x:"227",y:"38",rx:"0",ry:"0",width:"65",height:"3"}),(0,r.createElement)("rect",{x:"341",y:"38",rx:"0",ry:"0",width:"39",height:"2"}),(0,r.createElement)("rect",{x:"445",y:"38",rx:"0",ry:"0",width:"54",height:"3"}),(0,r.createElement)("rect",{x:"10",y:"55",rx:"0",ry:"0",width:"16",height:"2"}),(0,r.createElement)("rect",{x:"110",y:"54",rx:"0",ry:"0",width:"39",height:"2"}),(0,r.createElement)("rect",{x:"227",y:"54",rx:"0",ry:"0",width:"58",height:"3"}),(0,r.createElement)("rect",{x:"341",y:"54",rx:"0",ry:"0",width:"39",height:"2"}),(0,r.createElement)("rect",{x:"445",y:"54",rx:"0",ry:"0",width:"49",height:"3"}),(0,r.createElement)("rect",{x:"10",y:"70",rx:"0",ry:"0",width:"16",height:"2"}),(0,r.createElement)("rect",{x:"110",y:"69",rx:"0",ry:"0",width:"39",height:"2"}),(0,r.createElement)("rect",{x:"227",y:"69",rx:"0",ry:"0",width:"60",height:"3"}),(0,r.createElement)("rect",{x:"341",y:"69",rx:"0",ry:"0",width:"39",height:"2"}),(0,r.createElement)("rect",{x:"445",y:"68",rx:"0",ry:"0",width:"56",height:"3"}),(0,r.createElement)("rect",{x:"10",y:"85",rx:"0",ry:"0",width:"16",height:"2"}),(0,r.createElement)("rect",{x:"110",y:"84",rx:"0",ry:"0",width:"39",height:"2"}),(0,r.createElement)("rect",{x:"227",y:"84",rx:"0",ry:"0",width:"54",height:"3"}),(0,r.createElement)("rect",{x:"341",y:"84",rx:"0",ry:"0",width:"39",height:"2"}),(0,r.createElement)("rect",{x:"445",y:"84",rx:"0",ry:"0",width:"45",height:"3"}),(0,r.createElement)("rect",{x:"10",y:"100",rx:"0",ry:"0",width:"16",height:"2"}),(0,r.createElement)("rect",{x:"110",y:"99",rx:"0",ry:"0",width:"39",height:"2"}),(0,r.createElement)("rect",{x:"227",y:"99",rx:"0",ry:"0",width:"58",height:"3"}),(0,r.createElement)("rect",{x:"341",y:"99",rx:"0",ry:"0",width:"39",height:"2"}),(0,r.createElement)("rect",{x:"445",y:"99",rx:"0",ry:"0",width:"54",height:"3"}))}},16560:(e,t,l)=>{l.d(t,{A:()=>A});var a=l(3453),r=l(80045),n=l(51609),o=l.n(n),s=l(49924),i=l(68238),c=l(27723),d=l(19735),m=l(74086),u=l(67154),p=l(58766),y=l(7400),b=l(20312),h=l.n(b),f=l(46005),k=[{label:(0,c.__)("Delete All","betterlinks"),value:!1},{label:(0,c.__)("Delete clicks older than 30 days","betterlinks"),value:30},{label:(0,c.__)("Delete clicks older than 90 days","betterlinks"),value:90}];const v=(0,s.Ng)((function(){return{}}),(function(e){return{fetchCustomClicksData:(0,i.zH)(p.lC,e),dispatch_new_links_data:(0,i.zH)(y.jT,e)}}))((function(e){var t=e.fetchCustomClicksData,l=e.dispatch_new_links_data,r=(e.propsForAnalytics||{}).customDateFilter,o=(0,n.useState)(0),s=(0,a.A)(o,2),i=s[0],c=s[1],m=(0,n.useState)(!1),u=(0,a.A)(m,2),p=u[0],y=u[1],b=(0,n.useState)(0),v=(0,a.A)(b,2),E=v[0],g=v[1],_=(0,n.useState)("reset_modal_step_1"),w=(0,a.A)(_,2),N=w[0],x=w[1],A=(0,n.useState)(k[0]),C=(0,a.A)(A,2),F=C[0],T=C[1];(0,n.useEffect)((function(){var e,t;return p?null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.add("betterlinks-delete-clicks-modal-popup-opened"):null===(t=document)||void 0===t||null===(t=t.body)||void 0===t||null===(t=t.classList)||void 0===t||t.remove("betterlinks-delete-clicks-modal-popup-opened"),function(){var e;null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.remove("betterlinks-delete-clicks-modal-popup-opened")}}),[p]);var S=function(){clearTimeout(i),x("reset_modal_step_1"),y(!1),T(k[0])};return(0,n.createElement)("div",{className:"btl-analytic-reset-wrapeer betterlinks"},(0,n.createElement)("button",{className:"button-primary btl-reset-analytics-initial-button",onClick:function(){y(!0),x("reset_modal_step_1")}},"Reset"),(0,n.createElement)(h(),{isOpen:p,onRequestClose:S,ariaHideApp:!1},(0,n.createElement)("div",{className:"btl-reset-modal-popup-wrapper "},(0,n.createElement)("span",{className:"btl-close-modal",onClick:S},(0,n.createElement)("i",{className:"btl btl-cancel"})),"reset_modal_step_1"===N&&(0,n.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-1 betterlinks-body"},(0,n.createElement)("h2",null,"Pick the range of BetterLinks Analytics that you want to reset."),(0,n.createElement)("div",{className:"select_apply"},(0,n.createElement)(f.Ay,{className:"btl-modal-select--full ",classNamePrefix:"btl-react-select",onChange:function(e){T(e)},options:k,value:F,isMulti:!1}),(0,n.createElement)("button",{className:"button-primary btl-btn-reset-analytics btl-btn-reset-apply-1",onClick:function(){x("reset_modal_step_2")}},"Apply"))),"reset_modal_step_2"===N&&(0,n.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-2 betterlinks-body"},(0,n.createElement)("h2",null,"This Action Cannot be undone. Are you sure you want to continue?"),(0,n.createElement)("h4",null,"Clicking ",(0,n.createElement)("span",{style:{fontWeight:700}},"Reset Clicks")," will permanently delete the clicks data from database and it cannot be restored again.",(0,n.createElement)("span",{style:{display:"Block"}},"Click 'cancel' to abort.")),(0,n.createElement)("div",{className:"btl-btn-reset-popup-step-2-buttons"},(0,n.createElement)("button",{className:"button-primary btl-btn-reset-apply-2",onClick:function(){if(r){var e=(0,d.Yq)(r[0].startDate,"yyyy-mm-dd"),a=(0,d.Yq)(r[0].endDate,"yyyy-mm-dd");x("deleting");var n=(null==F?void 0:F.value)||!1;(0,d.Xq)(n,e,a).then((function(e){var a,r,n,o,s=setTimeout((function(){S()}),3e3);c(s),null!=e&&null!==(a=e.data)&&void 0!==a&&a.success?(g(null==e||null===(r=e.data)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.count),t({data:null==e||null===(n=e.data)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.new_clicks_data}),l({data:null==e||null===(o=e.data)||void 0===o||null===(o=o.data)||void 0===o?void 0:o.new_links_data}),x("success")):x("failed")})).catch((function(e){console.log("---caught error on DeleteClicks",{err:e});var t=setTimeout((function(){S()}),3e3);c(t)}))}}},"Reset Clicks"),(0,n.createElement)("button",{className:"button-primary btl-btn-reset-cancel",onClick:function(){return x("reset_modal_step_1")}},"Cancel"))),"deleting"===N&&(0,n.createElement)("h2",null,"Deleting..."),"success"===N&&0!==E&&(0,n.createElement)("h2",null,"Success!!! ",(0,n.createElement)("span",{className:"success_delete_count"},E)," clicks record Deleted!!!"),"success"===N&&0===E&&(0,n.createElement)("h2",null,!1===(null==F?void 0:F.value)&&"You don't have any clicks data",30===(null==F?void 0:F.value)&&"You don't have clicks data older than 30 days",90===(null==F?void 0:F.value)&&"You don't have clicks data older than 90 days"),"failed"===N&&(0,n.createElement)("h2",null,"Failed!!"))))}));var E=l(5556),g=l.n(E),_=l(40150),w=["is_pro","render"],N={label:g().string,render:g().func},x=function(e){var t=e.is_pro,l=void 0!==t&&t,s=e.render,i=void 0===s?function(){}:s,m=(0,r.A)(e,w),u=m.propsForAnalytics,p=m.activity.darkMode,y=(0,n.useState)(p),b=(0,a.A)(y,2),h=b[0],f=b[1];(0,n.useEffect)((function(){p?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode")}),[]);var k=betterLinksQuery.get("page"),E=m.favouriteSort.sortByFav;return(0,n.createElement)("div",{className:"topbar"},(0,n.createElement)("div",{className:"topbar__logo_container"},(0,n.createElement)("div",{className:"topbar__logo"},(0,n.createElement)("img",{src:d.hq+"assets/images/logo-large".concat(h?"-white":"",".svg"),alt:"logo"}),(0,n.createElement)("span",{className:"topbar__logo__text"},m.label),l&&(0,n.createElement)(_.A,null)),i()),(0,n.createElement)("div",{className:"topbar-inner"},"betterlinks"===k&&(0,n.createElement)(o().Fragment,null,(0,n.createElement)("div",{className:"btl-view-control"},(0,n.createElement)("button",{title:(0,c.__)("Favorite Links","betterlinks"),className:"btl-link-view-toggler btl-sortby-fav ".concat(E?"active":""),onClick:function(){return m.sortFavourite(!E)}},(0,n.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"favorite-svg",viewBox:"0 0 512 512",xmlSpace:"preserve"},(0,n.createElement)("path",{className:"fav-icon-svg-path",d:"M392.2 317.5c-3 2.9-4.4 7.1-3.7 11.3L414 477.4c1.2 7-3.5 13.6-10.5 14.9-2.8.5-5.6 0-8.1-1.3L262 420.9c-3.7-2-8.2-2-12 0L116.6 491c-3.1 1.7-6.8 1.9-10.1.8-6-2.1-9.5-8.1-8.5-14.4l25.4-148.5c.7-4.2-.7-8.4-3.7-11.4L11.9 212.4c-5.1-5-5.2-13.1-.2-18.2 2-2 4.6-3.3 7.3-3.7l149.1-21.7c4.2-.6 7.8-3.2 9.7-7l66.7-135c2.6-5.3 8.4-8.1 14.2-6.9 3.9.7 7.2 3.3 8.9 6.9l66.7 135c1.9 3.8 5.5 6.4 9.7 7l149 21.6c7 1 11.9 7.6 10.9 14.6-.4 2.7-1.7 5.3-3.7 7.2l-108 105.3z"}))),(0,n.createElement)("button",{title:(0,c.__)("List View","betterlinks"),className:"btl-link-view-toggler ".concat("list"==m.activity.linksView?"active":""),onClick:function(){return m.linksView("list")}},(0,n.createElement)("i",{className:"btl btl-list"})),(0,n.createElement)("button",{title:(0,c.__)("Grid View","betterlinks"),className:"btl-link-view-toggler ".concat("grid"==m.activity.linksView?"active":""),onClick:function(){return m.linksView("grid")}},(0,n.createElement)("i",{className:"btl btl-grid"})))),(null==u?void 0:u.isResetAnalytics)&&(0,n.createElement)(v,{propsForAnalytics:u}),(0,n.createElement)("label",{className:"theme-mood-button",htmlFor:"theme-mood",title:(0,c.__)("Theme Mode","betterlinks")},(0,n.createElement)("input",{type:"checkbox",name:"theme-mood",id:"theme-mood",value:h,onChange:function(){return function(e){e?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode"),m.update_theme_mode(e),f(e)}(!h)},checked:h}),(0,n.createElement)("span",{className:"theme-mood"},(0,n.createElement)("span",{className:"icon"},(0,n.createElement)("i",{className:"btl btl-sun"})),(0,n.createElement)("span",{className:"icon"},(0,n.createElement)("i",{className:"btl btl-moon"}))))))};x.propTypes=N;const A=(0,s.Ng)((function(e){return{activity:e.activity,favouriteSort:e.favouriteSort}}),(function(e){return{linksView:(0,i.zH)(m.xb,e),sortFavourite:(0,i.zH)(u.sortFavourite,e),update_theme_mode:(0,i.zH)(m.Q7,e)}}))(x)},62467:(e,t,l)=>{l.r(t),l.d(t,{default:()=>L});var a=l(3453),r=l(51609),n=l.n(r),o=l(27723),s=l(49924),i=l(60907),c=l(68238),d=l(7400),m=l(94831),u=l(41846),p=l(16560),y=l(46005),b=l(83757),h=l(20312),f=l.n(h),k=l(4949),v=l(5556),E=l.n(v),g=l(77925),_=l(19735);function w(e,t){var l="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!l){if(Array.isArray(e)||(l=function(e,t){if(e){if("string"==typeof e)return N(e,t);var l={}.toString.call(e).slice(8,-1);return"Object"===l&&e.constructor&&(l=e.constructor.name),"Map"===l||"Set"===l?Array.from(e):"Arguments"===l||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l)?N(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){l&&(e=l);var _n=0,a=function(){};return{s:a,n:function(){return _n>=e.length?{done:!0}:{done:!1,value:e[_n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,n=!0,o=!1;return{s:function(){l=l.call(e)},n:function(){var e=l.next();return n=e.done,e},e:function(e){o=!0,r=e},f:function(){try{n||null==l.return||l.return()}finally{if(o)throw r}}}}function N(e,t){(null==t||t>e.length)&&(t=e.length);for(var l=0,a=Array(t);l<t;l++)a[l]=e[l];return a}var x={data:E().object},A=function(e){var t=e.data,l=void 0===t?{}:t,s=e.add_keyword,i=e.update_keyword,c=e.keywords,d=e.linksForUpdateModal,m=e.postTypesProps,u=e.children,p=(0,r.useState)([]),b=(0,a.A)(p,2),h=b[0],v=b[1],E=(0,r.useState)(!1),N=(0,a.A)(E,2),x=N[0],A=N[1],C=(0,r.useState)("HTML"),F=(0,a.A)(C,2),T=F[0],S=F[1],O=(0,r.useState)([]),P=(0,a.A)(O,2),D=P[0],B=P[1],L=m.postTypes,H=m.postTags,M=m.postCategories,V=e.settings.settings,j=[{value:"whitespace",label:"White Space"},{value:"comma",label:"Comma"},{value:"point",label:"Point"},{value:"",label:"None"}];function U(){A(!0),l.link_id&&B(d.find((function(e){return e.value=="".concat(l.link_id)}))||{})}function R(){A(!1),v([]),B([])}return(0,r.useEffect)((function(){return function(){v([])}}),[]),(0,r.createElement)(n().Fragment,null,Object.keys(l).length>0?(0,r.createElement)(g.A,{type:"edit",label:(0,o.__)("Edit Keyword","betterlinks"),onClickHandler:U},u):(0,r.createElement)("div",{className:"btl-create-autolinks"},(0,r.createElement)("button",{className:"btl-create-autolink-button",onClick:U},(0,o.__)("Add New Keywords","betterlinks"))),(0,r.createElement)(f(),{isOpen:x,onRequestClose:R,style:_.vu,ariaHideApp:!1},(0,r.createElement)("span",{className:"btl-close-modal",onClick:R},(0,r.createElement)("i",{className:"btl btl-cancel"})),(0,r.createElement)(k.l1,{initialValues:(0,_.Bi)(l,(null==V?void 0:V.alk)||{}),onSubmit:function(e,t){var a,r=c.data.findIndex((function(e){return l===e})),n=[],m=[],u=w((e.keywords||"").trim().split(","));try{for(u.s();!(a=u.n()).done;){var p=a.value,y=(0,_.dE)(p);y&&m.push(y);var b,h=0,f=w(c.data);try{for(f.s();!(b=f.n()).done;){var k=b.value;if(h!=r){var E,g=w(k.keywords.split(","));try{for(g.s();!(E=g.n()).done;){var N=E.value,x=(0,_.dE)(N);y.toLowerCase()!==x.toLowerCase()||n.includes(x)||n.push(x)}}catch(e){g.e(e)}finally{g.f()}h++}else h++}}catch(e){f.e(e)}finally{f.f()}}}catch(e){u.e(e)}finally{u.f()}if(e.keywords=m.join(", "),n.length>0||0==m.length)return v(n),!1;""!==e.leftBoundary&&""!==e.keywordBefore||(e.leftBoundary="",e.keywordBefore=""),""!==e.rightBoundary&&""!==e.keywordAfter||(e.rightBoundary="",e.keywordAfter=""),d.find((function(t){return t.value==e.chooseLink}))?e.postType.includes("page")&&(e.category.length>0||e.tags.length>0)?t.setFieldError("auto_link_keyword_update_error",(0,o.__)("Post Category & Tags is not available for page","betterlinks")):e.chooseLink&&(Object.keys(l).length>0?i(e):s(e),t.setSubmitting(!1),t.resetForm(),R()):t.setFieldError("chooseLink_error",(0,o.__)("Please Select a Link","betterlinks"))}},(function(e){return(0,r.createElement)(k.lV,{className:"w-100",onSubmit:e.handleSubmit},(0,r.createElement)("div",{className:"btl-entry-content"},(0,r.createElement)("div",{className:"btl-entry-content-left",style:{marginBottom:"20px"}},(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label btl-required",htmlFor:"keywords"},(0,o.__)("Keywords","betterlinks")),(0,r.createElement)("label",{className:"extra_info_keywords"},(0,r.createElement)(k.D0,{id:"keywords",className:"btl-modal-form-control",type:"text",name:"keywords",required:!0}),h.length>0?(0,r.createElement)(r.Fragment,null,(0,r.createElement)("span",{className:"btl_duplicate_keyword"},"keywords:",h.map((function(e,t){return(0,r.createElement)("span",{className:"duplicate_words_wrapper",key:t},(0,r.createElement)("span",{className:"duplicate_words"},' "',e,'"'),(0,r.createElement)("span",{className:"duplicate_separator_comma"},", "))}))," already exists.")):(0,r.createElement)(r.Fragment,null,(0,o.__)(" use comma(,) to add multiple keywords","betterlinks")))),(0,r.createElement)("div",{className:"btl-modal-form-group",style:{position:"relative"}},(0,r.createElement)("label",{className:"btl-modal-form-label btl-required",htmlFor:"link_title"},(0,o.__)("Choose Link","betterlinks")),(0,r.createElement)("div",{className:"btl-modal-form-field-wrapper"},(0,r.createElement)(y.Ay,{isClearable:!0,name:"chooseLink",className:"btl-modal-select--full",classNamePrefix:"btl-react-select",options:d.filter((function(e){return null!==e.value})),value:D,onChange:function(t){e.setFieldValue("chooseLink",t?t.value:""),B(t||[])},required:!0}),e.errors.chooseLink_error&&(0,r.createElement)("span",null,e.errors.chooseLink_error))),(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"link_title"},(0,o.__)("Post Type","betterlinks")),(0,r.createElement)("div",{className:"btl-modal-form-field-wrapper"},(0,r.createElement)(y.Ay,{isMulti:!0,name:"postType",className:"btl-modal-select--full",classNamePrefix:"btl-react-select",options:L,value:L.filter((function(t){if(e.values.postType.includes(t.value.toString()))return t})),onChange:function(t){e.setFieldValue("postType",t.reduce((function(e,t){return e.push(t.value),e}),[]))}}),e.errors.auto_link_keyword_update_error&&(0,r.createElement)("span",null,e.errors.auto_link_keyword_update_error))),(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"link_title"},(0,o.__)("Post Category","betterlinks")),(0,r.createElement)(y.Ay,{isMulti:!0,name:"category",className:"btl-modal-select--full",classNamePrefix:"btl-react-select",options:M,value:M.filter((function(t){if(e.values.category.includes(t.value.toString()))return t})),onChange:function(t){e.setFieldValue("category",t.reduce((function(e,t){return e.push(t.value),e}),[]))}})),(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"link_title"},(0,o.__)("Post Tags","betterlinks")),(0,r.createElement)(y.Ay,{name:"tags",isMulti:!0,className:"btl-modal-select--full",classNamePrefix:"btl-react-select",options:H,value:H.filter((function(t){if(e.values.tags.includes(t.value.toString()))return t})),onChange:function(t){e.setFieldValue("tags",t.reduce((function(e,t){return e.push(t.value),e}),[]))}})),(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label"}),(0,r.createElement)("button",{type:"submit",className:"btl-modal-submit-button"},Object.keys(l).length>0?(0,o.__)("Update","betterlinks"):(0,o.__)("Publish","betterlinks")))),(0,r.createElement)("div",{className:"btl-entry-content-right"},(0,r.createElement)("div",{className:"link-options ".concat("HTML"===T?"link-options--open":"")},(0,r.createElement)("button",{className:"link-options__head",type:"button",onClick:function(){return S("HTML"==T?"ADVANCED":"HTML")}},(0,r.createElement)("h4",{className:"link-options__head--title"},(0,o.__)("HTML Options","betterlinks"))," ",(0,r.createElement)("i",{className:"btl btl-angle-arrow-down"})),(0,r.createElement)("div",{className:"link-options__body"},(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(k.D0,{className:"btl-check",name:"openNewTab",type:"checkbox",onChange:function(){return e.setFieldValue("openNewTab",!e.values.openNewTab)}}),(0,r.createElement)("span",{className:"text"},(0,o.__)("Open New Tab","betterlinks"))),(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(k.D0,{className:"btl-check",name:"useNoFollow",type:"checkbox",onChange:function(){return e.setFieldValue("useNoFollow",!e.values.useNoFollow)}}),(0,r.createElement)("span",{className:"text"},(0,o.__)("Use No Follow","betterlinks"))),(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(k.D0,{className:"btl-check",name:"caseSensitive",type:"checkbox",onChange:function(){return e.setFieldValue("caseSensitive",!e.values.caseSensitive)}}),(0,r.createElement)("span",{className:"text"},(0,o.__)("Case Sensitive","betterlinks"))))),(0,r.createElement)("div",{className:"link-options ".concat("ADVANCED"===T?"link-options--open":""," link-options--advance-keyword")},(0,r.createElement)("button",{className:"link-options__head",type:"button",onClick:function(){return S("ADVANCED"===T?"HTML":"ADVANCED")}},(0,r.createElement)("h4",{className:"link-options__head--title"},(0,o.__)("Advanced Settings","betterlinks"))," ",(0,r.createElement)("i",{className:"btl btl-angle-arrow-down"})),(0,r.createElement)("div",{className:"link-options__body"},(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"leftBoundary"},(0,o.__)("Left Boundary","betterlinks")),(0,r.createElement)(y.Ay,{id:"leftBoundary",name:"leftBoundary",className:"btl-modal-select--mini",classNamePrefix:"btl-react-select",options:j,value:j.filter((function(t){return t.value==e.values.leftBoundary})),onChange:function(t){e.setFieldValue("leftBoundary",t.value)}})),(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"rightBoundary"},(0,o.__)("Right Boundary","betterlinks")),(0,r.createElement)(y.Ay,{id:"rightBoundary",name:"rightBoundary",className:"btl-modal-select--mini",classNamePrefix:"btl-react-select",options:j,value:j.filter((function(t){return t.value==e.values.rightBoundary})),onChange:function(t){e.setFieldValue("rightBoundary",t.value)}})),""!=e.values.leftBoundary&&(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"keywordBefore"},(0,o.__)("Keyword Before","betterlinks")),(0,r.createElement)(k.D0,{id:"keywordBefore",type:"text",name:"keywordBefore"})),""!=e.values.rightBoundary&&(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"keywordAfter"},(0,o.__)("Keyword After","betterlinks")),(0,r.createElement)(k.D0,{id:"keywordAfter",type:"text",name:"keywordAfter"})),(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"limit"},(0,o.__)("Limit","betterlinks")),(0,r.createElement)(k.D0,{id:"limit",type:"number",name:"limit"})))))))}))))};A.propTypes=x;const C=(0,s.Ng)((function(e){return{settings:e.settings}}),(function(e){return{add_keyword:(0,c.zH)(m.s5,e),update_keyword:(0,c.zH)(m.kf,e)}}))(A);function F(e){var t=e.data,l=e.deleteKeywordHandler,s=e.keywords,i=e.postTypesProps,c=e.linksForUpdateModal,d=(0,r.useState)(!1),m=(0,a.A)(d,2),u=m[0],p=m[1];return(0,r.createElement)(n().Fragment,null,(0,r.createElement)("div",{className:"btl-list-view-action-wrapper"},u?(0,r.createElement)("div",{className:"btl-confirm-message"},(0,r.createElement)("span",{className:"action-text"},(0,o.__)("Are You Sure?","betterlinks")),(0,r.createElement)("div",{className:"action-set"},(0,r.createElement)("button",{className:"action yes",onClick:function(){return l()}},(0,o.__)("Yes","betterlinks")),(0,r.createElement)("button",{className:"action no",onClick:function(){return p(!1)}},(0,o.__)("No","betterlinks")))):(0,r.createElement)(r.Fragment,null,(0,r.createElement)(C,{postTypesProps:i,linksForUpdateModal:c,data:t,keywords:s}),(0,r.createElement)(g.A,{onClickHandler:function(){p(!0)},type:"delete",label:(0,o.__)("Delete Keyword","betterlinks")}))))}F.propTypes={};var T=l(50011),S=function(e){var t=(0,r.useState)([]),l=(0,a.A)(t,2),s=l[0],i=l[1],c=(0,r.useState)(!1),d=(0,a.A)(c,2),m=d[0],u=d[1];return(0,r.createElement)(n().Fragment,null,(0,r.createElement)("div",{className:"btl-links-filter"},e.bulkActionData&&e.bulkActionData.selectedCount>0&&(0,r.createElement)("div",{className:"btl-bulk-actions"},(0,r.createElement)(y.Ay,{className:"btl-list-view-select",classNamePrefix:"btl-react-select",defaultValue:{value:"",label:(0,o.__)("Bulk Actions","betterlinks")},value:null!=s&&s.value?s:{value:"",label:(0,o.__)("Bulk Actions","betterlinks")},options:[{value:"delete",label:(0,o.__)("Delete","betterlinks")}],onChange:function(e){return i(e)}}),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("button",{className:"btl-link-apply-button",onClick:function(){return function(t,l,a){return"delete"!==l.value?u(!0):(u(!1),a(t.selectedRows,l),i({}),e.setToggledClearRows())}(e.bulkActionData,s,e.deleteKeywordHandler)}},(0,o.__)("Apply","betterlinks")),m&&"delete"!==s.value&&(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,o.__)("Please Select Action","betterlinks")))),e.search))},O=function(e){var t=e.links,l=e.delete_keyword,a=e.keywords,n=e.postTypesProps,s=e.linksForUpdateModal;return[{name:(0,o.__)("Keywords","betterlinks"),selector:"keywords",sortable:!1,cell:function(e){var t;return(0,r.createElement)(C,{postTypesProps:n,linksForUpdateModal:s,data:e,keywords:a},(0,r.createElement)("div",{style:{textDecoration:"underline"}},null==e||null===(t=e.keywords)||void 0===t?void 0:t.toString()))}},{name:(0,o.__)("Shortened URL","betterlinks"),selector:"short_url",sortable:!1,cell:function(e){var l=t.find((function(t){return t.value==e.link_id}));if(null!=l&&l.value)return(0,r.createElement)("div",null,null!=l&&l.label?(0,r.createElement)(T.A,{shortUrl:l.label}):(0,r.createElement)("span",{style:{fontWeight:"bold",color:"red"}},(0,o.__)("No link selected or link may be deleted.","betterlinks")))}},{name:(0,o.__)("Action","betterlinks"),selector:"",sortable:!1,cell:function(e){return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(F,{keywords:a,postTypesProps:n,linksForUpdateModal:s,data:e,deleteKeywordHandler:function(){l([e])}}))}}]};const P=(0,s.Ng)(null,(function(e){return{delete_keyword:(0,c.zH)(m.eB,e)}}))((function(e){var t=e.linksForUpdateModal,l=e.links,o=e.keywords,s=e.delete_keyword,i=e.postTypesProps,c=e.search,d=(0,r.useState)({}),m=(0,a.A)(d,2),u=m[0],p=m[1],y=(0,r.useState)(!1),h=(0,a.A)(y,2),f=h[0],k=h[1],v=(0,r.createElement)(S,{deleteKeywordHandler:s,bulkActionData:u,search:c,setBulkActionData:p,setToggledClearRows:function(){k(!f)}});return(0,r.createElement)(n().Fragment,null,(0,r.createElement)("div",{className:"btl-list-view btl-autolink-keyword"},(0,r.createElement)(b.Ay,{className:"btl-list-view-table",columns:O({links:l,delete_keyword:s,keywords:o,postTypesProps:i,linksForUpdateModal:t}),data:o.data||[],pagination:!0,subHeader:!0,highlightOnHover:!0,subHeaderComponent:v,persistTableHead:!0,selectableRows:!0,selectableRowsVisibleOnly:!0,onSelectedRowsChange:function(e){return p(e)},clearSelectedRows:f})))}));var D=l(10138),B=function(e){var t=(0,r.useState)(""),l=(0,a.A)(t,2),s=l[0],c=l[1];(0,r.useEffect)((function(){e.settings||e.fetch_settings_data(),e.postdatas.fetchedAll||e.fetch_post_types_data(),e.keywords.data||e.fetch_keywords(),e.links.links||e.fetch_links_data()}),[]);var d,m,u,y,b=(0,_._p)(e.links),h={postTypes:e.postdatas.postTypes||[],postTags:e.postdatas.postTags||[],postCategories:e.postdatas.postCategories||[]},f=e.postdatas.fetchedAll&&e.keywords.data,k=(0,r.createElement)("div",{className:"btl-autolink-filter btl-click-filter"},(0,r.createElement)("input",{id:"search_autolink",type:"search",placeholder:"Search Keywords",value:s,onChange:function(e){var t=e.target.value;c(t)}}));return(0,r.createElement)(n().Fragment,null,f?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(p.A,{label:(0,o.__)("Auto-Link Keywords","betterlinks"),render:function(){var t;return(0,r.createElement)(C,{postTypesProps:h,linksForUpdateModal:b,keywords:e.keywords,settings:(null==e||null===(t=e.settings)||void 0===t?void 0:t.settings)||{}})}}),(0,r.createElement)(P,{search:k,postTypesProps:h,links:(0,_.gJ)(e.links),linksForUpdateModal:b,keywords:(u=new RegExp(s,"gi"),y=null===(d=(null==e||null===(m=e.keywords)||void 0===m?void 0:m.data)||[])||void 0===d?void 0:d.filter((function(e){return((null==e?void 0:e.keywords)||"").match(u)})),Array.isArray(y)?{data:y}:e.keywords)})):(0,r.createElement)(i.A,null))};B.propTypes={};const L=(0,s.Ng)((function(e){return{keywords:e.keywords,links:e.links,postdatas:e.postdatas,settings:e.settings}}),(function(e){return{fetch_keywords:(0,c.zH)(m.Al,e),fetch_links_data:(0,c.zH)(d.qh,e),fetch_post_types_data:(0,c.zH)(u.P,e),fetch_settings_data:(0,c.zH)(D.kc,e)}}))(B)},43516:(e,t,l)=>{l.d(t,{Ay:()=>i});var a=l(51609),r=function(){return r=Object.assign||function(e){for(var t,l=1,a=arguments.length;l<a;l++)for(var r in t=arguments[l])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},r.apply(this,arguments)},n=function(e){var t=e.animate,l=void 0===t||t,n=e.animateBegin,o=e.backgroundColor,s=void 0===o?"#f5f6f7":o,i=e.backgroundOpacity,c=void 0===i?1:i,d=e.baseUrl,m=void 0===d?"":d,u=e.children,p=e.foregroundColor,y=void 0===p?"#eee":p,b=e.foregroundOpacity,h=void 0===b?1:b,f=e.gradientRatio,k=void 0===f?2:f,v=e.gradientDirection,E=void 0===v?"left-right":v,g=e.uniqueKey,_=e.interval,w=void 0===_?.25:_,N=e.rtl,x=void 0!==N&&N,A=e.speed,C=void 0===A?1.2:A,F=e.style,T=void 0===F?{}:F,S=e.title,O=void 0===S?"Loading...":S,P=e.beforeMask,D=void 0===P?null:P,B=function(e,t){var l={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(l[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(l[a[r]]=e[a[r]])}return l}(e,["animate","animateBegin","backgroundColor","backgroundOpacity","baseUrl","children","foregroundColor","foregroundOpacity","gradientRatio","gradientDirection","uniqueKey","interval","rtl","speed","style","title","beforeMask"]),L=g||Math.random().toString(36).substring(6),H=L+"-diff",M=L+"-animated-diff",V=L+"-aria",j=x?{transform:"scaleX(-1)"}:null,U="0; "+w+"; 1",R=C+"s",K="top-bottom"===E?"rotate(90)":void 0;return(0,a.createElement)("svg",r({"aria-labelledby":V,role:"img",style:r(r({},T),j)},B),O?(0,a.createElement)("title",{id:V},O):null,D&&(0,a.isValidElement)(D)?D:null,(0,a.createElement)("rect",{role:"presentation",x:"0",y:"0",width:"100%",height:"100%",clipPath:"url("+m+"#"+H+")",style:{fill:"url("+m+"#"+M+")"}}),(0,a.createElement)("defs",null,(0,a.createElement)("clipPath",{id:H},u),(0,a.createElement)("linearGradient",{id:M,gradientTransform:K},(0,a.createElement)("stop",{offset:"0%",stopColor:s,stopOpacity:c},l&&(0,a.createElement)("animate",{attributeName:"offset",values:-k+"; "+-k+"; 1",keyTimes:U,dur:R,repeatCount:"indefinite",begin:n})),(0,a.createElement)("stop",{offset:"50%",stopColor:y,stopOpacity:h},l&&(0,a.createElement)("animate",{attributeName:"offset",values:-k/2+"; "+-k/2+"; "+(1+k/2),keyTimes:U,dur:R,repeatCount:"indefinite",begin:n})),(0,a.createElement)("stop",{offset:"100%",stopColor:s,stopOpacity:c},l&&(0,a.createElement)("animate",{attributeName:"offset",values:"0; 0; "+(1+k),keyTimes:U,dur:R,repeatCount:"indefinite",begin:n})))))},o=function(e){return e.children?(0,a.createElement)(n,r({},e)):(0,a.createElement)(s,r({},e))},s=function(e){return(0,a.createElement)(o,r({viewBox:"0 0 476 124"},e),(0,a.createElement)("rect",{x:"48",y:"8",width:"88",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"48",y:"26",width:"52",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"0",y:"56",width:"410",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"0",y:"72",width:"380",height:"6",rx:"3"}),(0,a.createElement)("rect",{x:"0",y:"88",width:"178",height:"6",rx:"3"}),(0,a.createElement)("circle",{cx:"20",cy:"20",r:"20"}))};const i=o}}]);