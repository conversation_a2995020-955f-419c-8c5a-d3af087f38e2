"use strict";(globalThis.webpackChunkbetterlinks=globalThis.webpackChunkbetterlinks||[]).push([[869],{34958:(e,t,l)=>{l.d(t,{A:()=>o});var r=l(3453),a=l(51609),n=l(5556),c=l.n(n),i=l(19735),s={siteUrl:c().string,shortUrl:c().string};function o(e){e.siteUrl;var t=e.shortUrl,l=(0,a.useState)(!1),n=(0,r.A)(l,2),c=n[0],s=n[1];return(0,a.createElement)("button",{type:"button",onClick:function(){return function(e){(0,i.Nj)(e),s(!0)}(t)},className:"btl-link-copy-button"},c?(0,a.createElement)("span",{className:"dashicons dashicons-yes"}):(0,a.createElement)("i",{className:"btl btl-copy"}))}o.propTypes=s},52360:(e,t,l)=>{l.d(t,{A:()=>n});var r=l(51609),a=l(27723);const n=function(e){var t=e.note,l=e.title,n=void 0===l?(0,a.__)("Note","betterlinks"):l;return(0,r.createElement)("span",{className:"btl-modal-customize-link-preview--note"},n,": ",t)}},50011:(e,t,l)=>{l.d(t,{A:()=>c});var r=l(51609),a=(l(27723),l(19735)),n=l(16602);const c=function(e){var t=betterLinksHooks.applyFilters("site_url",a.IV);return(0,r.createElement)(React.Fragment,null,(0,r.createElement)("div",{className:"btl-short-url-wrapper"},(0,r.createElement)("span",{className:"btl-short-url"},t+"/"+e.shortUrl),(0,r.createElement)(n.A,{shortUrl:e.shortUrl})))}},60907:(e,t,l)=>{l.d(t,{A:()=>i});var r=l(64467),a=l(51609),n=l(43516);function c(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,r)}return l}const i=function(e){return(0,a.createElement)(n.Ay,function(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?c(Object(l),!0).forEach((function(t){(0,r.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):c(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}({speed:2,width:"100%",height:"100%",viewBox:"0 0 532 148",backgroundColor:"#e8e8e8",foregroundColor:"#c2c2c2"},e),(0,a.createElement)("rect",{x:"2",y:"2",rx:"0",ry:"0",width:"530",height:"11"}),(0,a.createElement)("rect",{x:"3",y:"21",rx:"0",ry:"0",width:"527",height:"10"}),(0,a.createElement)("rect",{x:"2",y:"46",rx:"0",ry:"0",width:"530",height:"3"}),(0,a.createElement)("rect",{x:"2",y:"61",rx:"0",ry:"0",width:"530",height:"3"}),(0,a.createElement)("rect",{x:"2",y:"76",rx:"0",ry:"0",width:"530",height:"3"}),(0,a.createElement)("rect",{x:"2",y:"91",rx:"0",ry:"0",width:"530",height:"3"}),(0,a.createElement)("rect",{x:"2",y:"106",rx:"0",ry:"0",width:"530",height:"3"}),(0,a.createElement)("rect",{x:"1",y:"2",rx:"0",ry:"0",width:"3",height:"107"}),(0,a.createElement)("rect",{x:"529",y:"2",rx:"0",ry:"0",width:"3",height:"106"}),(0,a.createElement)("rect",{x:"2",y:"8",rx:"0",ry:"0",width:"8",height:"16"}),(0,a.createElement)("rect",{x:"25",y:"8",rx:"0",ry:"0",width:"84",height:"17"}),(0,a.createElement)("rect",{x:"144",y:"10",rx:"0",ry:"0",width:"83",height:"14"}),(0,a.createElement)("rect",{x:"264",y:"9",rx:"0",ry:"0",width:"77",height:"16"}),(0,a.createElement)("rect",{x:"476",y:"8",rx:"0",ry:"0",width:"54",height:"18"}),(0,a.createElement)("rect",{x:"374",y:"8",rx:"0",ry:"0",width:"71",height:"16"}),(0,a.createElement)("rect",{x:"10",y:"39",rx:"0",ry:"0",width:"16",height:"2"}),(0,a.createElement)("rect",{x:"110",y:"38",rx:"0",ry:"0",width:"39",height:"2"}),(0,a.createElement)("rect",{x:"227",y:"38",rx:"0",ry:"0",width:"65",height:"3"}),(0,a.createElement)("rect",{x:"341",y:"38",rx:"0",ry:"0",width:"39",height:"2"}),(0,a.createElement)("rect",{x:"445",y:"38",rx:"0",ry:"0",width:"54",height:"3"}),(0,a.createElement)("rect",{x:"10",y:"55",rx:"0",ry:"0",width:"16",height:"2"}),(0,a.createElement)("rect",{x:"110",y:"54",rx:"0",ry:"0",width:"39",height:"2"}),(0,a.createElement)("rect",{x:"227",y:"54",rx:"0",ry:"0",width:"58",height:"3"}),(0,a.createElement)("rect",{x:"341",y:"54",rx:"0",ry:"0",width:"39",height:"2"}),(0,a.createElement)("rect",{x:"445",y:"54",rx:"0",ry:"0",width:"49",height:"3"}),(0,a.createElement)("rect",{x:"10",y:"70",rx:"0",ry:"0",width:"16",height:"2"}),(0,a.createElement)("rect",{x:"110",y:"69",rx:"0",ry:"0",width:"39",height:"2"}),(0,a.createElement)("rect",{x:"227",y:"69",rx:"0",ry:"0",width:"60",height:"3"}),(0,a.createElement)("rect",{x:"341",y:"69",rx:"0",ry:"0",width:"39",height:"2"}),(0,a.createElement)("rect",{x:"445",y:"68",rx:"0",ry:"0",width:"56",height:"3"}),(0,a.createElement)("rect",{x:"10",y:"85",rx:"0",ry:"0",width:"16",height:"2"}),(0,a.createElement)("rect",{x:"110",y:"84",rx:"0",ry:"0",width:"39",height:"2"}),(0,a.createElement)("rect",{x:"227",y:"84",rx:"0",ry:"0",width:"54",height:"3"}),(0,a.createElement)("rect",{x:"341",y:"84",rx:"0",ry:"0",width:"39",height:"2"}),(0,a.createElement)("rect",{x:"445",y:"84",rx:"0",ry:"0",width:"45",height:"3"}),(0,a.createElement)("rect",{x:"10",y:"100",rx:"0",ry:"0",width:"16",height:"2"}),(0,a.createElement)("rect",{x:"110",y:"99",rx:"0",ry:"0",width:"39",height:"2"}),(0,a.createElement)("rect",{x:"227",y:"99",rx:"0",ry:"0",width:"58",height:"3"}),(0,a.createElement)("rect",{x:"341",y:"99",rx:"0",ry:"0",width:"39",height:"2"}),(0,a.createElement)("rect",{x:"445",y:"99",rx:"0",ry:"0",width:"54",height:"3"}))}},18767:(e,t,l)=>{l.d(t,{A:()=>u});var r=l(64467),a=l(3453),n=l(51609),c=l(4949),i=l(46005),s=l(19735);function o(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,r)}return l}function m(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?o(Object(l),!0).forEach((function(t){(0,r.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):o(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}const u=function(e){var t=(0,c.Mt)(e.name),l=(0,a.A)(t,3),r=l[0],o=l[2].setValue,u=["cloak","pro"].includes(r.value)&&!s.JT,d=u?"307":r.value;return u&&o("307"),(0,n.createElement)(React.Fragment,null,(0,n.createElement)(i.Ay,{className:"btl-modal-select--full ".concat(e.value&&e.value.find((function(e){return"pro"==e.value}))?"btl-modal-select-need-pro-teaser":""),classNamePrefix:"btl-react-select",id:r.id,name:r.name,defaultValue:e.value&&e.value.filter((function(e){return e.value==(d||"307")})),onChange:function(t){return null==t?e.setFieldValue(r.name,""):(null!=e&&e.isQuickSetup&&(null==e||e.setLinkOptions((function(l){return m(m({},l),{},{redirect_type:e.isMulti?t.map((function(e){return e.value})):t.value})}))),e.setFieldValue(r.name,e.isMulti?t.map((function(e){return e.value})):t.value))},options:e.value,isMulti:e.isMulti,isDisabled:e.disabled,isOptionDisabled:function(e){return e.disabled}}))}},10700:(e,t,l)=>{l.d(t,{A:()=>d});var r=l(3453),a=l(51609),n=l.n(a),c=l(5556),i=l.n(c),s=l(4949),o=l(93503),m={catId:i().number,data:i().object,fieldName:i().string,setFieldValue:i().func},u=function(e){var t=e.catId,l=e.data,c=e.fieldName,i=e.setFieldValue,m=e.disabled,u=(0,s.Mt)(c),d=(0,r.A)(u,1)[0];return(0,a.createElement)(n().Fragment,null,l.terms?(0,a.createElement)(o.Ay,{className:"btl-modal-select",id:d.id,name:d.name,defaultValue:function(){if(!t){var e=l.terms.filter((function(e){return"uncategorized"==e.term_slug}))[0];return{value:e.ID,label:e.term_name}}var r=l.terms.filter((function(e){return e.ID==t}));if(r.length>0){var a=r[0];return{value:a.ID,label:a.term_name}}}(),classNamePrefix:"btl-react-select",onChange:function(e){return i(d.name,null==e?"":e.value)},options:l.terms.filter((function(e){return"category"==e.term_type&&"uncategorized"!=e.term_slug})).map((function(e){return{value:e.ID,label:e.term_name}})),isDisabled:m}):(0,a.createElement)("div",null,(0,a.createElement)(o.Ay,{className:"btl-modal-select",id:d.id,classNamePrefix:"btl-react-select",isDisabled:m})))};u.propTypes=m;const d=u},45464:(e,t,l)=>{l.d(t,{A:()=>b});var r=l(64467),a=l(3453),n=l(51609),c=l.n(n),i=l(27723),s=l(86663),o=l(2078),m=l(19735);function u(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,r)}return l}function d(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?u(Object(l),!0).forEach((function(t){(0,r.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):u(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}function b(e){var t=e.targetUrl,l=e.saveValueHandler,r=e.closeModalHandler,u=(0,n.useState)(!1),b=(0,a.A)(u,2),p=b[0],h=b[1],E=s.parseUrl(t,{parseFragmentIdentifier:!0}),y=(0,n.useState)({utm_source:E.query.utm_source?E.query.utm_source:"",utm_medium:E.query.utm_medium?E.query.utm_medium:"",utm_campaign:E.query.utm_campaign?E.query.utm_campaign:"",utm_term:E.query.utm_term?E.query.utm_term:"",utm_content:E.query.utm_content?E.query.utm_content:""}),_=(0,a.A)(y,2),k=_[0],g=_[1],v=function(){h(!0)};return(0,n.createElement)(c().Fragment,null,(0,n.createElement)(o.A,{isOpenModal:p,closeModal:function(){h(!1)}}),(0,n.createElement)("div",{className:"btl-modal-utm-builder"},(0,n.createElement)("h3",{className:"btl-modal-utm-builder__title"},(0,i.__)("UTM Builder","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext",style:{width:"220px"}},(0,i.__)("Add Campaign Parameters to Track Custom Campaigns","betterlinks")))),(0,n.createElement)("div",{className:"btl-modal-utm-builder__body"},!betterLinksHooks.applyFilters("isActivePro",!1)&&(0,n.createElement)("div",{className:"btl-modal-utm-builder__form-group btl-modal-utm-templates"},(0,n.createElement)("label",{htmlFor:"savedtemplate"},(0,i.__)("Template","betterlinks")),(0,n.createElement)("div",null,(0,n.createElement)("div",{name:"savedtemplate",id:"savedtemplate",onClick:function(){return v()}},(0,i.__)("Pick a Template","betterlinks")," ",(0,n.createElement)("img",{src:m.hq+"assets/images/locked.svg",alt:"locked",style:{marginLeft:5}})))),(0,n.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,n.createElement)("label",{htmlFor:"utmCampaign"},(0,i.__)("Campaign","betterlinks")),(0,n.createElement)("div",null,(0,n.createElement)("input",{id:"utmCampaign",value:k.utm_campaign,onChange:function(e){return g(d(d({},k),{},{utm_campaign:e.target.value}))},type:"text",name:"utm_campaign",placeholder:(0,i.__)("e.g: Example-campaign","betterlinks")}))),(0,n.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,n.createElement)("label",{htmlFor:"utmMedium"},(0,i.__)("Medium","betterlinks")),(0,n.createElement)("div",null,(0,n.createElement)("input",{id:"utmMedium",value:k.utm_medium,onChange:function(e){return g(d(d({},k),{},{utm_medium:e.target.value}))},type:"text",name:"utm_medium",placeholder:(0,i.__)("e.g: cpc, banner, email","betterlinks")}))),(0,n.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,n.createElement)("label",{htmlFor:"utmSource"},(0,i.__)("Source","betterlinks")),(0,n.createElement)("div",null,(0,n.createElement)("input",{id:"utmSource",value:k.utm_source,onChange:function(e){return g(d(d({},k),{},{utm_source:e.target.value}))},type:"text",name:"utm_source",placeholder:(0,i.__)("e.g: Twitter, Facebook","betterlinks")}))),(0,n.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,n.createElement)("label",{htmlFor:"utmTerm"},(0,i.__)("Term","betterlinks")),(0,n.createElement)("div",null,(0,n.createElement)("input",{id:"utmTerm",value:k.utm_term,onChange:function(e){return g(d(d({},k),{},{utm_term:e.target.value}))},type:"text",name:"utm_term",placeholder:(0,i.__)("e.g: paid keywords","betterlinks")}))),(0,n.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,n.createElement)("label",{htmlFor:"utmContent"},(0,i.__)("Content","betterlinks")),(0,n.createElement)("div",null,(0,n.createElement)("input",{id:"utmContent",value:k.utm_content,onChange:function(e){return g(d(d({},k),{},{utm_content:e.target.value}))},type:"text",name:"utm_content",placeholder:(0,i.__)("e.g: text AD name","betterlinks")}))),(0,n.createElement)("div",{className:"btl-modal-utm-builder__form-group"},(0,n.createElement)("button",{type:"button",onClick:function(){return e=s.exclude(t,["utm_source","utm_medium","utm_campaign","utm_term","utm_content"]),l("target_url",s.stringifyUrl({url:e,query:Object.entries(k).reduce((function(e,t){var l=(0,a.A)(t,2),r=l[0],n=l[1];return n&&""!=n?(e[r]=n,e):e}),{})})),void r();var e}},(0,i.__)("Save Link","betterlinks")),!betterLinksHooks.applyFilters("isActivePro",!1)&&(0,n.createElement)("button",{type:"button",onClick:function(e){return v()}},(0,i.__)("Save New Template","betterlinks")," ",(0,n.createElement)("img",{src:m.hq+"assets/images/locked-white.svg",alt:"locked"}))))))}b.propTypes={}},16560:(e,t,l)=>{l.d(t,{A:()=>C});var r=l(3453),a=l(80045),n=l(51609),c=l.n(n),i=l(49924),s=l(68238),o=l(27723),m=l(19735),u=l(74086),d=l(67154),b=l(58766),p=l(7400),h=l(20312),E=l.n(h),y=l(46005),_=[{label:(0,o.__)("Delete All","betterlinks"),value:!1},{label:(0,o.__)("Delete clicks older than 30 days","betterlinks"),value:30},{label:(0,o.__)("Delete clicks older than 90 days","betterlinks"),value:90}];const k=(0,i.Ng)((function(){return{}}),(function(e){return{fetchCustomClicksData:(0,s.zH)(b.lC,e),dispatch_new_links_data:(0,s.zH)(p.jT,e)}}))((function(e){var t=e.fetchCustomClicksData,l=e.dispatch_new_links_data,a=(e.propsForAnalytics||{}).customDateFilter,c=(0,n.useState)(0),i=(0,r.A)(c,2),s=i[0],o=i[1],u=(0,n.useState)(!1),d=(0,r.A)(u,2),b=d[0],p=d[1],h=(0,n.useState)(0),k=(0,r.A)(h,2),g=k[0],v=k[1],f=(0,n.useState)("reset_modal_step_1"),x=(0,r.A)(f,2),w=x[0],N=x[1],C=(0,n.useState)(_[0]),F=(0,r.A)(C,2),A=F[0],O=F[1];(0,n.useEffect)((function(){var e,t;return b?null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.add("betterlinks-delete-clicks-modal-popup-opened"):null===(t=document)||void 0===t||null===(t=t.body)||void 0===t||null===(t=t.classList)||void 0===t||t.remove("betterlinks-delete-clicks-modal-popup-opened"),function(){var e;null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.remove("betterlinks-delete-clicks-modal-popup-opened")}}),[b]);var S=function(){clearTimeout(s),N("reset_modal_step_1"),p(!1),O(_[0])};return(0,n.createElement)("div",{className:"btl-analytic-reset-wrapeer betterlinks"},(0,n.createElement)("button",{className:"button-primary btl-reset-analytics-initial-button",onClick:function(){p(!0),N("reset_modal_step_1")}},"Reset"),(0,n.createElement)(E(),{isOpen:b,onRequestClose:S,ariaHideApp:!1},(0,n.createElement)("div",{className:"btl-reset-modal-popup-wrapper "},(0,n.createElement)("span",{className:"btl-close-modal",onClick:S},(0,n.createElement)("i",{className:"btl btl-cancel"})),"reset_modal_step_1"===w&&(0,n.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-1 betterlinks-body"},(0,n.createElement)("h2",null,"Pick the range of BetterLinks Analytics that you want to reset."),(0,n.createElement)("div",{className:"select_apply"},(0,n.createElement)(y.Ay,{className:"btl-modal-select--full ",classNamePrefix:"btl-react-select",onChange:function(e){O(e)},options:_,value:A,isMulti:!1}),(0,n.createElement)("button",{className:"button-primary btl-btn-reset-analytics btl-btn-reset-apply-1",onClick:function(){N("reset_modal_step_2")}},"Apply"))),"reset_modal_step_2"===w&&(0,n.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-2 betterlinks-body"},(0,n.createElement)("h2",null,"This Action Cannot be undone. Are you sure you want to continue?"),(0,n.createElement)("h4",null,"Clicking ",(0,n.createElement)("span",{style:{fontWeight:700}},"Reset Clicks")," will permanently delete the clicks data from database and it cannot be restored again.",(0,n.createElement)("span",{style:{display:"Block"}},"Click 'cancel' to abort.")),(0,n.createElement)("div",{className:"btl-btn-reset-popup-step-2-buttons"},(0,n.createElement)("button",{className:"button-primary btl-btn-reset-apply-2",onClick:function(){if(a){var e=(0,m.Yq)(a[0].startDate,"yyyy-mm-dd"),r=(0,m.Yq)(a[0].endDate,"yyyy-mm-dd");N("deleting");var n=(null==A?void 0:A.value)||!1;(0,m.Xq)(n,e,r).then((function(e){var r,a,n,c,i=setTimeout((function(){S()}),3e3);o(i),null!=e&&null!==(r=e.data)&&void 0!==r&&r.success?(v(null==e||null===(a=e.data)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.count),t({data:null==e||null===(n=e.data)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.new_clicks_data}),l({data:null==e||null===(c=e.data)||void 0===c||null===(c=c.data)||void 0===c?void 0:c.new_links_data}),N("success")):N("failed")})).catch((function(e){console.log("---caught error on DeleteClicks",{err:e});var t=setTimeout((function(){S()}),3e3);o(t)}))}}},"Reset Clicks"),(0,n.createElement)("button",{className:"button-primary btl-btn-reset-cancel",onClick:function(){return N("reset_modal_step_1")}},"Cancel"))),"deleting"===w&&(0,n.createElement)("h2",null,"Deleting..."),"success"===w&&0!==g&&(0,n.createElement)("h2",null,"Success!!! ",(0,n.createElement)("span",{className:"success_delete_count"},g)," clicks record Deleted!!!"),"success"===w&&0===g&&(0,n.createElement)("h2",null,!1===(null==A?void 0:A.value)&&"You don't have any clicks data",30===(null==A?void 0:A.value)&&"You don't have clicks data older than 30 days",90===(null==A?void 0:A.value)&&"You don't have clicks data older than 90 days"),"failed"===w&&(0,n.createElement)("h2",null,"Failed!!"))))}));var g=l(5556),v=l.n(g),f=l(40150),x=["is_pro","render"],w={label:v().string,render:v().func},N=function(e){var t=e.is_pro,l=void 0!==t&&t,i=e.render,s=void 0===i?function(){}:i,u=(0,a.A)(e,x),d=u.propsForAnalytics,b=u.activity.darkMode,p=(0,n.useState)(b),h=(0,r.A)(p,2),E=h[0],y=h[1];(0,n.useEffect)((function(){b?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode")}),[]);var _=betterLinksQuery.get("page"),g=u.favouriteSort.sortByFav;return(0,n.createElement)("div",{className:"topbar"},(0,n.createElement)("div",{className:"topbar__logo_container"},(0,n.createElement)("div",{className:"topbar__logo"},(0,n.createElement)("img",{src:m.hq+"assets/images/logo-large".concat(E?"-white":"",".svg"),alt:"logo"}),(0,n.createElement)("span",{className:"topbar__logo__text"},u.label),l&&(0,n.createElement)(f.A,null)),s()),(0,n.createElement)("div",{className:"topbar-inner"},"betterlinks"===_&&(0,n.createElement)(c().Fragment,null,(0,n.createElement)("div",{className:"btl-view-control"},(0,n.createElement)("button",{title:(0,o.__)("Favorite Links","betterlinks"),className:"btl-link-view-toggler btl-sortby-fav ".concat(g?"active":""),onClick:function(){return u.sortFavourite(!g)}},(0,n.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"favorite-svg",viewBox:"0 0 512 512",xmlSpace:"preserve"},(0,n.createElement)("path",{className:"fav-icon-svg-path",d:"M392.2 317.5c-3 2.9-4.4 7.1-3.7 11.3L414 477.4c1.2 7-3.5 13.6-10.5 14.9-2.8.5-5.6 0-8.1-1.3L262 420.9c-3.7-2-8.2-2-12 0L116.6 491c-3.1 1.7-6.8 1.9-10.1.8-6-2.1-9.5-8.1-8.5-14.4l25.4-148.5c.7-4.2-.7-8.4-3.7-11.4L11.9 212.4c-5.1-5-5.2-13.1-.2-18.2 2-2 4.6-3.3 7.3-3.7l149.1-21.7c4.2-.6 7.8-3.2 9.7-7l66.7-135c2.6-5.3 8.4-8.1 14.2-6.9 3.9.7 7.2 3.3 8.9 6.9l66.7 135c1.9 3.8 5.5 6.4 9.7 7l149 21.6c7 1 11.9 7.6 10.9 14.6-.4 2.7-1.7 5.3-3.7 7.2l-108 105.3z"}))),(0,n.createElement)("button",{title:(0,o.__)("List View","betterlinks"),className:"btl-link-view-toggler ".concat("list"==u.activity.linksView?"active":""),onClick:function(){return u.linksView("list")}},(0,n.createElement)("i",{className:"btl btl-list"})),(0,n.createElement)("button",{title:(0,o.__)("Grid View","betterlinks"),className:"btl-link-view-toggler ".concat("grid"==u.activity.linksView?"active":""),onClick:function(){return u.linksView("grid")}},(0,n.createElement)("i",{className:"btl btl-grid"})))),(null==d?void 0:d.isResetAnalytics)&&(0,n.createElement)(k,{propsForAnalytics:d}),(0,n.createElement)("label",{className:"theme-mood-button",htmlFor:"theme-mood",title:(0,o.__)("Theme Mode","betterlinks")},(0,n.createElement)("input",{type:"checkbox",name:"theme-mood",id:"theme-mood",value:E,onChange:function(){return function(e){e?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode"),u.update_theme_mode(e),y(e)}(!E)},checked:E}),(0,n.createElement)("span",{className:"theme-mood"},(0,n.createElement)("span",{className:"icon"},(0,n.createElement)("i",{className:"btl btl-sun"})),(0,n.createElement)("span",{className:"icon"},(0,n.createElement)("i",{className:"btl btl-moon"}))))))};N.propTypes=w;const C=(0,i.Ng)((function(e){return{activity:e.activity,favouriteSort:e.favouriteSort}}),(function(e){return{linksView:(0,s.zH)(u.xb,e),sortFavourite:(0,s.zH)(d.sortFavourite,e),update_theme_mode:(0,s.zH)(u.Q7,e)}}))(N)},77439:(e,t,l)=>{l.r(t),l.d(t,{default:()=>lt});var r=l(51609),a=l.n(r),n=l(49924),c=l(68238),i=l(27723),s=l(16560),o=l(3453),m=l(64467),u=l(23029),d=l(92901),b=l(56822),p=l(53954),h=l(85501),E=l(43516);function y(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,r)}return l}const _=function(e){return(0,r.createElement)(E.Ay,function(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?y(Object(l),!0).forEach((function(t){(0,m.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):y(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}({speed:2,height:"100%",width:"100%",viewBox:"0 0 533 250",backgroundColor:"#e8e8e8",foregroundColor:"#c2c2c2"},e),(0,r.createElement)("rect",{x:"1",y:"2",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"123",y:"2",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"1",y:"1",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"1",y:"115",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"1",y:"13",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"2",y:"8",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"84",y:"8",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"1",y:"36",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"1",y:"52",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"1",y:"68",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"1",y:"85",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("circle",{cx:"62",cy:"101",r:"9"}),(0,r.createElement)("rect",{x:"7",y:"44",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"7",y:"76",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"7",y:"61",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"7",y:"27",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("circle",{cx:"116",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"107",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"98",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"89",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"116",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"107",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"98",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"89",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"116",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"107",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"98",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"89",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"116",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"107",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"98",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"89",cy:"77",r:"3"}),(0,r.createElement)("rect",{x:"407",y:"2",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"529",y:"2",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"407",y:"1",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"407",y:"115",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"407",y:"13",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"408",y:"8",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"490",y:"8",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"407",y:"36",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"407",y:"52",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"407",y:"68",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"407",y:"85",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("circle",{cx:"468",cy:"101",r:"9"}),(0,r.createElement)("rect",{x:"413",y:"44",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"413",y:"76",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"413",y:"61",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"413",y:"27",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("circle",{cx:"522",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"513",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"504",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"495",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"522",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"513",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"504",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"495",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"522",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"513",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"504",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"495",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"522",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"513",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"504",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"495",cy:"77",r:"3"}),(0,r.createElement)("rect",{x:"408",y:"129",rx:"0",ry:"0",width:"3",height:"81"}),(0,r.createElement)("rect",{x:"530",y:"129",rx:"0",ry:"0",width:"3",height:"81"}),(0,r.createElement)("rect",{x:"408",y:"128",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"408",y:"209",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("circle",{cx:"469",cy:"163",r:"12"}),(0,r.createElement)("rect",{x:"445",y:"182",rx:"0",ry:"0",width:"48",height:"6"}),(0,r.createElement)("rect",{x:"1",y:"128",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"123",y:"129",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"1",y:"128",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"1",y:"242",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"1",y:"140",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"2",y:"135",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"84",y:"135",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"1",y:"163",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"1",y:"179",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"1",y:"195",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"1",y:"212",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("circle",{cx:"62",cy:"228",r:"9"}),(0,r.createElement)("rect",{x:"7",y:"171",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"7",y:"203",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"7",y:"188",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"7",y:"154",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("circle",{cx:"116",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"107",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"98",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"89",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"116",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"107",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"98",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"89",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"116",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"107",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"98",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"89",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"116",cy:"204",r:"3"}),(0,r.createElement)("circle",{cx:"107",cy:"204",r:"3"}),(0,r.createElement)("circle",{cx:"98",cy:"204",r:"3"}),(0,r.createElement)("circle",{cx:"89",cy:"204",r:"3"}),(0,r.createElement)("rect",{x:"135",y:"2",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"257",y:"2",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"135",y:"1",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"135",y:"115",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"135",y:"13",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"136",y:"8",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"218",y:"8",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"135",y:"36",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"135",y:"52",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"135",y:"68",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"135",y:"85",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("circle",{cx:"196",cy:"101",r:"9"}),(0,r.createElement)("rect",{x:"141",y:"44",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"141",y:"76",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"141",y:"61",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"141",y:"27",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("circle",{cx:"250",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"241",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"232",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"223",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"250",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"241",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"232",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"223",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"250",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"241",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"232",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"223",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"250",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"241",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"232",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"223",cy:"77",r:"3"}),(0,r.createElement)("rect",{x:"135",y:"129",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"257",y:"129",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"135",y:"128",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"135",y:"242",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"135",y:"140",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"136",y:"135",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"218",y:"135",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"135",y:"163",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"135",y:"179",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"135",y:"195",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"135",y:"212",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("circle",{cx:"196",cy:"228",r:"9"}),(0,r.createElement)("rect",{x:"141",y:"171",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"141",y:"203",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"141",y:"188",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"141",y:"154",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("circle",{cx:"250",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"241",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"232",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"223",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"250",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"241",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"232",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"223",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"250",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"241",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"232",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"223",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"250",cy:"204",r:"3"}),(0,r.createElement)("circle",{cx:"241",cy:"204",r:"3"}),(0,r.createElement)("circle",{cx:"232",cy:"204",r:"3"}),(0,r.createElement)("circle",{cx:"223",cy:"204",r:"3"}),(0,r.createElement)("rect",{x:"271",y:"2",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"393",y:"2",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"271",y:"1",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"271",y:"115",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"271",y:"13",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"272",y:"8",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"354",y:"8",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"271",y:"36",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"271",y:"52",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"271",y:"68",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"271",y:"85",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("circle",{cx:"332",cy:"101",r:"9"}),(0,r.createElement)("rect",{x:"277",y:"44",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"277",y:"76",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"277",y:"61",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"277",y:"27",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("circle",{cx:"386",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"377",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"368",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"359",cy:"29",r:"3"}),(0,r.createElement)("circle",{cx:"386",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"377",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"368",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"359",cy:"45",r:"3"}),(0,r.createElement)("circle",{cx:"386",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"377",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"368",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"359",cy:"61",r:"3"}),(0,r.createElement)("circle",{cx:"386",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"377",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"368",cy:"77",r:"3"}),(0,r.createElement)("circle",{cx:"359",cy:"77",r:"3"}),(0,r.createElement)("rect",{x:"271",y:"129",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"393",y:"129",rx:"0",ry:"0",width:"3",height:"116"}),(0,r.createElement)("rect",{x:"271",y:"128",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"271",y:"242",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"271",y:"140",rx:"0",ry:"0",width:"125",height:"9"}),(0,r.createElement)("rect",{x:"272",y:"135",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"354",y:"135",rx:"0",ry:"0",width:"39",height:"6"}),(0,r.createElement)("rect",{x:"271",y:"163",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"271",y:"179",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"271",y:"195",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("rect",{x:"271",y:"212",rx:"0",ry:"0",width:"125",height:"3"}),(0,r.createElement)("circle",{cx:"332",cy:"228",r:"9"}),(0,r.createElement)("rect",{x:"277",y:"171",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"277",y:"203",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"277",y:"188",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("rect",{x:"277",y:"154",rx:"0",ry:"0",width:"36",height:"3"}),(0,r.createElement)("circle",{cx:"386",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"377",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"368",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"359",cy:"156",r:"3"}),(0,r.createElement)("circle",{cx:"386",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"377",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"368",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"359",cy:"172",r:"3"}),(0,r.createElement)("circle",{cx:"386",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"377",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"368",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"359",cy:"188",r:"3"}),(0,r.createElement)("circle",{cx:"386",cy:"204",r:"3"}),(0,r.createElement)("circle",{cx:"377",cy:"204",r:"3"}),(0,r.createElement)("circle",{cx:"368",cy:"204",r:"3"}),(0,r.createElement)("circle",{cx:"359",cy:"204",r:"3"}))};var k=l(7400),g=l(10138),v=l(61582),f=l(43774),x=l(5556),w=l.n(x),N=l(72505),C=l.n(N),F=l(4949),A=l(19735),O={catId:w().number,catName:w().string,catSlug:w().string,submitHandler:w().func,hideHandler:w().func};function S(e){var t=e.catId,l=void 0===t?0:t,n=e.catName,c=void 0===n?"":n,s=e.catSlug,m=void 0===s?"":s,u=e.submitHandler,d=e.hideHandler,b=(0,r.useState)(!1),p=(0,o.A)(b,2),h=p[0],E=p[1];return(0,r.createElement)(a().Fragment,null,(0,r.createElement)(F.l1,{initialValues:{ID:l,term_name:c,term_slug:m,term_type:"category"},onSubmit:function(e,t){(0,t.setSubmitting)(!1),function(e){var t,l,r;(t=e.term_slug,l=e.ID,r=new FormData,r.append("action","betterlinks/admin/cat_slug_unique_checker"),r.append("security",A.sL),r.append("ID",l),r.append("slug",t),C().post(ajaxurl,r).then((function(e){if(e.data)return E(e.data.data),e.data.data}),(function(e){console.log(e)}))).then((function(t){if(!t){var l=e.term_name.trim();if(l)return e.term_name=l,d(!1),u(e)}}))}(e)}},(function(e){return(0,r.createElement)(F.lV,{className:h?"w-100 is-invalid":"w-100"},(0,r.createElement)("span",{className:l>0?"btl-modal-form-group":"btl-form-group"},l>0&&(0,r.createElement)("label",{className:"btl-modal-form-label btl-required",htmlFor:"cat_name"},(0,i.__)("Category Name","betterlinks")),(0,r.createElement)(F.D0,{id:"term_name",name:"term_name",placeholder:(0,i.__)("* Name","betterlinks"),className:l>0?"btl-modal-form-control":"btl-form-control",onChange:function(t){var l=(0,A.z9)(t.target.value);e.setFieldValue("term_name",t.target.value),e.setFieldValue("term_slug",l),E(!1)},required:!0,autoFocus:!0})),1==h&&(0,r.createElement)("div",{className:"errorlog"},"Already Exists"),l>0?(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label"}),(0,r.createElement)("button",{type:"submit",className:"btl-modal-submit-button"},(0,i.__)("Update","betterlinks"))):(0,r.createElement)("button",{className:"btl-create-category-submit",type:"submit"},(0,i.__)("Submit","betterlinks")))})))}S.propTypes=O;const D=function(e){var t=e.createCatHandler,l=(0,r.useState)(!1),a=(0,o.A)(l,2),n=a[0],c=a[1];return(0,r.createElement)("div",{className:"dnd-create-category"},(0,r.createElement)("button",{className:"dnd-create-category-button",onClick:function(){return c(!n)}},(0,r.createElement)("i",{className:"btl btl-add"})),(0,r.createElement)("p",{className:"dnd-create-category-text"},(0,i.__)("Add New Category","betterlinks")),n&&(0,r.createElement)(S,{hideHandler:c,submitHandler:t}))};var T=l(45458),L=l(10467),P=l(89280),H=l.n(P),j=l(20312),I=l.n(j),z=l(18767),R=l(66087),V=l.n(R),M=l(67783),q=l(10700),U=l(93503),B={linkId:w().number,data:w().object,fieldName:w().string,setFieldValue:w().func},J=function(e){var t=e.fieldName,l=e.linkId,n=e.setFieldValue,c=e.data,i=e.disabled,s=(0,r.useState)(null),m=(0,o.A)(s,2),u=m[0],d=m[1];(0,r.useEffect)((function(){if(l){var e=new FormData;e.append("action","betterlinks/admin/get_terms"),e.append("security",A.sL),e.append("ID",l),e.append("term_type","tags"),C().post(ajaxurl,e).then((function(e){e.data.data&&d(e.data.data.map((function(e){return{value:e.term_id,label:e.term_name}})))}),(function(e){console.log(e)}))}else d([])}),[]);var b=(0,F.Mt)(t),p=(0,o.A)(b,1)[0];return(0,r.createElement)(a().Fragment,null,u&&(0,r.createElement)(U.Ay,{className:"btl-modal-form-control btl-modal-select",isClearable:!0,id:p.id,name:p.name,defaultValue:u,onChange:function(e){return n(p.name,null==e?"":e.map((function(e){return e.value})))},classNamePrefix:"btl-react-select",options:c.terms&&c.terms.filter((function(e){return"tags"==e.term_type})).map((function(e){return{value:e.ID,label:e.term_name}})),isDisabled:i,isMulti:!0}))};const Y=J;J.propTypes=B;var Q=l(34958),W=l(45464),X=l(2078),K=l(52360),Z=l(19555),G=(0,i.__)("Your Prefered Meta Title Here | Site Title","betterlinks"),$=(0,i.__)("Your preferred meta description here. A concise summary to attract and inform visitors about your content, staying within the 160-character limit.","betterlinks");const ee=function(e){var t=e.site_url;return(0,r.createElement)("div",{className:"btl-customized-link-preview-facebook"},(0,r.createElement)("div",{className:"btl-customized-link-preview-image-container"},(0,r.createElement)("img",{src:A.hq+"assets/images/teasers/customize-link-preview-image.png",alt:""})),(0,r.createElement)("div",{className:"btl-customized-link-preview-content-container"},(0,r.createElement)("span",{className:"btl-link-preview-site-url"},t),(0,r.createElement)("span",{className:"btl-link-preview-title"},G),(0,r.createElement)("span",{className:"btl-link-preview-description"},$)))},te=function(e){var t=e.site_url;return(0,r.createElement)("div",{className:"btl-customized-link-preview-facebook btl-customized-link-preview-twitter"},(0,r.createElement)("div",{className:"btl-customized-link-preview-image-container"},(0,r.createElement)("img",{src:A.hq+"assets/images/teasers/customize-link-preview-image.png",alt:""})),(0,r.createElement)("div",{className:"btl-customized-link-preview-content-container"},(0,r.createElement)("span",{className:"btl-link-preview-title"},G),(0,r.createElement)("span",{className:"btl-link-preview-description"},$),(0,r.createElement)("span",{className:"btl-link-preview-site-url"},t)))},le=function(e){var t=e.site_url;return(0,r.createElement)("div",{className:"btl-customized-link-preview-facebook"},(0,r.createElement)("div",{className:"btl-customized-link-preview-image-container"},(0,r.createElement)("img",{src:A.hq+"assets/images/teasers/customize-link-preview-image.png",alt:""})),(0,r.createElement)("div",{className:"btl-customized-link-preview-content-container"},(0,r.createElement)("span",{className:"btl-link-preview-site-url"},t),(0,r.createElement)("span",{className:"btl-link-preview-title"},G)))};var re=[(0,i.__)("Facebook","betterlinks"),(0,i.__)("X (Formerly Twitter)","betterlinks"),(0,i.__)("LinkedIn","betterlinks")];const ae=function(){var e=betterLinksHooks.applyFilters("site_url",A.IV);return(0,r.createElement)("div",{className:"btl-modal-customize-link-preview-box"},(0,r.createElement)("h3",{class:"btl-modal-customize-link-preview__title"},(0,i.__)("Social Link Preview","betterlinks")),(0,r.createElement)(Z.tU,null,(0,r.createElement)(Z.wb,null,re.map((function(e,t){return(0,r.createElement)(Z.oz,{key:t},e)}))),[(0,r.createElement)(ee,{site_url:e}),(0,r.createElement)(te,{site_url:e}),(0,r.createElement)(le,{site_url:e})].map((function(e,t){return(0,r.createElement)(Z.Kp,{key:t},e)}))))};var ne=l(40150);const ce=function(e){var t=e.openUpgradeToProModal;return A.JT?null:(0,r.createElement)("div",{className:"btl-modal-customize-link-preview-container"},(0,r.createElement)("div",{className:"btl-modal-customize-link-preview"},(0,r.createElement)("h3",{className:"btl-modal-customize-link-preview__title"},(0,i.__)("Customize Link Preview","betterlinks"),(0,r.createElement)(ne.A,null)),(0,r.createElement)("div",{className:"btl-modal-customize-link-preview__body"},(0,r.createElement)("div",{className:"btl-modal-customize-link-preview__form-group"},(0,r.createElement)("label",{htmlFor:"meta_title"},(0,i.__)("Meta Title","betterlinks")),(0,r.createElement)("div",null,(0,r.createElement)("textarea",{defaultValue:G,rows:3,style:{height:"55px",cursor:"not-allowed"},disabled:!0}),(0,r.createElement)("div",{style:{display:"flex",justifyContent:"space-between"}},(0,r.createElement)(K.A,{note:(0,i.__)("Recommended meta title length is 50-60 characters. Maximum length is 160 characters.","betterlinks")}),(0,r.createElement)("span",{className:"btl-modal-customize-link-preview--text-counter"},"42/60")))),(0,r.createElement)("div",{className:"btl-modal-customize-link-preview__form-group"},(0,r.createElement)("label",{htmlFor:"meta_description"},(0,i.__)("Meta Description","betterlinks")),(0,r.createElement)("div",null,(0,r.createElement)("textarea",{id:"meta_description",name:"meta_description",rows:3,disabled:!0,style:{cursor:"not-allowed"}},$),(0,r.createElement)("div",{style:{display:"flex",justifyContent:"space-between"}},(0,r.createElement)(K.A,{note:(0,i.__)("Recommended meta description length is 150-160 characters.","betterlinks")}),(0,r.createElement)("span",{className:"btl-modal-customize-link-preview--text-counter"},"146/160")))),(0,r.createElement)("div",{className:"btl-modal-customize-link-preview__form-group"},(0,r.createElement)("label",{htmlFor:"meta_description"},(0,i.__)("Meta Image","betterlinks")),(0,r.createElement)("div",null,(0,r.createElement)("button",{className:"btl-modal-customize-link-preview__btn dashicons dashicons-upload",type:"button",style:{cursor:"not-allowed"},onClick:function(e){e.preventDefault()}},(0,r.createElement)("span",null,(0,i.__)("Upload Image","betterlinks"))),(0,r.createElement)(K.A,{note:(0,i.__)("Upload at least 600x315px image. Recommended size is 1200x630px.","betterlinks")}))),(0,r.createElement)("div",{className:"btl-modal-customize-link-preview__form-group"},(0,r.createElement)("button",{className:"btl-modal-customize-link-preview__save_btn--teaser",type:"button",onClick:t},(0,i.__)("Save","betterlinks"))))),(0,r.createElement)(ae,null))};var ie=l(80702);function se(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,r)}return l}function oe(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?se(Object(l),!0).forEach((function(t){(0,m.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):se(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}const me=function(e){var t,l=e.openAccordion,a=e.form,n=e.settings,c=e.metaTag,s=e.__handleToggle,m=n.settings.enable_customize_meta_tags;if(A.JT&&!m)return null;var u=(0,r.useState)(!1),d=(0,o.A)(u,2),b=d[0],p=d[1],h=(0,ie.c)(),E=(0,o.A)(h,3),y=E[0],_=E[1],k=E[2],g=function(){return p(!1)},v={Tab:Z.oz,Tabs:Z.tU,TabList:Z.wb,TabPanel:Z.Kp},f=oe(oe({},A.vu),{},{overlay:oe(oe({},A.vu.overlay),{},{zIndex:"999999"}),content:oe(oe({},A.vu.content),{},{maxWidth:"1000px"},!A.JT&&{height:"400px"})});(0,r.useEffect)((function(){A.JT&&(a.setFieldValue("enable_meta_tags",!!+(null==c?void 0:c.status)),a.setFieldValue("meta_title",(null==c?void 0:c.meta_title)||""),a.setFieldValue("meta_description",(null==c?void 0:c.meta_desc)||""),a.setFieldValue("meta_image",(null==c?void 0:c.meta_image)||""))}),[]);var x=(0,A.OS)("1.8.0");return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"link-options link-options--advanced link-options--customize-link-preview ".concat(l?"link-options--open":"")},(0,r.createElement)("button",{className:"link-options__head",type:"button",onClick:function(){!A.JT&&p(!0),s("optimizeMetaTags")}},(0,r.createElement)("h4",{className:"link-options__head--title"},(0,i.__)("Customize Link Preview","betterlinks")," ",!A.JT&&(0,r.createElement)(ne.A,null))," ",A.JT&&(0,r.createElement)("i",{className:"btl btl-angle-arrow-down"})),(0,r.createElement)(r.Fragment,null,A.JT&&(0,r.createElement)("div",{className:"link-options__body"},(0,r.createElement)("div",{className:"link-options--teasers"},(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)("input",{id:"enable_meta_tags",name:"enable_meta_tags",className:"btl-check",type:"checkbox",checked:!(null===(t=a.values)||void 0===t||!t.enable_meta_tags),onClick:function(e){var t=e.target.checked;t&&p(t),a.setFieldValue("enable_meta_tags",t)}}),(0,r.createElement)("span",{className:"text"},(0,r.createElement)("span",null,(0,i.__)("Enable Link Preview","betterlinks")),a.values.enable_meta_tags&&(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"btl btl-edit",onClick:function(e){e.preventDefault(),p(!0)}}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("Edit Link Preview","betterlinks")))))))),(0,r.createElement)(I(),{isOpen:b,onRequestClose:g,style:f,ariaHideApp:!1},(0,r.createElement)("span",{className:"btl-close-modal",onClick:g},(0,r.createElement)("i",{className:"btl btl-cancel"})),!x&&(0,r.createElement)("div",{className:"btl-form-group"},(0,r.createElement)("div",{className:"short-description"},(0,r.createElement)("b",{style:{fontWeight:700}},(0,i.__)("Note: ")),(0,i.__)("To Utilize the Customize Link Preview Feature, kindly ensure that you have updated to the latest version of BetterLinks Pro v-1.8.0","betterlinks"))),(0,r.createElement)(X.A,{isOpenModal:y,closeModal:k}),betterLinksHooks.applyFilters("linkOptionsOptimizeMetaTags",(0,r.createElement)(ce,{openUpgradeToProModal:_}),oe(oe(oe({},a),n),{},{metaTag:c,Note:K.A,closeModal:g,ReactTabs:v}))))))},ue=function(e){var t=e.openUpgradeToProModal;return(0,r.createElement)("button",{className:"link-options__head",type:"button",onClick:t},(0,r.createElement)("h4",{className:"link-options__head--title"},(0,i.__)("Custom Tracking Scripts","betterlinks")," ",(0,r.createElement)(ne.A,null))," ")},de=function(e){var t,l=e.openAccordion,a=e.openUpgradeToProModal,n=e.__handleToggle,c=e.props;if(!A.JT||null!=c&&null!==(t=c.tracking)&&void 0!==t&&t.is_enable_custom_scripts)return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"link-options link-options--advanced link-options--customize-link-preview ".concat(l?"link-options--open":"")},A.JT?(0,r.createElement)(r.Fragment,null,(0,r.createElement)("button",{className:"link-options__head",type:"button",onClick:function(){return n("customTrackingScripts")}},(0,r.createElement)("h4",{className:"link-options__head--title"},(0,i.__)("Custom Tracking Scripts","betterlinks")),(0,r.createElement)("i",{className:"btl btl-angle-arrow-down"})),(0,r.createElement)("div",{className:"link-options__body"},betterLinksHooks.applyFilters("linkOptionsCustomTrackingScripts",null,c))):(0,r.createElement)(ue,{openUpgradeToProModal:a})))},be=function(e){var t,l,a=e.props,n=e.customFields;return(0,r.useEffect)((function(){var e;(null==n?void 0:n.length)>0&&void 0===(null===(e=a.values)||void 0===e||null===(e=e.param_struct)||void 0===e?void 0:e.useCustomFields)&&a.setFieldValue("param_struct.useCustomFields",!0)}),[]),(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"useCustomFields"},(0,i.__)("Custom Fields","betterlinks")),(0,r.createElement)(F.D0,{id:"useCustomFields",className:"btl-check",name:"param_struct.useCustomFields",type:"checkbox",checked:null===(t=a.values)||void 0===t||null===(t=t.param_struct)||void 0===t?void 0:t.useCustomFields,onChange:function(){var e;return a.setFieldValue("param_struct.useCustomFields",!(null!==(e=a.values)&&void 0!==e&&null!==(e=e.param_struct)&&void 0!==e&&e.useCustomFields))},disabled:!1})),(null===(l=a.values)||void 0===l||null===(l=l.param_struct)||void 0===l?void 0:l.useCustomFields)&&n.map((function(e,t){return(0,r.createElement)("div",{key:t,className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:e.value},(0,i.__)(e.label,"betterlinks")),(0,r.createElement)(F.D0,{className:"btl-modal-form-control",id:e.value,name:"param_struct[".concat(e.value,"]"),onChange:function(t){a.setFieldValue("param_struct[".concat(e.value,"]"),t.target.value)}}))})))},pe=function(e){var t=e.fetchedTitle,l=e.handleYes,a=e.handleNo;return(0,r.createElement)("div",{className:"btl-modal-form-label-found"},(0,r.createElement)("span",{className:"btl-modal-fetced-title"},t),(0,r.createElement)("br",null),(0,r.createElement)("br",null),(0,r.createElement)("div",null,(0,r.createElement)("span",null,(0,i.__)("Title found from target url, ","betterlinks"),(0,r.createElement)("strong",null,(0,i.__)("Overwrite Title?","betterlinks"))," "),(0,r.createElement)("span",{className:"btl-modal-title-overwrite btl-modal-title-overwrite-yes",onClick:l},(0,i.__)("Yes","betterlinks")),(0,r.createElement)("span",{className:"btl-modal-title-overwrite btl-modal-title-overwrite-no",onClick:a},(0,i.__)("No","betterlinks"))))},he=function(e){var t=e.openUpgradeToProModal,l=void 0===t?function(){}:t;if(!A.JT)return(0,r.createElement)("div",{className:"link-options__body"},(0,r.createElement)("div",{className:"link-options--teasers"},(0,r.createElement)("div",{className:"btl-modal-form-group",onClick:function(){return l()}},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"status"},(0,i.__)("Status","betterlinks")),(0,r.createElement)("select",{id:"status",disabled:!0},(0,r.createElement)("option",{value:"publish"},(0,i.__)("Active","betterlinks")))),(0,r.createElement)("div",{className:"btl-modal-form-group",onClick:function(){return l()}},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"expire"},(0,i.__)("Expire","betterlinks")),(0,r.createElement)("input",{id:"expire",type:"checkbox",disabled:!0})),(0,r.createElement)("div",{className:"btl-modal-form-group",onClick:function(){return l()}},(0,r.createElement)("label",{className:"btl-modal-form-label"},(0,i.__)("Password Protection","betterlinks")),(0,r.createElement)("input",{id:"enable_password",type:"checkbox",disabled:!0}))))},Ee=function(e){var t=e.openUpgradeToProModal;if(!A.JT)return(0,r.createElement)("div",{className:"link-options--teasers",onClick:function(){return t()}},(0,r.createElement)("div",{className:"link-options-info"},(0,r.createElement)("ul",null,(0,r.createElement)("li",null,(0,r.createElement)("label",null,(0,i.__)("Redirection Type:","betterlinks"))),(0,r.createElement)("li",null,(0,r.createElement)("label",null,(0,i.__)("Target URL 1:","betterlinks")),(0,r.createElement)("input",{type:"text",value:"example-1.com",disabled:!0})),(0,r.createElement)("li",null,(0,r.createElement)("label",null,(0,i.__)("Target URL 2:","betterlinks")),(0,r.createElement)("input",{type:"text",value:"example-2.com",disabled:!0})),(0,r.createElement)("li",null,(0,r.createElement)("label",null,(0,i.__)("Split Test:","betterlinks")),(0,r.createElement)("input",{id:"splittest",type:"checkbox",disabled:!0})))))};function ye(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,r)}return l}function _e(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?ye(Object(l),!0).forEach((function(t){(0,m.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):ye(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}var ke={isShowIcon:w().bool,catId:w().number,catName:w().string,data:w().object,submitHandler:w().func},ge=function(e){var t,l,n,c=e.isShowIcon,s=void 0===c||c,u=e.catId,d=e.data,b=e.submitHandler,p=e.fetch_terms_data,h=e.betterlinksGutenStore,E=e.setShowLinkModal,y=void 0===E?function(){}:E,_=e.searchFieldRef,k=e.linkNewTab,g=e.type,v=void 0===g?"":g,f=h?null==h||null===(t=h.getState())||void 0===t?void 0:t.settings:e.settings,x=h?null==h||null===(l=h.getState())||void 0===l?void 0:l.terms:e.terms;window.betterLinksHooks=h?{applyFilters:function(e,t){return t}}:window.betterLinksHooks;var w=e.password,N=(e.metaTags||{}).metaTags,C=(0,r.useState)(!1),O=(0,o.A)(C,2),S=O[0],D=O[1],P=(0,r.useState)(!1),j=(0,o.A)(P,2),R=j[0],U=j[1],B=(0,r.useState)(!1),J=(0,o.A)(B,2),K=J[0],Z=J[1],G=(0,r.useState)(!1),$=(0,o.A)(G,2),ee=$[0],te=$[1],le=(0,r.useState)(!1),re=(0,o.A)(le,2),ae=re[0],ce=re[1],ie=(0,r.useState)(!0),se=(0,o.A)(ie,2),oe=se[0],ue=se[1],ye=(0,A.Yq)(new Date,"yyyy-mm-dd h:m:s"),ke=betterLinksHooks.applyFilters("isDisableLinkFormEditView",!1,d),ge=(0,r.useState)({options:!0,advanced:!1,dynamicRedirect:!1,optimizeMetaTags:!1,customTrackingScripts:!1}),ve=(0,o.A)(ge,2),fe=ve[0],xe=ve[1],we=(0,r.useState)(null),Ne=(0,o.A)(we,2),Ce=Ne[0],Fe=Ne[1],Ae=(0,r.useState)(null),Oe=(0,o.A)(Ae,2),Se=Oe[0],De=Oe[1],Te=(0,r.useState)(null),Le=(0,o.A)(Te,2),Pe=Le[0],He=Le[1],je=(null==f||null===(n=f.settings)||void 0===n?void 0:n.customFields)||[];(0,r.useEffect)((function(){if(null!=d&&d.ID&&null!=w&&w.password&&Object.values(w.password).length>0){var e=Object.values(w.password).find((function(e){return e.link_id==d.ID}));Fe(e)}}),[w]),(0,r.useEffect)((function(){if(null!=d&&d.ID&&N&&Object.values(N).length>0){var e=Object.values(N).find((function(e){return e.link_id==d.ID}));De(e)}}),[N]),(0,r.useEffect)((function(){return h&&D(!0),function(){var e;null!=_&&_.current&&(null==_||null===(e=_.current)||void 0===e||e.focus())}}),[h,Ce]);var Ie=h?{openInNewTab:k}:{},ze=_e(_e({link_title:"",link_slug:"",target_url:"",short_url:(0,A.dM)(f.settings,null),link_note:"",link_date:ye,link_date_gmt:ye,link_modified:ye,link_modified_gmt:ye,redirect_type:"307",cat_id:u||null},f.settings),Ie),Re=_e(_e(_e(_e({},f.settings),{},{link_modified:ye,link_modified_gmt:ye,cat_id:u,old_short_url:d?d.short_url:""},d),Ie),{},{enable_password:Ce&&"1"===Ce.status,old_enable_password:Ce&&"1"===Ce.status,password:Ce&&(null==Ce?void 0:Ce.password),old_allow_visitor_contact:Ce&&"1"===(null==Ce?void 0:Ce.allow_contact),allow_visitor_contact:Ce&&"1"===(null==Ce?void 0:Ce.allow_contact)});function Ve(){if(U(!0),h)return!1;null!=x&&x.terms?(D(!0),U(!1)):p().then((function(){D(!0),U(!1)}))}function Me(){h?y(!1):D(!1)}var qe=function(){D(!0),te(!0)},Ue=function(){A.JT?(ue(!1),qe()):Je()},Be=function(){ue(!0),te(!1)},Je=function(){ce(!0)},Ye=function(){ce(!1)},Qe=function(e){xe((0,m.A)({options:!1,advanced:!1,dynamicRedirect:!1},e,!fe[e]))},We=function(e){Qe(e)},Xe=(0,r.useCallback)(V().debounce(function(){var e=(0,L.A)(H().mark((function e(t,l,r,a){var n,c,i,s;return H().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,(0,A.In)({action:"betterlinks__fetch_target_url",target_url:t});case 3:if(!(n=e.sent).data.result){e.next=14;break}if((i=null===(c=n.data.result)||void 0===c?void 0:c.title)!==a){e.next=8;break}return e.abrupt("return");case 8:if(s=null,i.length>20&&(s=i.split(" ").map((function(e){return e[0]})).join("")),r){e.next=13;break}return He(i),e.abrupt("return");case 13:Ke(l,i||"",s);case 14:e.next=19;break;case 16:e.prev=16,e.t0=e.catch(0),console.log(e.t0);case 19:case"end":return e.stop()}}),e,null,[[0,16]])})));return function(_x,t,l,r){return e.apply(this,arguments)}}(),500),[f.settings]),Ke=function(e,t){var l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(e("link_title",t),!d){var r=(0,A.dM)(f.settings,l||t);r.length>0&&(e("short_url",r),Z(!1))}},Ze=betterLinksHooks.applyFilters("site_url",A.IV);return(0,r.createElement)(r.Fragment,null,d?(0,r.createElement)("button",{onClick:Ve,className:"dnd-link-button ".concat(R?"btl-rotating":"")},(0,r.createElement)("span",{style:{textDecoration:"underline",cursor:"pointer"}},e.children),!e.children&&(0,r.createElement)("span",{className:"icon"},R?(0,r.createElement)("i",{className:"btl btl-reload"}):(0,r.createElement)("i",{className:"btl btl-".concat(""===v?"edit":"copy")}))):(0,r.createElement)("button",{onClick:Ve,className:"btl-create-link-button ".concat(s&&R?"btl-rotating":"")},s?(0,r.createElement)("i",{className:"btl btl-add"}):(0,i.__)("Add New Link","betterlinks")," ",!s&&R?" ...":""),(0,r.createElement)(I(),{isOpen:S,onRequestClose:Me,style:A.vu,ariaHideApp:!1},(0,r.createElement)("span",{className:"btl-close-modal",onClick:Me},(0,r.createElement)("i",{className:"btl btl-cancel"})),(0,r.createElement)(F.l1,{initialValues:betterLinksHooks.applyFilters("linkFormInitialValues",d?Re:ze),onSubmit:function(e,t){var l=t.setSubmitting,r=t.setFieldError;l(!1),/<script\b[^>]*>[\s\S]*?<\/script\b[^>]*>/.test(e.link_title)?r("link_title",(0,i.__)("Please ensure the link title does not contain any script.","betterlinks")):null==e||!e.enable_custom_scripts||null!=e&&e.custom_tracking_scripts?("duplicate"===v&&(delete e.ID,delete e.analytic,delete e.favorite,e.link_date=ye,e.link_date_gmt=ye,e.link_modified=ye,e.link_modified_gmt=ye),function(e){var t=e.short_url;e.short_url=t.substring(0,t.length-+(t.lastIndexOf("/")==t.length-1)),(0,A.X4)(e.short_url,e.ID,Z).then((function(t){if(!t){if(!e.cat_id){var l=x.terms.filter((function(e){return"uncategorized"==e.term_slug}))[0].ID;e.cat_id=l}if(e.link_slug||(e.link_slug=(0,A.z9)(e.link_title)),isNaN(null==e?void 0:e.cat_id)&&(e.cat_slug=(0,A.z9)(e.cat_id)),e.wildcards=Number(e.short_url.includes("*")),e.cat_id){var r=e.link_title.trim();r&&(e.link_title=r,h?(b(e).then((function(e){null!=e&&e.data&&y(!1),(0,A.nB)(document)})).catch((function(e){return console.log("---error (submitHandler)--",{error:e})})),(0,A.mZ)(document)):(b(e),D(!1)))}}}))}(e)):r("custom_tracking_scripts",!0)}},(function(e){var t,l,n=null!==(t=e.values)&&void 0!==t&&t.enable_password?M.EK:M.XN,c=e.errors;return(0,r.createElement)(F.lV,{className:"w-100"},(0,r.createElement)("div",{className:"btl-entry-content"},(0,r.createElement)(X.A,{isOpenModal:ae,closeModal:Ye}),(0,r.createElement)(I(),{isOpen:ee,onRequestClose:Be,style:A.Qu,ariaHideApp:!1},(0,r.createElement)("span",{className:"btl-close-modal",onClick:Be},(0,r.createElement)("i",{className:"btl btl-cancel"})),oe?(0,r.createElement)(a().Fragment,null,betterLinksHooks.applyFilters("linksUTMBuilderField",(0,r.createElement)(W.A,{targetUrl:e.values.target_url,saveValueHandler:e.setFieldValue,closeModalHandler:Be}),e.values.target_url,e.setFieldValue,Be)):(0,r.createElement)(a().Fragment,null,betterLinksHooks.applyFilters("linksBuiltInUTMBuilderField","",e.values.target_url,e.setFieldValue,Be))),(0,r.createElement)("div",{className:"btl-entry-content-left",style:{marginBottom:"20px"}},(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label btl-required",htmlFor:"link_title"},(0,i.__)("Title","betterlinks")),(0,r.createElement)("div",{className:"btl-modal-form-title-wrapper"},(0,r.createElement)("div",{style:{display:"flex",flexDirection:"column",width:"100%"}},(0,r.createElement)(F.D0,{className:"btl-modal-form-control",id:"link_title",name:"link_title",disabled:ke,onChange:function(t){Ke(e.setFieldValue,t.target.value)},required:!0}),c.link_title&&(0,r.createElement)("span",{style:{color:"red"}},c.link_title)),Pe&&(0,r.createElement)(pe,{fetchedTitle:Pe,handleYes:function(){Ke(e.setFieldValue,Pe),He(null)},handleNo:function(){return He(null)}}))),(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"link_note"},(0,i.__)("Description","betterlinks")),(0,r.createElement)(F.D0,{className:"btl-modal-form-control",component:"textarea",id:"link_note",name:"link_note",disabled:ke})),(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label btl-required",htmlFor:"redirect_type"},(0,i.__)("Redirect Type","betterlinks")),(0,r.createElement)(z.A,{id:"redirect_type",name:"redirect_type",value:[].concat((0,T.A)(n),[{value:A.JT?"cloak":"pro",label:(0,i.__)("Cloaked","betterlinks"),disabled:!A.JT}]),setUpgradeToProModal:ce,setFieldValue:e.setFieldValue,disabled:ke,isMulti:!1,enable_password:null===(l=e.values)||void 0===l?void 0:l.enable_password})),(0,r.createElement)("div",{className:"btl-modal-form-group btl-has-utm-button"},(0,r.createElement)("label",{className:"btl-modal-form-label btl-required",htmlFor:"target_url"},(0,i.__)("Target URL","betterlinks")),(0,r.createElement)(F.D0,{className:"btl-modal-form-control",id:"target_url",name:"target_url",onChange:function(t){var l,r,a=t.target.value.replace(/\s+/g,"");e.setFieldValue("target_url",a);var n=""===(null===(l=e.values)||void 0===l?void 0:l.link_title);Xe(a,e.setFieldValue,n,null===(r=e.values)||void 0===r?void 0:r.link_title)},placeholder:"",disabled:ke,required:!0}),(0,r.createElement)("div",{className:"btl-utm-button-group"},(0,r.createElement)("button",{type:"button",className:"btl-utm-button",onClick:qe,disabled:ke},(0,i.__)("UTM","betterlinks")),A.JT?(0,r.createElement)("button",{type:"button",className:"btl-share-button",onClick:Ue,disabled:ke},(0,r.createElement)("i",{className:"btl btl-share"})):(0,r.createElement)("button",{type:"button",className:"btl-share-button btl-share-button--locked",onClick:Ue,disabled:ke},(0,r.createElement)("i",{className:"btl btl-share"}),(0,r.createElement)("img",{className:"locked",src:A.hq+"assets/images/lock-round.svg",alt:"icon"})))),(0,r.createElement)("div",{className:"btl-modal-shorturl-wrap"},(0,r.createElement)("div",{className:"btl-modal-form-group shorturl"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"short_url"},(0,i.__)("Shortened URL","betterlinks")),(0,r.createElement)("div",{className:K?"btl-link-field-copyable is-invalid":"btl-link-field-copyable"},(0,r.createElement)("span",{className:"btl-static-link"},Ze+"/"),(0,r.createElement)(F.D0,{className:"btl-dynamic-link",id:"short_url",name:"short_url",onChange:function(t){e.setFieldValue("short_url",t.target.value.replace(/\s+/g,"-")),Z(!1)},disabled:ke,required:!0}),(0,r.createElement)(Q.A,{siteUrl:Ze,shortUrl:e.values.short_url}))),1==K&&(0,r.createElement)("div",{className:"errorlog"},"Already Exists")),(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"catId"},(0,i.__)("Category","betterlinks")),(0,r.createElement)(q.A,{catId:parseInt(u),data:x,fieldName:"cat_id",setFieldValue:e.setFieldValue,disabled:ke})),(0,r.createElement)("div",{className:"btl-modal-form-group"},(0,r.createElement)("label",{className:"btl-modal-form-label",htmlFor:"tags"},(0,i.__)("Tags","betterlinks")),(0,r.createElement)(Y,{linkId:d?parseInt(d.ID):0,fieldName:"tags_id",data:x,setFieldValue:e.setFieldValue,disabled:ke})),(null==je?void 0:je.length)>0&&(0,r.createElement)(be,{props:e,customFields:je}),betterLinksHooks.applyFilters("isShowLinkSubmitButton",!0,d)&&(0,r.createElement)("div",{className:"btl-modal-form-group btl-modal-form-group-submit"},(0,r.createElement)("label",{className:"btl-modal-form-label"}),(0,r.createElement)("button",{type:"submit",className:"btl-modal-submit-button"},d&&""===v?(0,i.__)("Update","betterlinks"):(0,i.__)("Publish","betterlinks")))),(0,r.createElement)("div",{className:"btl-entry-content-right"},(0,r.createElement)("div",{className:"link-options ".concat(fe.options?"link-options--open":"")},(0,r.createElement)("button",{className:"link-options__head",type:"button",onClick:function(){return Qe("options")}},(0,r.createElement)("h4",{className:"link-options__head--title"},(0,i.__)("Link Options","betterlinks"))," ",(0,r.createElement)("i",{className:"btl btl-angle-arrow-down"})),(0,r.createElement)("div",{className:"link-options__body"},h&&(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(F.D0,{className:"btl-check",name:"openInNewTab",type:"checkbox",onChange:function(){return e.setFieldValue("openInNewTab",!e.values.openInNewTab)},disabled:!1}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Open In New Tab","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will open your link in a new tab when clicked","betterlinks"))))),(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(F.D0,{className:"btl-check",name:"nofollow",type:"checkbox",onChange:function(){return e.setFieldValue("nofollow",!e.values.nofollow)},disabled:ke}),(0,r.createElement)("span",{className:"text"},(0,i.__)("No Follow","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will add nofollow attribute to your link. (Recommended)","betterlinks"))))),(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(F.D0,{className:"btl-check",name:"sponsored",type:"checkbox",onChange:function(){return e.setFieldValue("sponsored",!e.values.sponsored)},disabled:ke}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Sponsored","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will add sponsored attribute to your link. (Recommended for Affiliate links)","betterlinks"))))),(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(F.D0,{className:"btl-check",name:"param_forwarding",type:"checkbox",onChange:function(){return e.setFieldValue("param_forwarding",!e.values.param_forwarding)},disabled:ke}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Parameter Forwarding","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will pass the parameters you have set in the target URL","betterlinks"))))),(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(F.D0,{className:"btl-check",name:"track_me",type:"checkbox",onChange:function(){return e.setFieldValue("track_me",!e.values.track_me)},disabled:ke}),(0,r.createElement)("span",{className:"text"},(0,i.__)("Tracking","betterlinks"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will let you check Analytics report of your links","betterlinks"))))),!A.JT&&(0,r.createElement)("label",{className:"btl-checkbox-field link-options--teasers",onClick:function(){return Je()}},(0,r.createElement)(F.D0,{disabled:!0,className:"btl-check",type:"checkbox",checked:!1,onChange:function(){return Je()},onClick:function(){return Je()}}),(0,r.createElement)("span",{className:"text btl-text"},(0,i.__)("Uncloak","betterlinks"),(0,r.createElement)(ne.A,null),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("This will uncloak your link","betterlinks"))))),betterLinksHooks.applyFilters("linkOptionsBasic",null,_e(_e({},e),{},{isDisableLinkFormEditView:ke,Field:F.D0},f)))),!h&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"link-options link-options--advanced ".concat(fe.advanced?"link-options--open":"")},(0,r.createElement)("button",{className:"link-options__head",type:"button",onClick:function(){return Qe("advanced")}},(0,r.createElement)("h4",{className:"link-options__head--title"},(0,i.__)("Advanced","betterlinks")," ",!A.JT&&(0,r.createElement)(ne.A,null)),(0,r.createElement)("i",{className:"btl btl-angle-arrow-down"})),(0,r.createElement)(he,{openUpgradeToProModal:Je}),(0,r.createElement)(r.Fragment,null,betterLinksHooks.applyFilters("linkOptionsAdvanced",null,_e(_e(_e({},e),f),{},{password:Ce,metaTag:Se})))),(0,r.createElement)("div",{className:"link-options link-options--dynamic-redirect ".concat(fe.dynamicRedirect?"link-options--open":"")},(0,r.createElement)("button",{className:"link-options__head",type:"button",onClick:function(){return Qe("dynamicRedirect")}},(0,r.createElement)("h4",{className:"link-options__head--title"},(0,i.__)("Dynamic Redirects","betterlinks")," ",!A.JT&&(0,r.createElement)(ne.A,null)," ",A.JT&&e.values.dynamic_redirect&&e.values.dynamic_redirect.type&&"none"!==e.values.dynamic_redirect.type?(0,r.createElement)("span",{className:"status"},(0,i.__)("ON","betterlinks")):"")," ",(0,r.createElement)("i",{className:"btl btl-angle-arrow-down"})),(0,r.createElement)("div",{className:"link-options__body"},betterLinksHooks.applyFilters("linkOptionsDynamicRedirect",(0,r.createElement)(Ee,{openUpgradeToProModal:Je}),e))),(0,r.createElement)(me,{openAccordion:fe.optimizeMetaTags,openUpgradeToProModal:Je,form:e,settings:f,metaTag:Se,__handleToggle:We}),(0,r.createElement)(de,{openAccordion:fe.customTrackingScripts,openUpgradeToProModal:Je,__handleToggle:We,props:_e(_e({},e),{},{tracking:null==f?void 0:f.tracking,Field:F.D0})}),!A.JT&&(0,r.createElement)("div",null,(0,r.createElement)("div",{className:"link-options link-options--auto-link-keywords"},(0,r.createElement)("button",{className:"link-options__head",type:"button",onClick:function(){return Je()}},(0,r.createElement)("h4",{className:"link-options__head--title"},(0,i.__)("Auto-Link Keywords","betterlinks")," ",(0,r.createElement)(ne.A,null)))))))),betterLinksHooks.applyFilters("isShowLinkSubmitButton",!0,d)&&(0,r.createElement)("div",{className:"btl-modal-form-group btl-modal-form-group-submit-medium-device"},(0,r.createElement)("label",{className:"btl-modal-form-label"}),(0,r.createElement)("button",{type:"submit",className:"btl-modal-submit-button"},d?(0,i.__)("Update","betterlinks"):(0,i.__)("Publish","betterlinks"))))}))))};const ve=(0,n.Ng)((function(e){return{settings:e.settings,terms:e.terms,password:e.password,metaTags:e.metaTags}}),(function(e){return{fetch_terms_data:(0,c.zH)(v.M3,e),fetch_tracking_settings:(0,c.zH)(g.nQ,e)}}))(ge);ge.propTypes=ke;var fe={catId:w().number,catName:w().string,catSlug:w().string},xe=function(e){var t=e.catId,l=e.catName,n=e.catSlug,c=e.update_cat,s=e.delete_cat,m=(0,r.useState)(!1),u=(0,o.A)(m,2),d=u[0],b=u[1],p=(0,r.useState)(!1),h=(0,o.A)(p,2),E=h[0],y=h[1],_=(0,r.useState)(!1),k=(0,o.A)(_,2),g=k[0],v=k[1];function f(){y(!1),b(!1)}return(0,r.createElement)(a().Fragment,null,(0,r.createElement)("div",{className:"category-head"},(0,r.createElement)("h4",{className:"title"},l),"uncategorized"!=n&&betterLinksHooks.applyFilters("isShowCatControl",!0)&&(0,r.createElement)("div",{className:"dropdown"},(0,r.createElement)("button",{className:"icon",onClick:function(){return v(!1),void y(!E)}},(0,r.createElement)("i",{className:"btl btl-more"})),(0,r.createElement)("div",{className:"dropdown-menu"},E&&(0,r.createElement)("ul",null,(0,r.createElement)("li",null,(0,r.createElement)("button",{onClick:function(){b(!0)},className:"link"},(0,i.__)("Edit","betterlinks"))),(0,r.createElement)("li",null,(0,r.createElement)("button",{className:"link delete",onClick:function(){return y(!E),void v(!g)}},(0,i.__)("Delete","betterlinks")))),g&&(0,r.createElement)("div",{className:"btl-confirm-message"},(0,r.createElement)("p",{className:"action-text"},(0,i.__)("Are You Sure?","betterlinks")),(0,r.createElement)("div",{className:"action-set"},(0,r.createElement)("button",{className:"action yes",onClick:function(){v(!1),v(!1),s({cat_id:t})}},(0,i.__)("Yes","betterlinks")),(0,r.createElement)("button",{className:"action no",onClick:function(){y(!1),v(!1)}},(0,i.__)("No","betterlinks"))))))),(0,r.createElement)(I(),{isOpen:d,onRequestClose:f,style:A.Qu,ariaHideApp:!1},(0,r.createElement)("span",{className:"btl-close-modal",onClick:f},(0,r.createElement)("i",{className:"btl btl-cancel"})),(0,r.createElement)(S,{catId:parseInt(t),catName:l,catSlug:n,submitHandler:c,hideHandler:f})))};xe.propTypes=fe;const we=(0,n.Ng)((function(e){return{links:e.links}}),(function(e){return{update_cat:(0,c.zH)(k.Ts,e),delete_cat:(0,c.zH)(k.hI,e)}}))(xe);var Ne=l(15286),Ce=l.n(Ne);function Fe(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Fe=function(){return!!e})()}var Ae=function(e){function t(e){var l,r,a,n;return(0,u.A)(this,t),r=this,a=t,n=[e],a=(0,p.A)(a),(l=(0,b.A)(r,Fe()?Reflect.construct(a,n||[],(0,p.A)(r).constructor):a.apply(r,n))).download=l.download.bind(l),l}return(0,h.A)(t,e),(0,d.A)(t,[{key:"componentDidMount",value:function(){this.download()}},{key:"download",value:function(){var e=document.querySelector(".betterlinksqrcode > canvas");this.downloadRef.href=e.toDataURL(),this.downloadRef.download="betterlinks-".concat(this.props.value,"-QR.png")}},{key:"render",value:function(){var e=this;return(0,r.createElement)(a().Fragment,null,(0,r.createElement)("div",{className:"btl-qrcode-modal"},(0,r.createElement)("div",{className:"betterlinksqrcode"},(0,r.createElement)(Ce(),{value:(0,A.Ks)(this.props.value),size:300,level:"H"}),(0,r.createElement)("p",{className:"btl-qrcode-modal__note"},(0,i.__)("Hit the","betterlinks")," ",(0,r.createElement)("strong",null,(0,i.__)('"Download"',"betterlinks"))," ",(0,i.__)("button below to save the QR Code on your device","betterlinks"))),(0,r.createElement)("a",{className:"btn-qrcode-download",ref:function(t){return e.downloadRef=t}},(0,r.createElement)("i",{className:"btl btl-download-arrow"}))))}}])}(a().Component),Oe={overlay:{background:"rgba(35, 40, 45, 0.62)"},content:{top:"50%",left:"50%",right:"auto",bottom:"auto",width:"auto",height:"auto",marginRight:"-50%",transform:"translate(-50%, -50%)"}};function Se(e){var t=e.shortUrl,l=(0,r.useState)(!1),n=(0,o.A)(l,2),c=n[0],s=n[1];function m(){s(!1)}return(0,r.createElement)(a().Fragment,null,(0,r.createElement)(r.Fragment,null,(0,r.createElement)("button",{className:"dnd-link-button btl-tooltip",onClick:function(){s(!0)}},(0,r.createElement)("span",{className:"icon"},(0,r.createElement)("i",{className:"btl btl-qr-scanner"})),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("QR Code","betterlinks"))),(0,r.createElement)(I(),{isOpen:c,onRequestClose:m,style:Oe,ariaHideApp:!1},(0,r.createElement)("span",{className:"btl-close-modal",onClick:m},(0,r.createElement)("i",{className:"btl btl-cancel"})),(0,r.createElement)(Ae,{value:t}))))}Se.propTypes={};var De=l(79296),Te={isShowAnalytics:w().bool,isShowVisitLink:w().bool,isShowCopyLink:w().bool,isShowEditLink:w().bool,isShowDeleteLink:w().bool,catId:w().number,catName:w().string,submitLinkHandler:w().func,deleteLinkHandler:w().func,data:w().object,handle_link_favorite:w().func},Le=function(e){var t=e.isAlowQr,l=e.isShowCopyLink,n=void 0===l||l,c=e.isShowAnalytics,s=void 0!==c&&c,m=e.isShowVisitLink,u=void 0===m||m,d=e.isShowEditLink,b=void 0===d||d,p=e.isShowDeleteLink,h=void 0===p||p,E=e.data,y=e.catId,_=e.catName,k=e.submitLinkHandler,g=e.deleteLinkHandler,v=e.addNewLink,f=(0,r.useState)(!1),x=(0,o.A)(f,2),w=x[0],N=x[1],C=(0,r.useState)(!1),F=(0,o.A)(C,2),O=F[0],S=F[1],D=betterLinksHooks.applyFilters("site_url",A.IV);return(0,r.createElement)(a().Fragment,null,betterLinksHooks.applyFilters("linkQuickActionNewField","",{data:E,ReactLink:De.N_}),s&&E.analytic&&(0,r.createElement)("button",{className:"dnd-link-button btl-tooltip"},(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("Clicks: ","betterlinks")+ +E.analytic.link_count+" / "+(0,i.__)("Unique Clicks: ","betterlinks")+ +E.analytic.ip),(0,r.createElement)("span",{className:"icon"},(0,A.Ag)(E.analytic,E.ID))),O?(0,r.createElement)("div",{className:"btl-confirm-message"},(0,r.createElement)("span",{className:"action-text"},(0,i.__)("Are You Sure?","betterlinks")),(0,r.createElement)("div",{className:"action-set"},(0,r.createElement)("button",{className:"action yes",onClick:function(){return S(!1),void g({ID:E.ID,short_url:E.short_url,term_id:y})}},(0,i.__)("Yes","betterlinks")),(0,r.createElement)("button",{className:"action no",onClick:function(){S(!1)}},(0,i.__)("No","betterlinks")))):(0,r.createElement)(r.Fragment,null,u&&(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("a",{className:"dnd-link-button",href:D+"/"+E.short_url,target:"_blank"},(0,r.createElement)("i",{className:"btl btl-visit-url"})),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("Visit Link","betterlinks"))),t&&(0,r.createElement)(Se,{shortUrl:E.short_url}),n&&(0,r.createElement)("button",{className:"dnd-link-button btl-tooltip",onClick:function(){return e=E.short_url,(0,A.Nj)(e),N(!0),void window.setTimeout((function(){N(!1)}),3e3);var e}},(0,r.createElement)("span",{className:"icon"},w?(0,r.createElement)("span",{className:"dashicons dashicons-yes"}):(0,r.createElement)("i",{className:"btl btl-link"})),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("Copy Link","betterlinks"))),b&&(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)(ve,{catId:parseInt(y),catName:_,data:E,submitHandler:k}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("Edit Link","betterlinks"))),h&&(0,r.createElement)("button",{type:"button",className:"dnd-link-button delete-button btl-tooltip",onClick:function(){S(!O)}},(0,r.createElement)("span",{className:"icon"},(0,r.createElement)("i",{className:"btl btl-delete"})),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("Delete","betterlinks"))),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)(ve,{catId:parseInt(y),catName:_,data:E,submitHandler:v,type:"duplicate"}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("Create Duplicate","betterlinks")))))};Le.propTypes=Te;const Pe=Le,He=(0,n.Ng)(null,(function(e){return{handle_link_favorite:(0,c.zH)(k.NQ,e)}}))((function(e){var t,l=e.handle_link_favorite,a=e.data,n=(0,r.useState)((null===(t=a.favorite)||void 0===t?void 0:t.favForAll)||!1),c=(0,o.A)(n,2),s=c[0],m=c[1];return betterLinksHooks.applyFilters("betterLinksIsShowFavorite",!0)?(0,r.createElement)("button",{className:"btl-tooltip dnd-link-button btl-fav-link no-btn c-pointer ".concat(s?"favorated":"unfavorated"),onClick:function(){var e=!s;m(e),l({ID:a.ID,favForAll:e})}},(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("".concat(s?"Unmark":"Mark"," as Favorite"),"betterlinks")),(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"favorite-svg",viewBox:"0 0 512 512",xmlSpace:"preserve"},(0,r.createElement)("path",{className:"fav-icon-svg-path",d:"M392.2 317.5c-3 2.9-4.4 7.1-3.7 11.3L414 477.4c1.2 7-3.5 13.6-10.5 14.9-2.8.5-5.6 0-8.1-1.3L262 420.9c-3.7-2-8.2-2-12 0L116.6 491c-3.1 1.7-6.8 1.9-10.1.8-6-2.1-9.5-8.1-8.5-14.4l25.4-148.5c.7-4.2-.7-8.4-3.7-11.4L11.9 212.4c-5.1-5-5.2-13.1-.2-18.2 2-2 4.6-3.3 7.3-3.7l149.1-21.7c4.2-.6 7.8-3.2 9.7-7l66.7-135c2.6-5.3 8.4-8.1 14.2-6.9 3.9.7 7.2 3.3 8.9 6.9l66.7 135c1.9 3.8 5.5 6.4 9.7 7l149 21.6c7 1 11.9 7.6 10.9 14.6-.4 2.7-1.7 5.3-3.7 7.2l-108 105.3z"}))):(0,r.createElement)("button",{className:"btl-tooltip dnd-link-button btl-fav-link no-edit no-btn ".concat(s?"favorated":"unfavorated"),onClick:function(){return!1}},(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"favorite-svg",viewBox:"0 0 512 512",xmlSpace:"preserve"},(0,r.createElement)("path",{className:"fav-icon-svg-path",d:"M392.2 317.5c-3 2.9-4.4 7.1-3.7 11.3L414 477.4c1.2 7-3.5 13.6-10.5 14.9-2.8.5-5.6 0-8.1-1.3L262 420.9c-3.7-2-8.2-2-12 0L116.6 491c-3.1 1.7-6.8 1.9-10.1.8-6-2.1-9.5-8.1-8.5-14.4l25.4-148.5c.7-4.2-.7-8.4-3.7-11.4L11.9 212.4c-5.1-5-5.2-13.1-.2-18.2 2-2 4.6-3.3 7.3-3.7l149.1-21.7c4.2-.6 7.8-3.2 9.7-7l66.7-135c2.6-5.3 8.4-8.1 14.2-6.9 3.9.7 7.2 3.3 8.9 6.9l66.7 135c1.9 3.8 5.5 6.4 9.7 7l149 21.6c7 1 11.9 7.6 10.9 14.6-.4 2.7-1.7 5.3-3.7 7.2l-108 105.3z"})))}));function je(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,r)}return l}function Ie(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?je(Object(l),!0).forEach((function(t){(0,m.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):je(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}const ze=(0,n.Ng)((function(e){return{favouriteSort:e.favouriteSort}}),null)((function(e){var t,l=e.catId,a=e.item,n=e.index,c=e.is_allow_qr,i=e.term_name,s=e.edit_link,o=e.delete_link,m=e.add_new_link,u=e.favouriteSort.sortByFav;if(null!=a&&null!==(t=a.favorite)&&void 0!==t&&t.favForAll||!u){var d=(0,ie.M)({data:a,view:"dnd"});return(0,r.createElement)(f.sx,{key:"cat-".concat(l,"-item_").concat(a.ID),draggableId:"cat-".concat(l,"-item_").concat(a.ID),index:n},(function(e,t){return(0,r.createElement)("div",Ie(Ie({className:"btl-dnd-link ".concat(t.isDragging?"btl-dnd-link-dragging":""),ref:e.innerRef},e.draggableProps),e.dragHandleProps),(0,r.createElement)("div",{className:"btl-dnd-link-body"},(0,r.createElement)("h3",{className:"dnd-link-title"},(0,r.createElement)("span",{className:"icon"},(0,r.createElement)("img",{src:A.hq+"assets/images/move-icon.svg",alt:"icon"})),(0,r.createElement)(He,{data:a}),d,(0,r.createElement)(ve,{catId:parseInt(l),catName:i,data:a,submitHandler:s},(0,r.createElement)("span",{className:"text"},a.link_title))),(0,r.createElement)("div",{className:"btl-dnd-link-button-group"},(0,r.createElement)(Pe,{isAlowQr:c,isShowAnalytics:!0,catId:parseInt(l),catName:i,submitLinkHandler:s,deleteLinkHandler:o,addNewLink:m,data:a,isShowEditLink:betterLinksHooks.applyFilters("betterLinksIsShowViewLink",!0),isShowDeleteLink:betterLinksHooks.applyFilters("betterLinksIsShowDeleteLink",!0)}))))}))}}));var Re=l(23050),Ve=l(74139);function Me(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,r)}return l}function qe(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(qe=function(){return!!e})()}var Ue=function(e){function t(){return(0,u.A)(this,t),e=this,l=t,r=arguments,l=(0,p.A)(l),(0,b.A)(e,qe()?Reflect.construct(l,r||[],(0,p.A)(e).constructor):l.apply(e,r));var e,l,r}return(0,h.A)(t,e),(0,d.A)(t,[{key:"shouldComponentUpdate",value:function(e){return e.lists!==this.props.lists}},{key:"render",value:function(){var e=this.props,t=e.lists,l=e.settings,a=e.edit_link,n=e.delete_link,c=e.add_new_link,i=e.catId;return t.map((function(e,t){return!!e.link_title&&(0,r.createElement)(ze,{is_allow_qr:l&&l.is_allow_qr,edit_link:a,delete_link:n,add_new_link:c,catId:i,key:"cat-".concat(i,"-item-").concat(t),item:e,index:t})}))}}])}(React.Component),Be=(0,r.memo)((function(e){var t=e.ind,l=e.el,a=e.provided,n=e.props,c=n.favouriteSort.sortByFav,i=l.lists;return(0,A.eh)(i,c)&&c?(0,r.createElement)("div",{className:"dnd-category",style:{display:"none"}},(0,r.createElement)("div",{ref:a.innerRef})):(0,r.createElement)("div",{className:"dnd-category"},(0,r.createElement)(we,{catId:parseInt(t),catName:l.term_name,catSlug:l.term_slug}),(0,r.createElement)("div",function(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?Me(Object(l),!0).forEach((function(t){(0,m.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):Me(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}({ref:a.innerRef,className:"dnd-category-body-wrap"},a.droppableProps),(0,r.createElement)("div",{className:"category-body"},(0,r.createElement)(Ue,{settings:n.settings.settings,edit_link:n.edit_link,delete_link:n.delete_link,add_new_link:n.add_new_link,catId:t,lists:i}),a.placeholder),(0,r.createElement)("div",{className:"category-footer"},betterLinksHooks.applyFilters("betterLinksIsShowWriteLink",!0)&&!c&&(0,r.createElement)(ve,{catId:parseInt(t),catName:l.term_name,submitHandler:n.add_new_link}))))}));const Je=(0,n.Ng)((function(e){return{links:e.links,settings:e.settings,terms:e.terms,favouriteSort:e.favouriteSort,password:e.password,metaTags:e.metaTags}}),(function(e){return{fetch_links_data:(0,c.zH)(k.qh,e),fetch_settings_data:(0,c.zH)(g.kc,e),fetch_tracking_settings:(0,c.zH)(g.nQ,e),onDragEnd:(0,c.zH)(k.nG,e),add_new_cat:(0,c.zH)(k.iP,e),add_new_link:(0,c.zH)(k.A4,e),edit_link:(0,c.zH)(k.wq,e),delete_link:(0,c.zH)(k.NZ,e),fetch_terms_data:(0,c.zH)(v.M3,e),fetch_links_password:(0,c.zH)(Re.f,e),fetch_meta_tags:(0,c.zH)(Ve.D6,e)}}))((function(e){var t=e.links.links,l=e.settings.settings,a=e.terms.terms,n=e.favouriteSort.sortByFav,c=e.password.password,s=e.metaTags.metaTags;return(0,r.useEffect)((function(){l||(e.fetch_settings_data(),e.fetch_tracking_settings()),t||e.fetch_links_data(),a||e.fetch_terms_data(),c||e.fetch_links_password(),s||e.fetch_meta_tags()}),[]),0===(0,A._$)(t)&&n?(0,r.createElement)("div",{className:"dnd-not-found"},(0,r.createElement)("div",{style:{padding:24}},(0,i.__)("There are no records to display","betterlinks"))):(0,r.createElement)(r.Fragment,null,t&&l&&a?(0,r.createElement)("div",{className:"dnd-category-wrapper ".concat(t?"":"d-flex")},(0,r.createElement)(f.JY,{onDragEnd:e.onDragEnd},t&&Object.entries(t).filter((function(e){return!(0===e[1].lists.length&&"uncategorized"===e[1].term_slug)})).map((function(t){var l=(0,o.A)(t,2),a=l[0],n=l[1];return(0,r.createElement)(f.gL,{key:a,droppableId:a},(function(t,l){return(0,r.createElement)(Be,{ind:a,el:n,provided:t,snapshot:l,props:e})}))})),betterLinksHooks.applyFilters("betterLinksIsShowWriteCat",!0)&&!n&&(0,r.createElement)(D,{createCatHandler:e.add_new_cat}))):(0,r.createElement)(_,null))}));var Ye=l(83757),Qe=l(979),We=l(38443),Xe=l(50011),Ke=l(46005),Ze=l(30020);const Ge=function(e){var t=(0,r.useState)({}),l=(0,o.A)(t,2),n=l[0],c=l[1],s=(0,r.useState)(!1),m=(0,o.A)(s,2),u=m[0],d=m[1];return(0,r.createElement)(a().Fragment,null,(0,r.createElement)("div",{className:"btl-links-filter"},e.bulkActionData.selectedCount>0&&(0,r.createElement)("div",{className:"btl-bulk-actions"},(0,r.createElement)(Ke.Ay,{className:"btl-list-view-select",classNamePrefix:"btl-react-select",defaultValue:{value:"",label:(0,i.__)("Bulk Actions","betterlinks")},value:null!=n&&n.value?n:{value:"",label:(0,i.__)("Bulk Actions","betterlinks")},options:[{value:"delete",label:(0,i.__)("Delete","betterlinks")}],onChange:function(e){return c(e)}}),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("button",{className:"btl-link-apply-button",onClick:function(){!function(e,t,l,r,a){if("delete"===t.value){r(!1);var n=[];return e.map((function(e){n.push({ID:e.ID,term_id:e.cat_id,short_url:e.short_url})})),a(),void l(n)}r(!0)}(e.bulkActionData.selectedRows,n,e.deleteLinkHandler,d,e.setToggledClearRows),c({})}},(0,i.__)("Apply","betterlinks")),u&&"delete"!==n.value&&(0,r.createElement)("span",{className:"btl-tooltiptext"},"Please Select Action."))),(0,r.createElement)("div",{className:"btl-click-filter"},(0,r.createElement)("input",{id:"search",type:"text",placeholder:(0,i.__)("Search","betterlinks"),value:e.filterText,onChange:e.onFilter})),(0,r.createElement)(Ke.Ay,{className:"btl-list-view-select btl-category-filter",classNamePrefix:"btl-react-select",placeholder:"Categories",value:e.selectedCategory,options:e.catItems,onChange:function(t){return e.categorySelectHandler(t)},isClearable:!0}),(0,r.createElement)(Ke.Ay,{className:"btl-list-view-select btl-category-filter",classNamePrefix:"btl-react-select",placeholder:"Tags",value:e.selectedTag,options:e.tagItems,onChange:function(t){return e.tagSelectHandler(t)},isClearable:!0}),(0,r.createElement)(Ke.Ay,{className:"btl-list-view-select btl-shortable-filter",classNamePrefix:"btl-react-select",placeholder:"Sort by Clicks",options:[{value:"mostClicks",label:(0,i.__)("Most Clicks","betterlinks")},{value:"leastClicks",label:(0,i.__)("Least Clicks","betterlinks")},{value:"mostUniqueClicks",label:(0,i.__)("Most Unique Clicks","betterlinks")},{value:"leastUniqueClicks",label:(0,i.__)("Least Unique Clicks","betterlinks")}],value:e.selectedClicksType,onChange:function(t){return e.setClicksType(t)},isClearable:!0}),(0,r.createElement)(Ke.Ay,{className:"btl-list-view-select",classNamePrefix:"btl-react-select",placeholder:"All Dates",options:[{value:"mostRecent",label:(0,i.__)("Most Recent","betterlinks")},{value:"leastRecent",label:(0,i.__)("Least Recent","betterlinks")},{value:"custom",label:(0,i.__)("Custom","betterlinks")}],value:e.selectedDateType,onChange:function(t){return e.dateHandler(t)},isClearable:!0}),e.selectedDateType&&"custom"===e.selectedDateType.value&&(0,r.createElement)(a().Fragment,null,(0,r.createElement)("button",{className:"btl-list-view-calendar",onClick:function(){return e.dateHandler({value:"custom",label:(0,i.__)("Custom","betterlinks")})}},(0,r.createElement)("span",{className:"dashicons dashicons-calendar"}),String(e.customDateFilter[0].startDate).slice(4,15)," - ",String(e.customDateFilter[0].endDate).slice(4,15))),(0,r.createElement)("button",{className:"btl-link-filter-button",onClick:e.resetFilterHandler},"Reset Filter"),e.isOpenCustomDateFilter&&(0,r.createElement)("div",{className:"btl-date-range-picker-wrap"},(0,r.createElement)("div",{className:"btl-date-range-picker"},(0,r.createElement)("button",{className:"btn-date-range-close",onClick:function(){return(0,A.jT)(),e.setIsOpenCustomDateFilter(!1),void e.dateHandler(null)}},(0,r.createElement)("span",{className:"dashicons dashicons-no-alt"})),(0,r.createElement)(Ze.Ur,{onChange:function(t){return function(t){e.setCustomDateFilter([t.selection]),t.selection.endDate!=t.selection.startDate&&((0,A.jT)(),e.setIsOpenCustomDateFilter(!1))}(t)},showSelectionPreview:!0,moveRangeOnFirstSelection:!1,months:2,ranges:e.customDateFilter,direction:"horizontal"})))))};var $e=l(60907),et=function(e){var t,l=null==e||null===(t=e.settings)||void 0===t||null===(t=t.settings)||void 0===t?void 0:t.is_allow_qr;return[{name:(0,i.__)("Title","betterlinks"),selector:"link_title",sortable:!1,width:"255px",cell:function(t){var l=(0,ie.M)({data:t,view:"list"});return!!t.link_title&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)(He,{data:t}),l,(0,r.createElement)(ve,{catId:parseInt(t.cat_id),catName:"",data:t,submitHandler:e.edit_link},(0,r.createElement)("div",{className:"btl-link-title"},t.link_title)))}},{name:(0,i.__)("Shortened URL","betterlinks"),selector:"short_url",sortable:!1,cell:function(e){return(0,r.createElement)(Xe.A,{shortUrl:e.short_url})}},{name:(0,i.__)("Target URL","betterlinks"),selector:"target_url",sortable:!1,cell:function(e){return(0,r.createElement)("div",{className:"btl-short-url-wrapper"},(0,r.createElement)("span",{className:"btl-short-url btl-truncate",title:e.target_url},e.target_url),(0,r.createElement)("a",{className:"dnd-link-button",href:e.target_url,target:"_blank"},(0,r.createElement)("i",{className:"btl btl-visit-url"})))}},{name:(0,i.__)("Redirect Type","betterlinks"),selector:"redirect_type",sortable:!1,width:"80px",cell:function(e){return(0,r.createElement)("div",null,"cloak"==e.redirect_type?"Cloaked":e.redirect_type)}},{name:(0,i.__)("Clicks","betterlinks"),selector:"",sortable:!1,width:"120px",cell:function(e){return(0,r.createElement)("div",null,e.analytic?(0,r.createElement)("button",{className:"dnd-link-button btl-tooltip"},(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("Clicks: ","betterlinks")+e.analytic.link_count+" / "+(0,i.__)("Unique Clicks: ","betterlinks")+e.analytic.ip),(0,r.createElement)("span",{className:"icon"},(0,A.Ag)(e.analytic,e.ID))):(0,r.createElement)("button",{className:"dnd-link-button btl-tooltip"},(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("Clicks: 0 / ","betterlinks")+(0,i.__)("Unique Clicks: 0","betterlinks")),(0,r.createElement)("span",{className:"icon"},(0,i.__)("0/0","betterlinks"))))}},{name:(0,i.__)("Date","betterlinks"),selector:"link_date",sortable:!1,width:"120px",cell:function(e){return(0,r.createElement)("div",null,(0,We.dateI18n)(A.VY||"F j, Y",new Date(e.link_date)))}},{name:(0,i.__)("Action","betterlinks"),selector:"",sortable:!1,width:"200px",cell:function(t){return(0,r.createElement)("div",{className:"btl-list-view-action-wrapper"},(0,r.createElement)(Pe,{isAlowQr:l,isShowVisitLink:!0,isShowAnalytics:!1,isShowCopyLink:!1,catId:parseInt(t.cat_id),submitLinkHandler:e.edit_link,deleteLinkHandler:e.delete_link,addNewLink:e.add_new_link,data:t,isShowEditLink:betterLinksHooks.applyFilters("betterLinksIsShowViewLink",!0),isShowDeleteLink:betterLinksHooks.applyFilters("betterLinksIsShowDeleteLink",!0)}))}}]};const tt=(0,n.Ng)((function(e){return{links:e.links,settings:e.settings,terms:e.terms,favouriteSort:e.favouriteSort,password:e.password,metaTags:e.metaTags}}),(function(e){return{fetch_links_data:(0,c.zH)(k.qh,e),fetch_settings_data:(0,c.zH)(g.kc,e),fetch_tracking_settings:(0,c.zH)(g.nQ,e),add_new_cat:(0,c.zH)(k.iP,e),add_new_link:(0,c.zH)(k.A4,e),edit_link:(0,c.zH)(k.wq,e),delete_link:(0,c.zH)(k.NZ,e),fetch_terms_data:(0,c.zH)(v.M3,e),fetch_links_password:(0,c.zH)(Re.f,e),fetch_meta_tags:(0,c.zH)(Ve.D6,e)}}))((function(e){var t=e.links.links,l=e.settings.settings,n=e.terms.terms,c=e.password.password,i=e.metaTags.metaTags,s=(0,r.useState)({}),m=(0,o.A)(s,2),u=m[0],d=m[1],b=(0,r.useState)(""),p=(0,o.A)(b,2),h=p[0],E=p[1],y=(0,r.useState)(!1),_=(0,o.A)(y,2),g=_[0],v=_[1],f=(0,r.useState)(null),x=(0,o.A)(f,2),w=x[0],N=x[1],C=(0,r.useState)(null),F=(0,o.A)(C,2),O=F[0],S=F[1],D=(0,r.useState)(null),L=(0,o.A)(D,2),P=L[0],H=L[1],j=(0,r.useState)(null),I=(0,o.A)(j,2),z=I[0],R=I[1],V=(0,r.useState)(!1),M=(0,o.A)(V,2),q=M[0],U=M[1],B=(0,r.useState)([{startDate:(0,Qe.A)(new Date,30),endDate:new Date,key:"selection"}]),J=(0,o.A)(B,2),Y=J[0],Q=J[1],W=(0,r.useState)(!1),X=(0,o.A)(W,2),K=X[0],Z=X[1],G=e.favouriteSort.sortByFav;(0,r.useEffect)((function(){t||e.fetch_links_data(),l||(e.fetch_settings_data(),e.fetch_tracking_settings()),n||e.fetch_terms_data(),c||e.fetch_links_password(),i||e.fetch_meta_tags()}),[]);var $=t&&Object.values(t).reduce((function(e,t){return[].concat((0,T.A)(e),(0,T.A)(t.lists))}),[]),ee=t&&Object.entries(t).reduce((function(e,t){var l=(0,o.A)(t,2),r=l[0],a=l[1];return[].concat((0,T.A)(e),[{value:r,label:a.term_name}])}),[]),te=t&&Object.entries((0,A.Xl)(t)).reduce((function(e,t){return[].concat((0,T.A)(e),[{value:null==t?void 0:t[0],label:null==t?void 0:t[1]}])}),[]),le=function(e){R(e),e&&"custom"==e.value?((0,A.AX)(),U(!q)):U(!1)},re=function(){E(""),N(null),S(null),H(null),R(null),U(!1)},ae=function(){Z(!K)},ne=a().useMemo((function(){return(0,r.createElement)(Ge,{deleteLinkHandler:e.delete_link,catItems:ee,tagItems:te,bulkActionData:u,onFilter:function(e){return E(e.target.value)},selectedCategory:w,categorySelectHandler:N,selectedTag:O,tagSelectHandler:S,selectedClicksType:P,setClicksType:H,selectedDateType:z,dateHandler:le,customDateFilter:Y,setCustomDateFilter:Q,isOpenCustomDateFilter:q,setIsOpenCustomDateFilter:U,onClear:function(){h&&(v(!g),E(""))},filterText:h,resetFilterHandler:re,setToggledClearRows:ae})}),[h,g,u,k.NZ,ee,Y,Q,q,U,re]);return(0,r.createElement)(a().Fragment,null,(0,r.createElement)("div",{className:"btl-list-view"},t?(0,r.createElement)(Ye.Ay,{className:"btl-list-view-table",columns:et(e),data:(0,A.Cg)($,h,w,P,z,Y,G,O),pagination:!0,paginationResetDefaultPage:g,subHeader:!0,highlightOnHover:!0,onChangeRowsPerPage:function(e){return localStorage.setItem("btlRowsPerPage",e)},paginationPerPage:+localStorage.getItem("btlRowsPerPage")||10,subHeaderComponent:ne,persistTableHead:!0,selectableRows:!0,selectableRowsVisibleOnly:!0,onSelectedRowsChange:function(e){return function(e){d(e)}(e)},clearSelectedRows:K,paginationRowsPerPageOptions:A._t}):(0,r.createElement)($e.A,null)))})),lt=(0,n.Ng)((function(e){return{activity:e.activity,favouriteSort:e.favouriteSort}}),(function(e){return{add_new_link:(0,c.zH)(k.A4,e),add_new_password:(0,c.zH)(Re.t,e)}}))((function(e){var t=e.add_new_link,l=e.add_new_password,n=e.activity,c=e.favouriteSort.sortByFav;return(0,r.createElement)(a().Fragment,null,(0,r.createElement)(s.A,{label:(0,i.__)("BetterLinks","betterlinks"),render:function(){return(0,r.createElement)(r.Fragment,null,betterLinksHooks.applyFilters("betterLinksIsShowWriteLink",!0)&&!c&&(0,r.createElement)("div",{className:"btl-create-links"},(0,r.createElement)(ve,{isShowIcon:!1,submitHandler:t,add_new_password:l})))}}),"list"==n.linksView?(0,r.createElement)(tt,null):(0,r.createElement)(Je,null))}))}}]);