"use strict";(globalThis.webpackChunkbetterlinks=globalThis.webpackChunkbetterlinks||[]).push([[488],{80220:(e,t,l)=>{l.d(t,{h:()=>s});var n,a=l(51609);function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)({}).hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},r.apply(null,arguments)}var s=function(e){return a.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 40 47"},e),n||(n=a.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"m19.557 26.171-2.514-1.864a5.6 5.6 0 0 0-7.834 1.162l-5.873 7.92a5.6 5.6 0 0 0 1.162 7.834l2.514 1.864a5.6 5.6 0 0 0 7.834-1.162l5.874-7.92a5.6 5.6 0 0 0-1.163-7.834M35.109 5.2l-2.514-1.864a5.6 5.6 0 0 0-7.834 1.162l-5.873 7.92a5.6 5.6 0 0 0 1.162 7.834l2.514 1.864a5.6 5.6 0 0 0 7.834-1.162l5.873-7.92A5.6 5.6 0 0 0 35.11 5.2M12.89 32.524l13.82-18.63"})))}},52360:(e,t,l)=>{l.d(t,{A:()=>r});var n=l(51609),a=l(27723);const r=function(e){var t=e.note,l=e.title,r=void 0===l?(0,a.__)("Note","betterlinks"):l;return(0,n.createElement)("span",{className:"btl-modal-customize-link-preview--note"},r,": ",t)}},80123:(e,t,l)=>{l.r(t),l.d(t,{default:()=>R});var n,a,r=l(51609),s=l(3453),i=l(27723),c=l(46005),o=l(2078),m=l(80702),p=l(83757),u=l(19735),b=[{link:"http://example.com",title:"Example",status:200,post_name:"Hello World",edit_link:"#",post_link:"#",post_type:"post"},{link:"https://testsite.org/",title:"Test Site",status:200,post_name:"Sample Page",edit_link:"#",post_link:"#",post_type:"page"},{link:"http://trialweb.org/",title:"Trial Web",status:404,post_name:"Trial Web",edit_link:"#",post_link:"#",post_type:"page"},{link:"https://demo-page.net",title:"Demo Page",status:200,post_name:"Demo Page",edit_link:"#",post_link:"#",post_type:"page"},{link:"http://genericdemo.org/",title:"Generic Demo",status:403,post_name:"Sample Product",edit_link:"#",post_link:"#",post_type:"product"}],d=[{name:(0,i.__)("Link","betterlinks"),selector:"link",sortable:!1,width:"300px",cell:function(e){return(0,r.createElement)("div",{className:"btl-short-url-wrapper"},(0,r.createElement)("span",{className:"btl-short-url btl-truncate",title:e.link},e.link),(0,r.createElement)("a",{className:"dnd-link-button",href:e.link,target:"_blank",style:{marginLeft:"5px"}},(0,r.createElement)("span",{className:"btl btl-visit-url"})))}},{name:(0,i.__)("Link Label","betterlinks"),selector:"title",sortable:!1,width:"200px",cell:function(e){return(0,r.createElement)("div",{title:e.title},(0,r.createElement)("span",null,e.title),(0,r.createElement)("button",{onClick:function(){},className:"btl-short-url-copy-button btl-tooltip"},(0,r.createElement)("span",{className:"icon"},(0,r.createElement)("img",{width:"20",src:u.hq+"/assets/images/copy-icon.svg",alt:"icon"})),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("Copy Label","betterlinks"))))}},{name:(0,i.__)("Status","betterlinks"),selector:"status",width:"150px",sortable:!1,cell:function(e){var t="Broken Link",l="danger";return e.status>=200&&e.status<=299||1==e.status?(t="Active",l="success"):403===e.status?(t="403 Forbidden",l="danger2"):401===e.status&&(t="401 Unauthorized",l="danger2"),(0,r.createElement)("div",{className:"betterlinks-broken-links-status-column btl-".concat(l)},(0,r.createElement)("span",null,t))}},{name:(0,i.__)("Post Title","betterlinks"),selector:"post_name",width:"300px",sortable:!1,cell:function(e){return(0,r.createElement)("div",{style:{display:"flex",alignItems:"center",columnGap:"15px"}},(0,r.createElement)("span",{title:e.post_name},e.post_name.length>50?e.post_name.slice(0,50)+"[...]":e.post_name))}},{name:(0,i.__)("Post Type","betterlinks"),selector:"post_type",width:"100px",sortable:!1},{name:(0,i.__)("Actions","betterlinks"),selector:"actions",width:"100px",sortable:!1,cell:function(e){var t=function(e){return e.preventDefault()};return(0,r.createElement)("div",{className:"btl-action-btns"},(0,r.createElement)("a",{title:"Edit - ".concat(e.post_name),onClick:t,style:{cursor:"not-allowed"}},(0,r.createElement)("span",{className:"btl btl-edit"}))," ",(0,r.createElement)("a",{title:"Preview - ".concat(e.post_name),style:{marginLeft:"5px",cursor:"not-allowed"},onClick:t},(0,r.createElement)("span",{className:"btl btl-visit-url"})))}}],k=l(73486),v=l(50384),h=l(77430),E=l(26758),f=l(83182),g=l(54392),_=l(80220);function w(){return w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)({}).hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},w.apply(null,arguments)}var y,N,x=function(e){return r.createElement("svg",w({xmlns:"http://www.w3.org/2000/svg",width:18,height:18,fill:"none"},e),n||(n=r.createElement("g",{stroke:"#58D83E",clipPath:"url(#Magnifer_svg__a)"},r.createElement("circle",{cx:8.625,cy:8.625,r:7.125,strokeWidth:1.125}),r.createElement("path",{strokeLinecap:"round",strokeWidth:1.125,d:"M13.875 13.875 16.5 16.5"}),r.createElement("path",{strokeLinecap:"round",strokeWidth:.85,d:"M12.5 6.813h-7M10.75 9H5.5M12.5 11.188h-7"}))),a||(a=r.createElement("defs",null,r.createElement("clipPath",{id:"Magnifer_svg__a"},r.createElement("path",{fill:"#fff",d:"M0 0h18v18H0z"})))))};function A(){return A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)({}).hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},A.apply(null,arguments)}var P,L,M,S=function(e){return r.createElement("svg",A({xmlns:"http://www.w3.org/2000/svg",width:18,height:18,fill:"none"},e),y||(y=r.createElement("g",{fill:"#6339CE",clipPath:"url(#bar-chart_svg__a)"},r.createElement("path",{d:"M6.375 9.75h-2.25a.375.375 0 0 0-.375.375v3.75c0 .207.168.375.375.375h2.25a.375.375 0 0 0 .375-.375v-3.75a.375.375 0 0 0-.375-.375M13.875 6.75h-2.25a.375.375 0 0 0-.375.375v6.75c0 .207.168.375.375.375h2.25a.375.375 0 0 0 .375-.375v-6.75a.375.375 0 0 0-.375-.375M10.125 3.75h-2.25a.375.375 0 0 0-.375.375v9.75c0 .207.168.375.375.375h2.25a.375.375 0 0 0 .375-.375v-9.75a.375.375 0 0 0-.375-.375"}))),N||(N=r.createElement("defs",null,r.createElement("clipPath",{id:"bar-chart_svg__a"},r.createElement("path",{fill:"#fff",d:"M3 3h12v12H3z"})))))};function O(){return O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)({}).hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},O.apply(null,arguments)}var C=function(e){return r.createElement("svg",O({xmlns:"http://www.w3.org/2000/svg",width:15,height:16,fill:"none"},e),P||(P=r.createElement("rect",{width:15,height:15,y:.5,fill:"#D84010",rx:7.5})),L||(L=r.createElement("g",{clipPath:"url(#broken-links_svg__a)"},r.createElement("path",{fill:"#fff",fillRule:"evenodd",d:"M7.86 5.562A1.434 1.434 0 0 1 9.888 7.59L8.792 8.685l-.61-.609.549-.548a.571.571 0 1 0-.808-.809l-.549.55-.61-.61zm-1.904.288 1.096-1.096a2.577 2.577 0 1 1 3.645 3.644L9.6 9.494l1.723 1.722a.425.425 0 0 1-.602.601l-7.09-7.09a.425.425 0 0 1 .601-.601zm-.891 1.7a.57.57 0 0 1 0 .808 1.434 1.434 0 0 0 2.028 2.028.571.571 0 0 1 .808.808A2.577 2.577 0 1 1 4.256 7.55a.57.57 0 0 1 .809 0",clipRule:"evenodd"}))),M||(M=r.createElement("defs",null,r.createElement("clipPath",{id:"broken-links_svg__a"},r.createElement("path",{fill:"#fff",d:"M3.5 4h8v8h-8z"})))))},T=l(52360),z=l(72057);const B=function(){var e=(0,m.c)(),t=(0,s.A)(e,3),l=t[0],n=t[1],a=t[2],c=(0,u.OS)("2.2");return(0,r.createElement)(r.Fragment,null,!c&&(0,r.createElement)("div",{className:"btl-notes notice notice-warning",style:{marginLeft:0,marginBottom:"10px",padding:"10px",fontSize:"12px"}},(0,r.createElement)(T.A,{note:"To utilize the Full Site Link Scanner Feature, please update the BetterLinks Pro plugin to at least v2.2."})),(0,r.createElement)("div",{style:{position:"relative"}},(0,r.createElement)(o.A,{isOpenModal:l,closeModal:a}),(0,r.createElement)(z.M,{description:(0,i.__)("Scan your entire website’s Links in posts and pages to get real-time link status-active links, Broken Links, 403 forbidden and more","betterlinks")},(0,r.createElement)(k.A,null,[(0,r.createElement)("div",{className:"btl-graphteaser-icon"},(0,r.createElement)("span",null,(0,r.createElement)(x,null)),(0,r.createElement)("p",null,(0,i.__)("Conduct a thorough scan of your website's links","betterlinks"))),(0,r.createElement)("div",{className:"btl-graphteaser-icon"},(0,r.createElement)(_.h,null),(0,r.createElement)("p",null,(0,i.__)("Pinpoint active and broken links with precision","betterlinks"))),(0,r.createElement)("div",{className:"btl-graphteaser-icon"},(0,r.createElement)("span",null,"🚫"),(0,r.createElement)("p",null,(0,i.__)("Identify 403 forbidden and other error codes","betterlinks"))),(0,r.createElement)("div",{className:"btl-graphteaser-icon"},(0,r.createElement)("span",null,(0,r.createElement)(S,null)),(0,r.createElement)("p",null,(0,i.__)("Keep track of all website link status in real time","betterlinks"))),(0,r.createElement)("div",{className:"btl-graphteaser-icon"},(0,r.createElement)("span",null,(0,r.createElement)(C,null)),(0,r.createElement)("p",null,(0,i.__)("Update broken links & maintain website integrity","betterlinks")))].map((function(e){return(0,r.createElement)(v.A,{disableGutters:!0},(0,r.createElement)(h.A,{primary:e}))})))),(0,r.createElement)("div",{className:"btl-tab-panel-inner btl-broken-links-panel btl-broken-links-panel-disabled"},(0,r.createElement)("div",{className:"btl-broken-link-checker-wrapper btl-fullsite"},(0,r.createElement)("div",{className:"btl-broken-link-checker btl-broken-link-checker-wrapper-left",style:{width:"55%"}},(0,r.createElement)("div",null,(0,r.createElement)("h4",null,(0,i.__)("Choose Post Type and Scan URL","betterlinks")),(0,r.createElement)(W,null),(0,r.createElement)("div",{className:"btl-link-scan-btn-group"},(0,r.createElement)("button",{onClick:n,className:"btl-link-scan-btn btl-filled-button",style:{cursor:"not-allowed"},disabled:!0},(0,i.__)("Start New Scan","betterlinks")),(0,r.createElement)("button",{className:"btl-resume-link-scan-btn",disabled:!0},(0,i.__)("Resume Scan","betterlinks"))))),(0,r.createElement)("div",{className:"btl-broken-link-checker-wrapper-right"},(0,r.createElement)(D,{value:(0,i.__)("50%","betterlinks-pro")}),(0,r.createElement)("div",{className:"btl-flc-scan-details"},(0,r.createElement)(j,{color:"blue",title:(0,i.__)("Total Posts, Pages & Custom Posts:","betterlinks"),value:"10"}),(0,r.createElement)(j,{color:"green",title:(0,i.__)("Total Scaned Posts:","betterlinks"),value:"5"}),(0,r.createElement)(j,{color:"yellow",title:(0,i.__)("Total Scaned Links:","betterlinks"),value:"5"}),(0,r.createElement)("div",{className:"btl-flc-scanned-count"},(0,r.createElement)(F,{type:"active"}),(0,r.createElement)(F,{type:"broken"}),(0,r.createElement)(F,{type:"forbidden"})))))),(0,r.createElement)("div",{className:"btl-tab-panel-inner btl-broken-links-panel btl-broken-links-table-wrapper btl-broken-links-table-wrapper--fullsite"},(0,r.createElement)(p.Ay,{className:"btl-broken-links-table",title:(0,i.__)("Broken Link Reports","betterlinks"),columns:d,data:b,pagination:!0,subHeader:!0,highlightOnHover:!1,subHeaderComponent:(0,r.createElement)(H,null),persistTableHead:!0}))))};var H=function(){return(0,r.createElement)("form",{onSubmit:function(){},className:"btl-subheader-form"},(0,r.createElement)("div",{className:"btl-subheader-form-input"},(0,r.createElement)("input",{id:"search",type:"text",placeholder:(0,i.__)("Search...","betterlinks"),value:"",onChange:function(){}})),(0,r.createElement)("button",{className:"button",disabled:!0},(0,i.__)("Clear Logs","betterlinks-pro")))},j=function(e){var t=e.color,l=e.title,n=e.value,a=void 0===n?null:n;return(0,r.createElement)("div",null,(0,r.createElement)("p",null,(0,r.createElement)("span",{className:"btl-flc-round-pointer ".concat(t)})," ",l),(0,r.createElement)("span",null," ",a||(0,i.__)("N/A","betterlinks")," "))},F=function(e){var t=e.type,l=void 0===t?"active":t,n={active:{icon:u.hq+"assets/images/all-right.svg",title:(0,i.__)("Total Active Links","betterlinks"),count:"3"},broken:{icon:u.hq+"assets/images/broken-links.svg",title:(0,i.__)("Broken Links","betterlinks"),count:"1"},forbidden:{icon:u.hq+"assets/images/stop.svg",title:(0,i.__)("403 Forbidden","betterlinks"),count:"1"}};return(0,r.createElement)("div",{className:"btl-flc-scanned-count--".concat(l),title:"".concat(n[l].title," count: ").concat(n[l].count)},(0,r.createElement)("span",null,(0,r.createElement)("img",{src:n[l].icon,alt:"".concat(l,"-links-count")}),n[l].title),(0,r.createElement)("p",null,n[l].count))},D=function(e){var t=e.value,l=void 0===t?null:t;return(0,r.createElement)(E.A,{position:"relative",display:"inline-flex"},(0,r.createElement)(f.A,{variant:"determinate",className:"btl-flc-circle-progressbar",value:50,size:180}),(0,r.createElement)(f.A,{variant:"determinate",className:"flc-background-circle",value:100,size:180}),(0,r.createElement)(E.A,{top:0,left:0,bottom:0,right:0,position:"absolute",display:"flex",alignItems:"center",justifyContent:"center"},(0,r.createElement)(g.A,{variant:"caption",component:"div",color:"textSecondary"},l||(0,i.__)("No Data Found","betterlinks-pro"))))},W=function(){return(0,r.createElement)("div",{style:{padding:"0 0 20px 0"}},(0,r.createElement)("form",null,(0,r.createElement)("span",{className:"btl-form-group btl-general-tab-settings-react-select"},(0,r.createElement)("label",{className:"btl-form-label"},(0,i.__)("Post Types","betterlinks-pro"),(0,r.createElement)("div",{className:"btl-tooltip"},(0,r.createElement)("span",{className:"dashicons dashicons-info-outline",style:{display:"inline",fontSize:"16px"}}),(0,r.createElement)("span",{className:"btl-tooltiptext"},(0,i.__)("Select the post types on which you want to perform the Link Scan","betterlinks-pro")))),(0,r.createElement)("div",{className:"btl-form-field"},(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)(c.Ay,{isMulti:!0,name:"fullsite_scanner_post_types",className:"btl-modal-select--full",classNamePrefix:"btl-react-select",value:[{label:"post",value:""},{label:"page",value:""},{label:"product",value:""}],isDisabled:!0}))))))};const R=function(){return betterLinksHooks.applyFilters("betterLinksFullSiteLinkChecker",(0,r.createElement)(B,null))}},72057:(e,t,l)=>{l.d(t,{M:()=>b});var n,a,r=l(51609),s=l(20426),i=l(6272),c=l(54392),o=l(27723);function m(){return m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var l=arguments[t];for(var n in l)({}).hasOwnProperty.call(l,n)&&(e[n]=l[n])}return e},m.apply(null,arguments)}var p=function(e){return r.createElement("svg",m({xmlns:"http://www.w3.org/2000/svg",width:21,height:21,fill:"none"},e),n||(n=r.createElement("g",{clipPath:"url(#locked-2_svg__a)"},r.createElement("path",{fill:"#3C3A60",d:"M15.171 11.09a.807.807 0 0 1-.806-.806V5.979a3.87 3.87 0 0 0-3.866-3.866 3.87 3.87 0 0 0-3.867 3.866v4.305a.806.806 0 1 1-1.612 0V5.979A5.485 5.485 0 0 1 10.499.5a5.485 5.485 0 0 1 5.479 5.48v4.304a.807.807 0 0 1-.807.806"}),r.createElement("path",{fill:"#9FACBA",d:"M10.5.5q-.41 0-.805.06a5.49 5.49 0 0 1 4.675 5.42v4.304l-.002.046a.806.806 0 0 0 1.61-.046V5.979A5.485 5.485 0 0 0 10.5.5"}),r.createElement("path",{fill:"#FEB137",d:"M16.999 20.5H4a1.36 1.36 0 0 1-1.36-1.36V9.506c0-.75.61-1.359 1.36-1.359h12.999c.75 0 1.359.609 1.359 1.36v9.634c0 .75-.608 1.359-1.36 1.359"}),r.createElement("path",{fill:"#F6AB31",d:"M16.999 8.147H15.39c.75 0 1.359.609 1.359 1.36v9.634c0 .75-.609 1.359-1.36 1.359H17c.75 0 1.36-.609 1.36-1.36V9.506c0-.75-.61-1.359-1.36-1.359"}),r.createElement("path",{fill:"#272847",d:"M12.56 13.249a2.061 2.061 0 1 0-3.095 1.783v2.377a1.034 1.034 0 1 0 2.067 0v-2.377a2.06 2.06 0 0 0 1.028-1.783"}))),a||(a=r.createElement("defs",null,r.createElement("clipPath",{id:"locked-2_svg__a"},r.createElement("path",{fill:"#fff",d:"M.5.5h20v20H.5z"})))))},u=l(19735),b=function(e){var t=e.title,l=void 0===t?null:t,n=e.description,a=void 0===n?"":n,m=e.children;return u.JT?null:(0,r.createElement)("div",{className:"btl-analytics-chart-overlay",style:{zIndex:"10"}},(0,r.createElement)(s.A,{sx:{minWidth:275}},(0,r.createElement)(i.A,null,(0,r.createElement)(c.A,{variant:"h5",color:"text.secondary",gutterBottom:!0},l||(0,o.__)("Get BetterLinks PRO ","betterlinks"),(0,r.createElement)(p,null)),(0,r.createElement)(c.A,{variant:"p",color:"text.secondary",gutterBottom:!0},a),(0,r.createElement)("div",null,m),(0,r.createElement)("div",{className:"analytics-upgrade-btn"},(0,r.createElement)("a",{href:"https://wpdeveloper.com/in/upgrade-betterlinks",target:"_blank"},(0,r.createElement)("img",{src:u.hq+"/assets/images/crown.svg",alt:""}),(0,o.__)("Upgrade To BetterLinks PRO","betterlinks"))))))}}}]);