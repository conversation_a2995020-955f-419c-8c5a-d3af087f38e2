(globalThis.webpackChunkbetterlinks=globalThis.webpackChunkbetterlinks||[]).push([[99],{17790:(e,t,r)=>{var n=r(19852);function i(e){this.mode=n.MODE_8BIT_BYTE,this.data=e}i.prototype={getLength:function(e){return this.data.length},write:function(e){for(var t=0;t<this.data.length;t++)e.put(this.data.charCodeAt(t),8)}},e.exports=i},10046:e=>{function t(){this.buffer=new Array,this.length=0}t.prototype={get:function(e){var t=Math.floor(e/8);return 1==(this.buffer[t]>>>7-e%8&1)},put:function(e,t){for(var r=0;r<t;r++)this.putBit(1==(e>>>t-r-1&1))},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}},e.exports=t},41537:e=>{e.exports={L:1,M:0,Q:3,H:2}},30501:(e,t,r)=>{var n=r(39341);function i(e,t){if(null==e.length)throw new Error(e.length+"/"+t);for(var r=0;r<e.length&&0==e[r];)r++;this.num=new Array(e.length-r+t);for(var n=0;n<e.length-r;n++)this.num[n]=e[n+r]}i.prototype={get:function(e){return this.num[e]},getLength:function(){return this.num.length},multiply:function(e){for(var t=new Array(this.getLength()+e.getLength()-1),r=0;r<this.getLength();r++)for(var o=0;o<e.getLength();o++)t[r+o]^=n.gexp(n.glog(this.get(r))+n.glog(e.get(o)));return new i(t,0)},mod:function(e){if(this.getLength()-e.getLength()<0)return this;for(var t=n.glog(this.get(0))-n.glog(e.get(0)),r=new Array(this.getLength()),o=0;o<this.getLength();o++)r[o]=this.get(o);for(o=0;o<e.getLength();o++)r[o]^=n.gexp(n.glog(e.get(o))+t);return new i(r,0).mod(e)}},e.exports=i},46641:(e,t,r)=>{var n=r(17790),i=r(12835),o=r(10046),a=r(38759),l=r(30501);function u(e,t){this.typeNumber=e,this.errorCorrectLevel=t,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}var c=u.prototype;c.addData=function(e){var t=new n(e);this.dataList.push(t),this.dataCache=null},c.isDark=function(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(e+","+t);return this.modules[e][t]},c.getModuleCount=function(){return this.moduleCount},c.make=function(){if(this.typeNumber<1){var e=1;for(e=1;e<40;e++){for(var t=i.getRSBlocks(e,this.errorCorrectLevel),r=new o,n=0,l=0;l<t.length;l++)n+=t[l].dataCount;for(l=0;l<this.dataList.length;l++){var u=this.dataList[l];r.put(u.mode,4),r.put(u.getLength(),a.getLengthInBits(u.mode,e)),u.write(r)}if(r.getLengthInBits()<=8*n)break}this.typeNumber=e}this.makeImpl(!1,this.getBestMaskPattern())},c.makeImpl=function(e,t){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++){this.modules[r]=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++)this.modules[r][n]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=u.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)},c.setupPositionProbePattern=function(e,t){for(var r=-1;r<=7;r++)if(!(e+r<=-1||this.moduleCount<=e+r))for(var n=-1;n<=7;n++)t+n<=-1||this.moduleCount<=t+n||(this.modules[e+r][t+n]=0<=r&&r<=6&&(0==n||6==n)||0<=n&&n<=6&&(0==r||6==r)||2<=r&&r<=4&&2<=n&&n<=4)},c.getBestMaskPattern=function(){for(var e=0,t=0,r=0;r<8;r++){this.makeImpl(!0,r);var n=a.getLostPoint(this);(0==r||e>n)&&(e=n,t=r)}return t},c.createMovieClip=function(e,t,r){var n=e.createEmptyMovieClip(t,r);this.make();for(var i=0;i<this.modules.length;i++)for(var o=1*i,a=0;a<this.modules[i].length;a++){var l=1*a;this.modules[i][a]&&(n.beginFill(0,100),n.moveTo(l,o),n.lineTo(l+1,o),n.lineTo(l+1,o+1),n.lineTo(l,o+1),n.endFill())}return n},c.setupTimingPattern=function(){for(var e=8;e<this.moduleCount-8;e++)null==this.modules[e][6]&&(this.modules[e][6]=e%2==0);for(var t=8;t<this.moduleCount-8;t++)null==this.modules[6][t]&&(this.modules[6][t]=t%2==0)},c.setupPositionAdjustPattern=function(){for(var e=a.getPatternPosition(this.typeNumber),t=0;t<e.length;t++)for(var r=0;r<e.length;r++){var n=e[t],i=e[r];if(null==this.modules[n][i])for(var o=-2;o<=2;o++)for(var l=-2;l<=2;l++)this.modules[n+o][i+l]=-2==o||2==o||-2==l||2==l||0==o&&0==l}},c.setupTypeNumber=function(e){for(var t=a.getBCHTypeNumber(this.typeNumber),r=0;r<18;r++){var n=!e&&1==(t>>r&1);this.modules[Math.floor(r/3)][r%3+this.moduleCount-8-3]=n}for(r=0;r<18;r++)n=!e&&1==(t>>r&1),this.modules[r%3+this.moduleCount-8-3][Math.floor(r/3)]=n},c.setupTypeInfo=function(e,t){for(var r=this.errorCorrectLevel<<3|t,n=a.getBCHTypeInfo(r),i=0;i<15;i++){var o=!e&&1==(n>>i&1);i<6?this.modules[i][8]=o:i<8?this.modules[i+1][8]=o:this.modules[this.moduleCount-15+i][8]=o}for(i=0;i<15;i++)o=!e&&1==(n>>i&1),i<8?this.modules[8][this.moduleCount-i-1]=o:i<9?this.modules[8][15-i-1+1]=o:this.modules[8][15-i-1]=o;this.modules[this.moduleCount-8][8]=!e},c.mapData=function(e,t){for(var r=-1,n=this.moduleCount-1,i=7,o=0,l=this.moduleCount-1;l>0;l-=2)for(6==l&&l--;;){for(var u=0;u<2;u++)if(null==this.modules[n][l-u]){var c=!1;o<e.length&&(c=1==(e[o]>>>i&1)),a.getMask(t,n,l-u)&&(c=!c),this.modules[n][l-u]=c,-1==--i&&(o++,i=7)}if((n+=r)<0||this.moduleCount<=n){n-=r,r=-r;break}}},u.PAD0=236,u.PAD1=17,u.createData=function(e,t,r){for(var n=i.getRSBlocks(e,t),l=new o,c=0;c<r.length;c++){var s=r[c];l.put(s.mode,4),l.put(s.getLength(),a.getLengthInBits(s.mode,e)),s.write(l)}var d=0;for(c=0;c<n.length;c++)d+=n[c].dataCount;if(l.getLengthInBits()>8*d)throw new Error("code length overflow. ("+l.getLengthInBits()+">"+8*d+")");for(l.getLengthInBits()+4<=8*d&&l.put(0,4);l.getLengthInBits()%8!=0;)l.putBit(!1);for(;!(l.getLengthInBits()>=8*d||(l.put(u.PAD0,8),l.getLengthInBits()>=8*d));)l.put(u.PAD1,8);return u.createBytes(l,n)},u.createBytes=function(e,t){for(var r=0,n=0,i=0,o=new Array(t.length),u=new Array(t.length),c=0;c<t.length;c++){var s=t[c].dataCount,d=t[c].totalCount-s;n=Math.max(n,s),i=Math.max(i,d),o[c]=new Array(s);for(var p=0;p<o[c].length;p++)o[c][p]=255&e.buffer[p+r];r+=s;var f=a.getErrorCorrectPolynomial(d),g=new l(o[c],f.getLength()-1).mod(f);for(u[c]=new Array(f.getLength()-1),p=0;p<u[c].length;p++){var v=p+g.getLength()-u[c].length;u[c][p]=v>=0?g.get(v):0}}var m=0;for(p=0;p<t.length;p++)m+=t[p].totalCount;var h=new Array(m),b=0;for(p=0;p<n;p++)for(c=0;c<t.length;c++)p<o[c].length&&(h[b++]=o[c][p]);for(p=0;p<i;p++)for(c=0;c<t.length;c++)p<u[c].length&&(h[b++]=u[c][p]);return h},e.exports=u},12835:(e,t,r)=>{var n=r(41537);function i(e,t){this.totalCount=e,this.dataCount=t}i.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],i.getRSBlocks=function(e,t){var r=i.getRsBlockTable(e,t);if(null==r)throw new Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);for(var n=r.length/3,o=new Array,a=0;a<n;a++)for(var l=r[3*a+0],u=r[3*a+1],c=r[3*a+2],s=0;s<l;s++)o.push(new i(u,c));return o},i.getRsBlockTable=function(e,t){switch(t){case n.L:return i.RS_BLOCK_TABLE[4*(e-1)+0];case n.M:return i.RS_BLOCK_TABLE[4*(e-1)+1];case n.Q:return i.RS_BLOCK_TABLE[4*(e-1)+2];case n.H:return i.RS_BLOCK_TABLE[4*(e-1)+3];default:return}},e.exports=i},39341:e=>{for(var t={glog:function(e){if(e<1)throw new Error("glog("+e+")");return t.LOG_TABLE[e]},gexp:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return t.EXP_TABLE[e]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},r=0;r<8;r++)t.EXP_TABLE[r]=1<<r;for(r=8;r<256;r++)t.EXP_TABLE[r]=t.EXP_TABLE[r-4]^t.EXP_TABLE[r-5]^t.EXP_TABLE[r-6]^t.EXP_TABLE[r-8];for(r=0;r<255;r++)t.LOG_TABLE[t.EXP_TABLE[r]]=r;e.exports=t},19852:e=>{e.exports={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8}},38759:(e,t,r)=>{var n=r(19852),i=r(30501),o=r(39341),a={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(e){for(var t=e<<10;a.getBCHDigit(t)-a.getBCHDigit(a.G15)>=0;)t^=a.G15<<a.getBCHDigit(t)-a.getBCHDigit(a.G15);return(e<<10|t)^a.G15_MASK},getBCHTypeNumber:function(e){for(var t=e<<12;a.getBCHDigit(t)-a.getBCHDigit(a.G18)>=0;)t^=a.G18<<a.getBCHDigit(t)-a.getBCHDigit(a.G18);return e<<12|t},getBCHDigit:function(e){for(var t=0;0!=e;)t++,e>>>=1;return t},getPatternPosition:function(e){return a.PATTERN_POSITION_TABLE[e-1]},getMask:function(e,t,r){switch(e){case 0:return(t+r)%2==0;case 1:return t%2==0;case 2:return r%3==0;case 3:return(t+r)%3==0;case 4:return(Math.floor(t/2)+Math.floor(r/3))%2==0;case 5:return t*r%2+t*r%3==0;case 6:return(t*r%2+t*r%3)%2==0;case 7:return(t*r%3+(t+r)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial:function(e){for(var t=new i([1],0),r=0;r<e;r++)t=t.multiply(new i([1,o.gexp(r)],0));return t},getLengthInBits:function(e,t){if(1<=t&&t<10)switch(e){case n.MODE_NUMBER:return 10;case n.MODE_ALPHA_NUM:return 9;case n.MODE_8BIT_BYTE:case n.MODE_KANJI:return 8;default:throw new Error("mode:"+e)}else if(t<27)switch(e){case n.MODE_NUMBER:return 12;case n.MODE_ALPHA_NUM:return 11;case n.MODE_8BIT_BYTE:return 16;case n.MODE_KANJI:return 10;default:throw new Error("mode:"+e)}else{if(!(t<41))throw new Error("type:"+t);switch(e){case n.MODE_NUMBER:return 14;case n.MODE_ALPHA_NUM:return 13;case n.MODE_8BIT_BYTE:return 16;case n.MODE_KANJI:return 12;default:throw new Error("mode:"+e)}}},getLostPoint:function(e){for(var t=e.getModuleCount(),r=0,n=0;n<t;n++)for(var i=0;i<t;i++){for(var o=0,a=e.isDark(n,i),l=-1;l<=1;l++)if(!(n+l<0||t<=n+l))for(var u=-1;u<=1;u++)i+u<0||t<=i+u||0==l&&0==u||a==e.isDark(n+l,i+u)&&o++;o>5&&(r+=3+o-5)}for(n=0;n<t-1;n++)for(i=0;i<t-1;i++){var c=0;e.isDark(n,i)&&c++,e.isDark(n+1,i)&&c++,e.isDark(n,i+1)&&c++,e.isDark(n+1,i+1)&&c++,0!=c&&4!=c||(r+=3)}for(n=0;n<t;n++)for(i=0;i<t-6;i++)e.isDark(n,i)&&!e.isDark(n,i+1)&&e.isDark(n,i+2)&&e.isDark(n,i+3)&&e.isDark(n,i+4)&&!e.isDark(n,i+5)&&e.isDark(n,i+6)&&(r+=40);for(i=0;i<t;i++)for(n=0;n<t-6;n++)e.isDark(n,i)&&!e.isDark(n+1,i)&&e.isDark(n+2,i)&&e.isDark(n+3,i)&&e.isDark(n+4,i)&&!e.isDark(n+5,i)&&e.isDark(n+6,i)&&(r+=40);var s=0;for(i=0;i<t;i++)for(n=0;n<t;n++)e.isDark(n,i)&&s++;return r+Math.abs(100*s/t/t-50)/5*10}};e.exports=a},15286:(e,t,r)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(){return i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},i.apply(this,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(r,!0).forEach((function(t){m(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function l(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,r){return t&&c(e.prototype,t),r&&c(e,r),e}function d(e,t){return!t||"object"!==n(t)&&"function"!=typeof t?f(e):t}function p(e){return p=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},p(e)}function f(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function g(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&v(e,t)}function v(e,t){return v=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},v(e,t)}function m(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var h=r(51609),b=(r(5556),r(46641)),y=r(41537);function D(e){for(var t="",r=0;r<e.length;r++){var n=e.charCodeAt(r);n<128?t+=String.fromCharCode(n):n<2048?(t+=String.fromCharCode(192|n>>6),t+=String.fromCharCode(128|63&n)):n<55296||n>=57344?(t+=String.fromCharCode(224|n>>12),t+=String.fromCharCode(128|n>>6&63),t+=String.fromCharCode(128|63&n)):(r++,n=65536+((1023&n)<<10|1023&e.charCodeAt(r)),t+=String.fromCharCode(240|n>>18),t+=String.fromCharCode(128|n>>12&63),t+=String.fromCharCode(128|n>>6&63),t+=String.fromCharCode(128|63&n))}return t}var I={size:128,level:"L",bgColor:"#FFFFFF",fgColor:"#000000",includeMargin:!1};function x(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=[];return e.forEach((function(e,n){var i=null;e.forEach((function(o,a){if(!o&&null!==i)return r.push("M".concat(i+t," ").concat(n+t,"h").concat(a-i,"v1H").concat(i+t,"z")),void(i=null);if(a!==e.length-1)o&&null===i&&(i=a);else{if(!o)return;null===i?r.push("M".concat(a+t,",").concat(n+t," h1v1H").concat(a+t,"z")):r.push("M".concat(i+t,",").concat(n+t," h").concat(a+1-i,"v1H").concat(i+t,"z"))}}))})),r.join("")}function A(e,t){return e.slice().map((function(e,r){return r<t.y||r>=t.y+t.h?e:e.map((function(e,r){return(r<t.x||r>=t.x+t.w)&&e}))}))}function C(e,t){var r=e.imageSettings,n=e.size,i=e.includeMargin;if(null==r)return null;var o=i?4:0,a=t.length+2*o,l=Math.floor(.1*n),u=a/n,c=(r.width||l)*u,s=(r.height||l)*u,d=null==r.x?t.length/2-c/2:r.x*u,p=null==r.y?t.length/2-s/2:r.y*u,f=null;if(r.excavate){var g=Math.floor(d),v=Math.floor(p);f={x:g,y:v,w:Math.ceil(c+d-g),h:Math.ceil(s+p-v)}}return{x:d,y:p,h:s,w:c,excavation:f}}var w=function(){try{(new Path2D).addPath(new Path2D)}catch(e){return!1}return!0}(),E=function(e){function t(){var e,r;u(this,t);for(var n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return m(f(r=d(this,(e=p(t)).call.apply(e,[this].concat(i)))),"_canvas",void 0),m(f(r),"_image",void 0),m(f(r),"state",{imgLoaded:!1}),m(f(r),"handleImageLoad",(function(){r.setState({imgLoaded:!0})})),r}return g(t,e),s(t,[{key:"componentDidMount",value:function(){this._image&&this._image.complete&&this.handleImageLoad(),this.update()}},{key:"componentWillReceiveProps",value:function(e){var t,r;(null===(t=this.props.imageSettings)||void 0===t?void 0:t.src)!==(null===(r=e.imageSettings)||void 0===r?void 0:r.src)&&this.setState({imgLoaded:!1})}},{key:"componentDidUpdate",value:function(){this.update()}},{key:"update",value:function(){var e=this.props,t=e.value,r=e.size,n=e.level,i=e.bgColor,o=e.fgColor,a=e.includeMargin,l=e.imageSettings,u=new b(-1,y[n]);if(u.addData(D(t)),u.make(),null!=this._canvas){var c=this._canvas,s=c.getContext("2d");if(!s)return;var d=u.modules;if(null===d)return;var p=a?4:0,f=d.length+2*p,g=C(this.props,d);null!=l&&null!=g&&null!=g.excavation&&(d=A(d,g.excavation));var v=window.devicePixelRatio||1;c.height=c.width=r*v;var m=r/f*v;s.scale(m,m),s.fillStyle=i,s.fillRect(0,0,f,f),s.fillStyle=o,w?s.fill(new Path2D(x(d,p))):d.forEach((function(e,t){e.forEach((function(e,r){e&&s.fillRect(r+p,t+p,1,1)}))})),this.state.imgLoaded&&this._image&&null!=g&&s.drawImage(this._image,g.x+p,g.y+p,g.w,g.h)}}},{key:"render",value:function(){var e=this,t=this.props,r=(t.value,t.size),n=(t.level,t.bgColor,t.fgColor,t.style),o=(t.includeMargin,t.imageSettings),u=l(t,["value","size","level","bgColor","fgColor","style","includeMargin","imageSettings"]),c=a({height:r,width:r},n),s=null,d=o&&o.src;return null!=o&&null!=d&&(s=h.createElement("img",{src:d,style:{display:"none"},onLoad:this.handleImageLoad,ref:function(t){return e._image=t}})),h.createElement(h.Fragment,null,h.createElement("canvas",i({style:c,height:r,width:r,ref:function(t){return e._canvas=t}},u)),s)}}]),t}(h.PureComponent);m(E,"defaultProps",I);var B=function(e){function t(){return u(this,t),d(this,p(t).apply(this,arguments))}return g(t,e),s(t,[{key:"render",value:function(){var e=this.props,t=e.value,r=e.size,n=e.level,o=e.bgColor,a=e.fgColor,u=e.includeMargin,c=e.imageSettings,s=l(e,["value","size","level","bgColor","fgColor","includeMargin","imageSettings"]),d=new b(-1,y[n]);d.addData(D(t)),d.make();var p=d.modules;if(null===p)return null;var f=u?4:0,g=p.length+2*f,v=C(this.props,p),m=null;null!=c&&null!=v&&(null!=v.excavation&&(p=A(p,v.excavation)),m=h.createElement("image",{xlinkHref:c.src,height:v.h,width:v.w,x:v.x+f,y:v.y+f,preserveAspectRatio:"none"}));var I=x(p,f);return h.createElement("svg",i({shapeRendering:"crispEdges",height:r,width:r,viewBox:"0 0 ".concat(g," ").concat(g)},s),h.createElement("path",{fill:o,d:"M0,0 h".concat(g,"v").concat(g,"H0z")}),h.createElement("path",{fill:a,d:I}),m)}}]),t}(h.PureComponent);m(B,"defaultProps",I);var P=function(e){var t=e.renderAs,r=l(e,["renderAs"]),n="svg"===t?B:E;return h.createElement(n,r)};P.defaultProps=a({renderAs:"canvas"},I),e.exports=P},43774:(e,t,r)=>{"use strict";r.d(t,{JY:()=>sn,sx:()=>Hn,gL:()=>Vn});var n=r(51609),i=r.n(n),o=r(77387),a=r(58168),l=r(68238),u=r(49924);function c(e,t){var r=(0,n.useState)((function(){return{inputs:t,result:e()}}))[0],i=(0,n.useRef)(!0),o=(0,n.useRef)(r),a=i.current||Boolean(t&&o.current.inputs&&function(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(t,o.current.inputs))?o.current:{inputs:t,result:e()};return(0,n.useEffect)((function(){i.current=!1,o.current=a}),[a]),a.result}var s=c,d=function(e,t){return c((function(){return e}),t)},p=r(11561),f=function(e){var t=e.top,r=e.right,n=e.bottom,i=e.left;return{top:t,right:r,bottom:n,left:i,width:r-i,height:n-t,x:i,y:t,center:{x:(r+i)/2,y:(n+t)/2}}},g=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},v=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},m={top:0,right:0,bottom:0,left:0},h=function(e){var t=e.borderBox,r=e.margin,n=void 0===r?m:r,i=e.border,o=void 0===i?m:i,a=e.padding,l=void 0===a?m:a,u=f(g(t,n)),c=f(v(t,o)),s=f(v(c,l));return{marginBox:u,borderBox:f(t),paddingBox:c,contentBox:s,margin:n,border:o,padding:l}},b=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var r=Number(t);return isNaN(r)&&(0,p.A)(!1),r},y=function(e,t){var r,n,i=e.borderBox,o=e.border,a=e.margin,l=e.padding,u=(n=t,{top:(r=i).top+n.y,left:r.left+n.x,bottom:r.bottom+n.y,right:r.right+n.x});return h({borderBox:u,border:o,margin:a,padding:l})},D=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),y(e,t)},I=function(e,t){var r={top:b(t.marginTop),right:b(t.marginRight),bottom:b(t.marginBottom),left:b(t.marginLeft)},n={top:b(t.paddingTop),right:b(t.paddingRight),bottom:b(t.paddingBottom),left:b(t.paddingLeft)},i={top:b(t.borderTopWidth),right:b(t.borderRightWidth),bottom:b(t.borderBottomWidth),left:b(t.borderLeftWidth)};return h({borderBox:e,margin:r,padding:n,border:i})},x=function(e){var t=e.getBoundingClientRect(),r=window.getComputedStyle(e);return I(t,r)},A=r(41811);const C=function(e){var t=[],r=null,n=function(){for(var n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];t=i,r||(r=requestAnimationFrame((function(){r=null,e.apply(void 0,t)})))};return n.cancel=function(){r&&(cancelAnimationFrame(r),r=null)},n};var w=r(75795),E=r.n(w);function B(e,t){}function P(){}function O(e,t,r){var n=t.map((function(t){var n,i,o=(n=r,i=t.options,(0,a.A)({},n,{},i));return e.addEventListener(t.eventName,t.fn,o),function(){e.removeEventListener(t.eventName,t.fn,o)}}));return function(){n.forEach((function(e){e()}))}}B.bind(null,"warn"),B.bind(null,"error");var S=!0,N="Invariant failed";function L(e){this.message=e}function R(e,t){if(!e)throw new L(S?N:N+": "+(t||""))}L.prototype.toString=function(){return this.message};var T=function(e){function t(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return(t=e.call.apply(e,[this].concat(n))||this).callbacks=null,t.unbind=P,t.onWindowError=function(e){var r=t.getCallbacks();r.isDragging()&&r.tryAbort(),e.error instanceof L&&e.preventDefault()},t.getCallbacks=function(){if(!t.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return t.callbacks},t.setCallbacks=function(e){t.callbacks=e},t}(0,o.A)(t,e);var r=t.prototype;return r.componentDidMount=function(){this.unbind=O(window,[{eventName:"error",fn:this.onWindowError}])},r.componentDidCatch=function(e){if(!(e instanceof L))throw e;this.setState({})},r.componentWillUnmount=function(){this.unbind()},r.render=function(){return this.props.children(this.setCallbacks)},t}(i().Component),M=function(e){return e+1},_=function(e,t){var r=e.droppableId===t.droppableId,n=M(e.index),i=M(t.index);return r?"\n      You have moved the item from position "+n+"\n      to position "+i+"\n    ":"\n    You have moved the item from position "+n+"\n    in list "+e.droppableId+"\n    to list "+t.droppableId+"\n    in position "+i+"\n  "},G=function(e,t,r){return t.droppableId===r.droppableId?"\n      The item "+e+"\n      has been combined with "+r.draggableId:"\n      The item "+e+"\n      in list "+t.droppableId+"\n      has been combined with "+r.draggableId+"\n      in list "+r.droppableId+"\n    "},k=function(e){return"\n  The item has returned to its starting position\n  of "+M(e.index)+"\n"},F={dragHandleUsageInstructions:"\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n",onDragStart:function(e){return"\n  You have lifted an item in position "+M(e.source.index)+"\n"},onDragUpdate:function(e){var t=e.destination;if(t)return _(e.source,t);var r=e.combine;return r?G(e.draggableId,e.source,r):"You are over an area that cannot be dropped on"},onDragEnd:function(e){if("CANCEL"===e.reason)return"\n      Movement cancelled.\n      "+k(e.source)+"\n    ";var t=e.destination,r=e.combine;return t?"\n      You have dropped the item.\n      "+_(e.source,t)+"\n    ":r?"\n      You have dropped the item.\n      "+G(e.draggableId,e.source,r)+"\n    ":"\n    The item has been dropped while not over a drop area.\n    "+k(e.source)+"\n  "}},H={x:0,y:0},U=function(e,t){return{x:e.x+t.x,y:e.y+t.y}},W=function(e,t){return{x:e.x-t.x,y:e.y-t.y}},j=function(e,t){return e.x===t.x&&e.y===t.y},V=function(e){return{x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}},z=function(e,t,r){var n;return void 0===r&&(r=0),(n={})[e]=t,n["x"===e?"y":"x"]=r,n},q=function(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))},Y=function(e,t){return Math.min.apply(Math,t.map((function(t){return q(e,t)})))},X=function(e){return function(t){return{x:e(t.x),y:e(t.y)}}},J=function(e,t){return{top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}},K=function(e){return[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}]},Q=function(e,t){return t&&t.shouldClipSubject?function(e,t){var r=f({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return r.width<=0||r.height<=0?null:r}(t.pageMarginBox,e):f(e)},Z=function(e){var t=e.page,r=e.withPlaceholder,n=e.axis,i=e.frame,o=function(e,t){return t?J(e,t.scroll.diff.displacement):e}(t.marginBox,i),l=function(e,t,r){var n;return r&&r.increasedBy?(0,a.A)({},e,((n={})[t.end]=e[t.end]+r.increasedBy[t.line],n)):e}(o,n,r);return{page:t,withPlaceholder:r,active:Q(l,i)}},$=function(e,t){e.frame||R(!1);var r=e.frame,n=W(t,r.scroll.initial),i=V(n),o=(0,a.A)({},r,{scroll:{initial:r.scroll.initial,current:t,diff:{value:n,displacement:i},max:r.scroll.max}}),l=Z({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:o});return(0,a.A)({},e,{frame:o,subject:l})};function ee(e){return Object.values?Object.values(e):Object.keys(e).map((function(t){return e[t]}))}function te(e,t){if(e.findIndex)return e.findIndex(t);for(var r=0;r<e.length;r++)if(t(e[r]))return r;return-1}function re(e,t){if(e.find)return e.find(t);var r=te(e,t);return-1!==r?e[r]:void 0}function ne(e){return Array.prototype.slice.call(e)}var ie=(0,A.A)((function(e){return e.reduce((function(e,t){return e[t.descriptor.id]=t,e}),{})})),oe=(0,A.A)((function(e){return e.reduce((function(e,t){return e[t.descriptor.id]=t,e}),{})})),ae=(0,A.A)((function(e){return ee(e)})),le=(0,A.A)((function(e){return ee(e)})),ue=(0,A.A)((function(e,t){var r=le(t).filter((function(t){return e===t.descriptor.droppableId})).sort((function(e,t){return e.descriptor.index-t.descriptor.index}));return r}));function ce(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function se(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var de=(0,A.A)((function(e,t){return t.filter((function(t){return t.descriptor.id!==e.descriptor.id}))})),pe=function(e,t){return e.descriptor.droppableId===t.descriptor.id},fe={point:H,value:0},ge={invisible:{},visible:{},all:[]},ve={displaced:ge,displacedBy:fe,at:null},me=function(e,t){return function(r){return e<=r&&r<=t}},he=function(e){var t=me(e.top,e.bottom),r=me(e.left,e.right);return function(n){if(t(n.top)&&t(n.bottom)&&r(n.left)&&r(n.right))return!0;var i=t(n.top)||t(n.bottom),o=r(n.left)||r(n.right);if(i&&o)return!0;var a=n.top<e.top&&n.bottom>e.bottom,l=n.left<e.left&&n.right>e.right;return!(!a||!l)||a&&o||l&&i}},be=function(e){var t=me(e.top,e.bottom),r=me(e.left,e.right);return function(e){return t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)}},ye={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},De={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},Ie=function(e){var t=e.target,r=e.destination,n=e.viewport,i=e.withDroppableDisplacement,o=e.isVisibleThroughFrameFn,a=i?function(e,t){var r=t.frame?t.frame.scroll.diff.displacement:H;return J(e,r)}(t,r):t;return function(e,t,r){return!!t.subject.active&&r(t.subject.active)(e)}(a,r,o)&&function(e,t,r){return r(t)(e)}(a,n,o)},xe=function(e){return Ie((0,a.A)({},e,{isVisibleThroughFrameFn:be}))};function Ae(e){var t=e.afterDragging,r=e.destination,n=e.displacedBy,i=e.viewport,o=e.forceShouldAnimate,l=e.last;return t.reduce((function(e,t){var u,c=function(e,t){var r=e.page.marginBox,n={top:t.point.y,right:0,bottom:0,left:t.point.x};return f(g(r,n))}(t,n),s=t.descriptor.id;if(e.all.push(s),u={target:c,destination:r,viewport:i,withDroppableDisplacement:!0},!Ie((0,a.A)({},u,{isVisibleThroughFrameFn:he})))return e.invisible[t.descriptor.id]=!0,e;var d=function(e,t,r){if("boolean"==typeof r)return r;if(!t)return!0;var n=t.invisible,i=t.visible;if(n[e])return!1;var o=i[e];return!o||o.shouldAnimate}(s,l,o),p={draggableId:s,shouldAnimate:d};return e.visible[s]=p,e}),{all:[],visible:{},invisible:{}})}function Ce(e){var t=e.insideDestination,r=e.inHomeList,n=e.displacedBy,i=e.destination,o=function(e,t){if(!e.length)return 0;var r=e[e.length-1].descriptor.index;return t.inHomeList?r:r+1}(t,{inHomeList:r});return{displaced:ge,displacedBy:n,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:o}}}}function we(e){var t=e.draggable,r=e.insideDestination,n=e.destination,i=e.viewport,o=e.displacedBy,a=e.last,l=e.index,u=e.forceShouldAnimate,c=pe(t,n);if(null==l)return Ce({insideDestination:r,inHomeList:c,displacedBy:o,destination:n});var s=re(r,(function(e){return e.descriptor.index===l}));if(!s)return Ce({insideDestination:r,inHomeList:c,displacedBy:o,destination:n});var d=de(t,r),p=r.indexOf(s);return{displaced:Ae({afterDragging:d.slice(p),destination:n,displacedBy:o,last:a,viewport:i.frame,forceShouldAnimate:u}),displacedBy:o,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:l}}}}function Ee(e,t){return Boolean(t.effected[e])}var Be=function(e,t){return t.margin[e.start]+t.borderBox[e.size]/2},Pe=function(e,t,r){return t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2},Oe=function(e){var t=e.axis,r=e.moveRelativeTo,n=e.isMoving;return z(t.line,r.marginBox[t.end]+Be(t,n),Pe(t,r.marginBox,n))},Se=function(e){var t=e.axis,r=e.moveRelativeTo,n=e.isMoving;return z(t.line,r.marginBox[t.start]-function(e,t){return t.margin[e.end]+t.borderBox[e.size]/2}(t,n),Pe(t,r.marginBox,n))},Ne=function(e,t){var r=e.frame;return r?U(t,r.scroll.diff.displacement):t},Le=function(e){var t=function(e){var t=e.impact,r=e.draggable,n=e.droppable,i=e.draggables,o=e.afterCritical,a=r.page.borderBox.center,l=t.at;return n&&l?"REORDER"===l.type?function(e){var t=e.impact,r=e.draggable,n=e.draggables,i=e.droppable,o=e.afterCritical,a=ue(i.descriptor.id,n),l=r.page,u=i.axis;if(!a.length)return function(e){var t=e.axis,r=e.moveInto,n=e.isMoving;return z(t.line,r.contentBox[t.start]+Be(t,n),Pe(t,r.contentBox,n))}({axis:u,moveInto:i.page,isMoving:l});var c=t.displaced,s=t.displacedBy,d=c.all[0];if(d){var p=n[d];if(Ee(d,o))return Se({axis:u,moveRelativeTo:p.page,isMoving:l});var f=y(p.page,s.point);return Se({axis:u,moveRelativeTo:f,isMoving:l})}var g=a[a.length-1];if(g.descriptor.id===r.descriptor.id)return l.borderBox.center;if(Ee(g.descriptor.id,o)){var v=y(g.page,V(o.displacedBy.point));return Oe({axis:u,moveRelativeTo:v,isMoving:l})}return Oe({axis:u,moveRelativeTo:g.page,isMoving:l})}({impact:t,draggable:r,draggables:i,droppable:n,afterCritical:o}):function(e){var t=e.afterCritical,r=e.impact,n=e.draggables,i=se(r);i||R(!1);var o=i.draggableId,a=n[o].page.borderBox.center,l=function(e){var t=e.displaced,r=e.afterCritical,n=e.combineWith,i=e.displacedBy,o=Boolean(t.visible[n]||t.invisible[n]);return Ee(n,r)?o?H:V(i.point):o?i.point:H}({displaced:r.displaced,afterCritical:t,combineWith:o,displacedBy:r.displacedBy});return U(a,l)}({impact:t,draggables:i,afterCritical:o}):a}(e),r=e.droppable;return r?Ne(r,t):t},Re=function(e,t){var r=W(t,e.scroll.initial),n=V(r);return{frame:f({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:n}}}};function Te(e,t){return e.map((function(e){return t[e]}))}var Me=function(e){var t,r,n=e.pageBorderBoxCenter,i=e.draggable,o=(t=e.viewport,r=n,U(t.scroll.diff.displacement,r)),a=W(o,i.page.borderBox.center);return U(i.client.borderBox.center,a)},_e=function(e){var t=e.draggable,r=e.destination,n=e.newPageBorderBoxCenter,i=e.viewport,o=e.withDroppableDisplacement,l=e.onlyOnMainAxis,u=void 0!==l&&l,c=W(n,t.page.borderBox.center),s={target:J(t.page.borderBox,c),destination:r,withDroppableDisplacement:o,viewport:i};return u?function(e){return Ie((0,a.A)({},e,{isVisibleThroughFrameFn:(t=e.destination.axis,function(e){var r=me(e.top,e.bottom),n=me(e.left,e.right);return function(e){return t===ye?r(e.top)&&r(e.bottom):n(e.left)&&n(e.right)}})}));var t}(s):xe(s)},Ge=function(e){var t=e.isMovingForward,r=e.draggable,n=e.destination,i=e.draggables,o=e.previousImpact,l=e.viewport,u=e.previousPageBorderBoxCenter,c=e.previousClientSelection,s=e.afterCritical;if(!n.isEnabled)return null;var d=ue(n.descriptor.id,i),p=pe(r,n),f=function(e){var t=e.isMovingForward,r=e.draggable,n=e.destination,i=e.insideDestination,o=e.previousImpact;if(!n.isCombineEnabled)return null;if(!ce(o))return null;function l(e){var t={type:"COMBINE",combine:{draggableId:e,droppableId:n.descriptor.id}};return(0,a.A)({},o,{at:t})}var u=o.displaced.all,c=u.length?u[0]:null;if(t)return c?l(c):null;var s=de(r,i);if(!c)return s.length?l(s[s.length-1].descriptor.id):null;var d=te(s,(function(e){return e.descriptor.id===c}));-1===d&&R(!1);var p=d-1;return p<0?null:l(s[p].descriptor.id)}({isMovingForward:t,draggable:r,destination:n,insideDestination:d,previousImpact:o})||function(e){var t=e.isMovingForward,r=e.isInHomeList,n=e.draggable,i=e.draggables,o=e.destination,a=e.insideDestination,l=e.previousImpact,u=e.viewport,c=e.afterCritical,s=l.at;if(s||R(!1),"REORDER"===s.type){var d=function(e){var t=e.isMovingForward,r=e.isInHomeList,n=e.insideDestination,i=e.location;if(!n.length)return null;var o=i.index,a=t?o+1:o-1,l=n[0].descriptor.index,u=n[n.length-1].descriptor.index;return a<l||a>(r?u:u+1)?null:a}({isMovingForward:t,isInHomeList:r,location:s.destination,insideDestination:a});return null==d?null:we({draggable:n,insideDestination:a,destination:o,viewport:u,last:l.displaced,displacedBy:l.displacedBy,index:d})}var p=function(e){var t=e.isMovingForward,r=e.draggables,n=e.combine,i=e.afterCritical;if(!e.destination.isCombineEnabled)return null;var o=n.draggableId,a=r[o].descriptor.index;return Ee(o,i)?t?a:a-1:t?a+1:a}({isMovingForward:t,destination:o,displaced:l.displaced,draggables:i,combine:s.combine,afterCritical:c});return null==p?null:we({draggable:n,insideDestination:a,destination:o,viewport:u,last:l.displaced,displacedBy:l.displacedBy,index:p})}({isMovingForward:t,isInHomeList:p,draggable:r,draggables:i,destination:n,insideDestination:d,previousImpact:o,viewport:l,afterCritical:s});if(!f)return null;var g=Le({impact:f,draggable:r,droppable:n,draggables:i,afterCritical:s});if(_e({draggable:r,destination:n,newPageBorderBoxCenter:g,viewport:l.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:Me({pageBorderBoxCenter:g,draggable:r,viewport:l}),impact:f,scrollJumpRequest:null};var v=W(g,u),m=function(e){var t=e.impact,r=e.viewport,n=e.destination,i=e.draggables,o=e.maxScrollChange,l=Re(r,U(r.scroll.current,o)),u=n.frame?$(n,U(n.frame.scroll.current,o)):n,c=t.displaced,s=Ae({afterDragging:Te(c.all,i),destination:n,displacedBy:t.displacedBy,viewport:l.frame,last:c,forceShouldAnimate:!1}),d=Ae({afterDragging:Te(c.all,i),destination:u,displacedBy:t.displacedBy,viewport:r.frame,last:c,forceShouldAnimate:!1}),p={},f={},g=[c,s,d];return c.all.forEach((function(e){var t=function(e,t){for(var r=0;r<t.length;r++){var n=t[r].visible[e];if(n)return n}return null}(e,g);t?f[e]=t:p[e]=!0})),(0,a.A)({},t,{displaced:{all:c.all,invisible:p,visible:f}})}({impact:f,viewport:l,destination:n,draggables:i,maxScrollChange:v});return{clientSelection:c,impact:m,scrollJumpRequest:v}},ke=function(e){var t=e.subject.active;return t||R(!1),t},Fe=function(e,t){var r=e.page.borderBox.center;return Ee(e.descriptor.id,t)?W(r,t.displacedBy.point):r},He=function(e,t){var r=e.page.borderBox;return Ee(e.descriptor.id,t)?J(r,V(t.displacedBy.point)):r},Ue=(0,A.A)((function(e,t){var r=t[e.line];return{value:r,point:z(e.line,r)}})),We=function(e,t){return(0,a.A)({},e,{scroll:(0,a.A)({},e.scroll,{max:t})})},je=function(e,t,r){var n=e.frame;pe(t,e)&&R(!1),e.subject.withPlaceholder&&R(!1);var i=Ue(e.axis,t.displaceBy).point,o=function(e,t,r){var n=e.axis;if("virtual"===e.descriptor.mode)return z(n.line,t[n.line]);var i=e.subject.page.contentBox[n.size],o=ue(e.descriptor.id,r).reduce((function(e,t){return e+t.client.marginBox[n.size]}),0)+t[n.line]-i;return o<=0?null:z(n.line,o)}(e,i,r),l={placeholderSize:i,increasedBy:o,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!n){var u=Z({page:e.subject.page,withPlaceholder:l,axis:e.axis,frame:e.frame});return(0,a.A)({},e,{subject:u})}var c=o?U(n.scroll.max,o):n.scroll.max,s=We(n,c),d=Z({page:e.subject.page,withPlaceholder:l,axis:e.axis,frame:s});return(0,a.A)({},e,{subject:d,frame:s})},Ve=function(e){var t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null},ze=function(e){var t=e.state,r=e.type,n=function(e,t){var r=Ve(e);return r?t[r]:null}(t.impact,t.dimensions.droppables),i=Boolean(n),o=t.dimensions.droppables[t.critical.droppable.id],a=n||o,l=a.axis.direction,u="vertical"===l&&("MOVE_UP"===r||"MOVE_DOWN"===r)||"horizontal"===l&&("MOVE_LEFT"===r||"MOVE_RIGHT"===r);if(u&&!i)return null;var c="MOVE_DOWN"===r||"MOVE_RIGHT"===r,s=t.dimensions.draggables[t.critical.draggable.id],d=t.current.page.borderBoxCenter,p=t.dimensions,f=p.draggables,g=p.droppables;return u?Ge({isMovingForward:c,previousPageBorderBoxCenter:d,draggable:s,destination:a,draggables:f,viewport:t.viewport,previousClientSelection:t.current.client.selection,previousImpact:t.impact,afterCritical:t.afterCritical}):function(e){var t=e.isMovingForward,r=e.previousPageBorderBoxCenter,n=e.draggable,i=e.isOver,o=e.draggables,a=e.droppables,l=e.viewport,u=e.afterCritical,c=function(e){var t=e.isMovingForward,r=e.pageBorderBoxCenter,n=e.source,i=e.droppables,o=e.viewport,a=n.subject.active;if(!a)return null;var l=n.axis,u=me(a[l.start],a[l.end]),c=ae(i).filter((function(e){return e!==n})).filter((function(e){return e.isEnabled})).filter((function(e){return Boolean(e.subject.active)})).filter((function(e){return he(o.frame)(ke(e))})).filter((function(e){var r=ke(e);return t?a[l.crossAxisEnd]<r[l.crossAxisEnd]:r[l.crossAxisStart]<a[l.crossAxisStart]})).filter((function(e){var t=ke(e),r=me(t[l.start],t[l.end]);return u(t[l.start])||u(t[l.end])||r(a[l.start])||r(a[l.end])})).sort((function(e,r){var n=ke(e)[l.crossAxisStart],i=ke(r)[l.crossAxisStart];return t?n-i:i-n})).filter((function(e,t,r){return ke(e)[l.crossAxisStart]===ke(r[0])[l.crossAxisStart]}));if(!c.length)return null;if(1===c.length)return c[0];var s=c.filter((function(e){return me(ke(e)[l.start],ke(e)[l.end])(r[l.line])}));return 1===s.length?s[0]:s.length>1?s.sort((function(e,t){return ke(e)[l.start]-ke(t)[l.start]}))[0]:c.sort((function(e,t){var n=Y(r,K(ke(e))),i=Y(r,K(ke(t)));return n!==i?n-i:ke(e)[l.start]-ke(t)[l.start]}))[0]}({isMovingForward:t,pageBorderBoxCenter:r,source:i,droppables:a,viewport:l});if(!c)return null;var s=ue(c.descriptor.id,o),d=function(e){var t=e.pageBorderBoxCenter,r=e.viewport,n=e.destination,i=e.afterCritical,o=e.insideDestination.filter((function(e){return xe({target:He(e,i),destination:n,viewport:r.frame,withDroppableDisplacement:!0})})).sort((function(e,r){var o=q(t,Ne(n,Fe(e,i))),a=q(t,Ne(n,Fe(r,i)));return o<a?-1:a<o?1:e.descriptor.index-r.descriptor.index}));return o[0]||null}({pageBorderBoxCenter:r,viewport:l,destination:c,insideDestination:s,afterCritical:u}),p=function(e){var t=e.previousPageBorderBoxCenter,r=e.moveRelativeTo,n=e.insideDestination,i=e.draggable,o=e.draggables,a=e.destination,l=e.viewport,u=e.afterCritical;if(!r){if(n.length)return null;var c={displaced:ge,displacedBy:fe,at:{type:"REORDER",destination:{droppableId:a.descriptor.id,index:0}}},s=Le({impact:c,draggable:i,droppable:a,draggables:o,afterCritical:u}),d=pe(i,a)?a:je(a,i,o);return _e({draggable:i,destination:d,newPageBorderBoxCenter:s,viewport:l.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?c:null}var p,f=Boolean(t[a.axis.line]<=r.page.borderBox.center[a.axis.line]),g=(p=r.descriptor.index,r.descriptor.id===i.descriptor.id||f?p:p+1);return we({draggable:i,insideDestination:n,destination:a,viewport:l,displacedBy:Ue(a.axis,i.displaceBy),last:ge,index:g})}({previousPageBorderBoxCenter:r,destination:c,draggable:n,draggables:o,moveRelativeTo:d,insideDestination:s,viewport:l,afterCritical:u});if(!p)return null;var f=Le({impact:p,draggable:n,droppable:c,draggables:o,afterCritical:u});return{clientSelection:Me({pageBorderBoxCenter:f,draggable:n,viewport:l}),impact:p,scrollJumpRequest:null}}({isMovingForward:c,previousPageBorderBoxCenter:d,draggable:s,isOver:a,draggables:f,droppables:g,viewport:t.viewport,afterCritical:t.afterCritical})};function qe(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function Ye(e){var t=me(e.top,e.bottom),r=me(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}var Xe=function(e,t){return f(J(e,t))};function Je(e){var t=e.displaced,r=e.id;return Boolean(t.visible[r]||t.invisible[r])}var Ke=function(e){var t=e.pageOffset,r=e.draggable,n=e.draggables,i=e.droppables,o=e.previousImpact,a=e.viewport,l=e.afterCritical,u=Xe(r.page.borderBox,t),c=function(e){var t=e.pageBorderBox,r=e.draggable,n=e.droppables,i=ae(n).filter((function(e){if(!e.isEnabled)return!1;var r,n,i=e.subject.active;if(!i)return!1;if(n=i,!((r=t).left<n.right&&r.right>n.left&&r.top<n.bottom&&r.bottom>n.top))return!1;if(Ye(i)(t.center))return!0;var o=e.axis,a=i.center[o.crossAxisLine],l=t[o.crossAxisStart],u=t[o.crossAxisEnd],c=me(i[o.crossAxisStart],i[o.crossAxisEnd]),s=c(l),d=c(u);return!s&&!d||(s?l<a:u>a)}));return i.length?1===i.length?i[0].descriptor.id:function(e){var t=e.pageBorderBox,r=e.candidates,n=e.draggable.page.borderBox.center,i=r.map((function(e){var r=e.axis,i=z(e.axis.line,t.center[r.line],e.page.borderBox.center[r.crossAxisLine]);return{id:e.descriptor.id,distance:q(n,i)}})).sort((function(e,t){return t.distance-e.distance}));return i[0]?i[0].id:null}({pageBorderBox:t,draggable:r,candidates:i}):null}({pageBorderBox:u,draggable:r,droppables:i});if(!c)return ve;var s=i[c],d=ue(s.descriptor.id,n),p=function(e,t){var r=e.frame;return r?Xe(t,r.scroll.diff.value):t}(s,u);return function(e){var t=e.draggable,r=e.pageBorderBoxWithDroppableScroll,n=e.previousImpact,i=e.destination,o=e.insideDestination,a=e.afterCritical;if(!i.isCombineEnabled)return null;var l=i.axis,u=Ue(i.axis,t.displaceBy),c=u.value,s=r[l.start],d=r[l.end],p=re(de(t,o),(function(e){var t=e.descriptor.id,r=e.page.borderBox,i=r[l.size]/4,o=Ee(t,a),u=Je({displaced:n.displaced,id:t});return o?u?d>r[l.start]+i&&d<r[l.end]-i:s>r[l.start]-c+i&&s<r[l.end]-c-i:u?d>r[l.start]+c+i&&d<r[l.end]+c-i:s>r[l.start]+i&&s<r[l.end]-i}));return p?{displacedBy:u,displaced:n.displaced,at:{type:"COMBINE",combine:{draggableId:p.descriptor.id,droppableId:i.descriptor.id}}}:null}({pageBorderBoxWithDroppableScroll:p,draggable:r,previousImpact:o,destination:s,insideDestination:d,afterCritical:l})||function(e){var t=e.pageBorderBoxWithDroppableScroll,r=e.draggable,n=e.destination,i=e.insideDestination,o=e.last,a=e.viewport,l=e.afterCritical,u=n.axis,c=Ue(n.axis,r.displaceBy),s=c.value,d=t[u.start],p=t[u.end],f=function(e){var t=e.draggable,r=e.closest;return r?e.inHomeList&&r.descriptor.index>t.descriptor.index?r.descriptor.index-1:r.descriptor.index:null}({draggable:r,closest:re(de(r,i),(function(e){var t=e.descriptor.id,r=e.page.borderBox.center[u.line],n=Ee(t,l),i=Je({displaced:o,id:t});return n?i?p<=r:d<r-s:i?p<=r+s:d<r})),inHomeList:pe(r,n)});return we({draggable:r,insideDestination:i,destination:n,viewport:a,last:o,displacedBy:c,index:f})}({pageBorderBoxWithDroppableScroll:p,draggable:r,destination:s,insideDestination:d,last:o.displaced,viewport:a,afterCritical:l})},Qe=function(e,t){var r;return(0,a.A)({},e,((r={})[t.descriptor.id]=t,r))},Ze=function(e){var t=e.state,r=e.clientSelection,n=e.dimensions,i=e.viewport,o=e.impact,l=e.scrollJumpRequest,u=i||t.viewport,c=n||t.dimensions,s=r||t.current.client.selection,d=W(s,t.initial.client.selection),p={offset:d,selection:s,borderBoxCenter:U(t.initial.client.borderBoxCenter,d)},f={selection:U(p.selection,u.scroll.current),borderBoxCenter:U(p.borderBoxCenter,u.scroll.current),offset:U(p.offset,u.scroll.diff.value)},g={client:p,page:f};if("COLLECTING"===t.phase)return(0,a.A)({phase:"COLLECTING"},t,{dimensions:c,viewport:u,current:g});var v=c.draggables[t.critical.draggable.id],m=o||Ke({pageOffset:f.offset,draggable:v,draggables:c.draggables,droppables:c.droppables,previousImpact:t.impact,viewport:u,afterCritical:t.afterCritical}),h=function(e){var t=e.draggable,r=e.draggables,n=e.droppables,i=e.impact,o=function(e){var t=e.previousImpact,r=e.impact,n=e.droppables,i=Ve(t),o=Ve(r);if(!i)return n;if(i===o)return n;var l=n[i];if(!l.subject.withPlaceholder)return n;var u=function(e){var t=e.subject.withPlaceholder;t||R(!1);var r=e.frame;if(!r){var n=Z({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return(0,a.A)({},e,{subject:n})}var i=t.oldFrameMaxScroll;i||R(!1);var o=We(r,i),l=Z({page:e.subject.page,axis:e.axis,frame:o,withPlaceholder:null});return(0,a.A)({},e,{subject:l,frame:o})}(l);return Qe(n,u)}({previousImpact:e.previousImpact,impact:i,droppables:n}),l=Ve(i);if(!l)return o;var u=n[l];if(pe(t,u))return o;if(u.subject.withPlaceholder)return o;var c=je(u,t,r);return Qe(o,c)}({draggable:v,impact:m,previousImpact:t.impact,draggables:c.draggables,droppables:c.droppables});return(0,a.A)({},t,{current:g,dimensions:{draggables:c.draggables,droppables:h},impact:m,viewport:u,scrollJumpRequest:l||null,forceShouldAnimate:!l&&null})},$e=function(e){var t=e.impact,r=e.viewport,n=e.draggables,i=e.destination,o=e.forceShouldAnimate,l=t.displaced,u=function(e,t){return e.map((function(e){return t[e]}))}(l.all,n),c=Ae({afterDragging:u,destination:i,displacedBy:t.displacedBy,viewport:r.frame,forceShouldAnimate:o,last:l});return(0,a.A)({},t,{displaced:c})},et=function(e){var t=e.impact,r=e.draggable,n=e.droppable,i=e.draggables,o=e.viewport,a=e.afterCritical,l=Le({impact:t,draggable:r,draggables:i,droppable:n,afterCritical:a});return Me({pageBorderBoxCenter:l,draggable:r,viewport:o})},tt=function(e){var t=e.state,r=e.dimensions,n=e.viewport;"SNAP"!==t.movementMode&&R(!1);var i=t.impact,o=n||t.viewport,a=r||t.dimensions,l=a.draggables,u=a.droppables,c=l[t.critical.draggable.id],s=Ve(i);s||R(!1);var d=u[s],p=$e({impact:i,viewport:o,destination:d,draggables:l}),f=et({impact:p,draggable:c,droppable:d,draggables:l,viewport:o,afterCritical:t.afterCritical});return Ze({impact:p,clientSelection:f,state:t,dimensions:a,viewport:o})},rt=function(e){var t=e.draggable,r=e.home,n=e.draggables,i=e.viewport,o=Ue(r.axis,t.displaceBy),a=ue(r.descriptor.id,n),l=a.indexOf(t);-1===l&&R(!1);var u,c=a.slice(l+1),s=c.reduce((function(e,t){return e[t.descriptor.id]=!0,e}),{}),d={inVirtualList:"virtual"===r.descriptor.mode,displacedBy:o,effected:s};return{impact:{displaced:Ae({afterDragging:c,destination:r,displacedBy:o,last:null,viewport:i.frame,forceShouldAnimate:!1}),displacedBy:o,at:{type:"REORDER",destination:(u=t.descriptor,{index:u.index,droppableId:u.droppableId})}},afterCritical:d}},nt=function(e){return"SNAP"===e.movementMode},it=function(e,t,r){var n=function(e,t){return{draggables:e.draggables,droppables:Qe(e.droppables,t)}}(e.dimensions,t);return!nt(e)||r?Ze({state:e,dimensions:n}):tt({state:e,dimensions:n})};function ot(e){return e.isDragging&&"SNAP"===e.movementMode?(0,a.A)({phase:"DRAGGING"},e,{scrollJumpRequest:null}):e}var at={phase:"IDLE",completed:null,shouldFlush:!1},lt=function(e,t){if(void 0===e&&(e=at),"FLUSH"===t.type)return(0,a.A)({},at,{shouldFlush:!0});if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&R(!1);var r=t.payload,n=r.critical,i=r.clientSelection,o=r.viewport,l=r.dimensions,u=r.movementMode,c=l.draggables[n.draggable.id],s=l.droppables[n.droppable.id],d={selection:i,borderBoxCenter:c.client.borderBox.center,offset:H},p={client:d,page:{selection:U(d.selection,o.scroll.initial),borderBoxCenter:U(d.selection,o.scroll.initial),offset:U(d.selection,o.scroll.diff.value)}},f=ae(l.droppables).every((function(e){return!e.isFixedOnPage})),g=rt({draggable:c,home:s,draggables:l.draggables,viewport:o}),v=g.impact;return{phase:"DRAGGING",isDragging:!0,critical:n,movementMode:u,dimensions:l,initial:p,current:p,isWindowScrollAllowed:f,impact:v,afterCritical:g.afterCritical,onLiftImpact:v,viewport:o,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase?e:("DRAGGING"!==e.phase&&R(!1),(0,a.A)({phase:"COLLECTING"},e,{phase:"COLLECTING"}));if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&R(!1),function(e){var t=e.state,r=e.published,n=r.modified.map((function(e){var r=t.dimensions.droppables[e.droppableId];return $(r,e.scroll)})),i=(0,a.A)({},t.dimensions.droppables,{},ie(n)),o=oe(function(e){var t=e.additions,r=e.updatedDroppables,n=e.viewport,i=n.scroll.diff.value;return t.map((function(e){var t=e.descriptor.droppableId,o=function(e){var t=e.frame;return t||R(!1),t}(r[t]),l=o.scroll.diff.value,u=function(e){var t=e.draggable,r=e.offset,n=e.initialWindowScroll,i=y(t.client,r),o=D(i,n);return(0,a.A)({},t,{placeholder:(0,a.A)({},t.placeholder,{client:i}),client:i,page:o})}({draggable:e,offset:U(i,l),initialWindowScroll:n.scroll.initial});return u}))}({additions:r.additions,updatedDroppables:i,viewport:t.viewport})),l=(0,a.A)({},t.dimensions.draggables,{},o);r.removals.forEach((function(e){delete l[e]}));var u={droppables:i,draggables:l},c=Ve(t.impact),s=c?u.droppables[c]:null,d=u.draggables[t.critical.draggable.id],p=u.droppables[t.critical.droppable.id],f=rt({draggable:d,home:p,draggables:l,viewport:t.viewport}),g=f.impact,v=f.afterCritical,m=s&&s.isCombineEnabled?t.impact:g,h=Ke({pageOffset:t.current.page.offset,draggable:u.draggables[t.critical.draggable.id],draggables:u.draggables,droppables:u.droppables,previousImpact:m,viewport:t.viewport,afterCritical:v}),b=(0,a.A)({phase:"DRAGGING"},t,{phase:"DRAGGING",impact:h,onLiftImpact:g,dimensions:u,afterCritical:v,forceShouldAnimate:!1});return"COLLECTING"===t.phase?b:(0,a.A)({phase:"DROP_PENDING"},b,{phase:"DROP_PENDING",reason:t.reason,isWaiting:!1})}({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;qe(e)||R(!1);var m=t.payload.client;return j(m,e.current.client.selection)?e:Ze({state:e,clientSelection:m,impact:nt(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase)return ot(e);if("COLLECTING"===e.phase)return ot(e);qe(e)||R(!1);var h=t.payload,b=h.id,I=h.newScroll,x=e.dimensions.droppables[b];if(!x)return e;var A=$(x,I);return it(e,A,!1)}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;qe(e)||R(!1);var C=t.payload,w=C.id,E=C.isEnabled,B=e.dimensions.droppables[w];B||R(!1),B.isEnabled===E&&R(!1);var P=(0,a.A)({},B,{isEnabled:E});return it(e,P,!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;qe(e)||R(!1);var O=t.payload,S=O.id,N=O.isCombineEnabled,L=e.dimensions.droppables[S];L||R(!1),L.isCombineEnabled===N&&R(!1);var T=(0,a.A)({},L,{isCombineEnabled:N});return it(e,T,!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;qe(e)||R(!1),e.isWindowScrollAllowed||R(!1);var M=t.payload.newScroll;if(j(e.viewport.scroll.current,M))return ot(e);var _=Re(e.viewport,M);return nt(e)?tt({state:e,viewport:_}):Ze({state:e,viewport:_})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!qe(e))return e;var G=t.payload.maxScroll;if(j(G,e.viewport.scroll.max))return e;var k=(0,a.A)({},e.viewport,{scroll:(0,a.A)({},e.viewport.scroll,{max:G})});return(0,a.A)({phase:"DRAGGING"},e,{viewport:k})}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&R(!1);var F=ze({state:e,type:t.type});return F?Ze({state:e,impact:F.impact,clientSelection:F.clientSelection,scrollJumpRequest:F.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){var W=t.payload.reason;return"COLLECTING"!==e.phase&&R(!1),(0,a.A)({phase:"DROP_PENDING"},e,{phase:"DROP_PENDING",isWaiting:!0,reason:W})}if("DROP_ANIMATE"===t.type){var V=t.payload,z=V.completed,q=V.dropDuration,Y=V.newHomeClientOffset;return"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&R(!1),{phase:"DROP_ANIMATING",completed:z,dropDuration:q,newHomeClientOffset:Y,dimensions:e.dimensions}}return"DROP_COMPLETE"===t.type?{phase:"IDLE",completed:t.payload.completed,shouldFlush:!1}:e},ut=function(e){return{type:"LIFT",payload:e}},ct=function(e){return{type:"PUBLISH_WHILE_DRAGGING",payload:e}},st=function(){return{type:"COLLECTION_STARTING",payload:null}},dt=function(e){return{type:"UPDATE_DROPPABLE_SCROLL",payload:e}},pt=function(e){return{type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}},ft=function(e){return{type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}},gt=function(e){return{type:"MOVE",payload:e}},vt=function(){return{type:"MOVE_UP",payload:null}},mt=function(){return{type:"MOVE_DOWN",payload:null}},ht=function(){return{type:"MOVE_RIGHT",payload:null}},bt=function(){return{type:"MOVE_LEFT",payload:null}},yt=function(){return{type:"FLUSH",payload:null}},Dt=function(e){return{type:"DROP_COMPLETE",payload:e}},It=function(e){return{type:"DROP",payload:e}},xt="cubic-bezier(.2,1,.1,1)",At=0,Ct=.7,wt=.75,Et="0.2s cubic-bezier(0.2, 0, 0, 1)",Bt={fluid:"opacity "+Et,snap:"transform "+Et+", opacity "+Et,drop:function(e){var t=e+"s "+xt;return"transform "+t+", opacity "+t},outOfTheWay:"transform "+Et,placeholder:"height "+Et+", width "+Et+", margin "+Et},Pt=function(e){return j(e,H)?null:"translate("+e.x+"px, "+e.y+"px)"},Ot=Pt,St=.33,Nt=.55-St,Lt=function(e){var t=e.getState,r=e.dispatch;return function(e){return function(n){if("DROP"===n.type){var i=t(),o=n.payload.reason;if("COLLECTING"!==i.phase){if("IDLE"!==i.phase){"DROP_PENDING"===i.phase&&i.isWaiting&&R(!1),"DRAGGING"!==i.phase&&"DROP_PENDING"!==i.phase&&R(!1);var l=i.critical,u=i.dimensions,c=u.draggables[i.critical.draggable.id],s=function(e){var t=e.draggables,r=e.reason,n=e.lastImpact,i=e.home,o=e.viewport,l=e.onLiftImpact;return n.at&&"DROP"===r?"REORDER"===n.at.type?{impact:n,didDropInsideDroppable:!0}:{impact:(0,a.A)({},n,{displaced:ge}),didDropInsideDroppable:!0}:{impact:$e({draggables:t,impact:l,destination:i,viewport:o,forceShouldAnimate:!0}),didDropInsideDroppable:!1}}({reason:o,lastImpact:i.impact,afterCritical:i.afterCritical,onLiftImpact:i.onLiftImpact,home:i.dimensions.droppables[i.critical.droppable.id],viewport:i.viewport,draggables:i.dimensions.draggables}),d=s.impact,p=s.didDropInsideDroppable,f=p?ce(d):null,g=p?se(d):null,v={index:l.draggable.index,droppableId:l.droppable.id},m={draggableId:c.descriptor.id,type:c.descriptor.type,source:v,reason:o,mode:i.movementMode,destination:f,combine:g},h=function(e){var t=e.impact,r=e.draggable,n=e.dimensions,i=e.viewport,o=e.afterCritical,a=n.draggables,l=n.droppables,u=Ve(t),c=u?l[u]:null,s=l[r.descriptor.droppableId],d=et({impact:t,draggable:r,draggables:a,afterCritical:o,droppable:c||s,viewport:i});return W(d,r.client.borderBox.center)}({impact:d,draggable:c,dimensions:u,viewport:i.viewport,afterCritical:i.afterCritical}),b={critical:i.critical,afterCritical:i.afterCritical,result:m,impact:d};if(!j(i.current.client.offset,h)||Boolean(m.combine)){var y=function(e){var t=e.reason,r=q(e.current,e.destination);if(r<=0)return St;if(r>=1500)return.55;var n=St+Nt*(r/1500);return Number(("CANCEL"===t?.6*n:n).toFixed(2))}({current:i.current.client.offset,destination:h,reason:o});r({type:"DROP_ANIMATE",payload:{newHomeClientOffset:h,dropDuration:y,completed:b}})}else r(Dt({completed:b}))}}else r(function(e){return{type:"DROP_PENDING",payload:e}}({reason:o}))}else e(n)}}},Rt=function(){return{x:window.pageXOffset,y:window.pageYOffset}};var Tt=function(e){var t=function(e){var t=e.onWindowScroll,r=C((function(){t(Rt())})),n=function(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(t){t.target!==window&&t.target!==window.document||e()}}}(r),i=P;function o(){return i!==P}return{start:function(){o()&&R(!1),i=O(window,[n])},stop:function(){o()||R(!1),r.cancel(),i(),i=P},isActive:o}}({onWindowScroll:function(t){e.dispatch({type:"MOVE_BY_WINDOW_SCROLL",payload:{newScroll:t}})}});return function(e){return function(r){t.isActive()||"INITIAL_PUBLISH"!==r.type||t.start(),t.isActive()&&function(e){return"DROP_COMPLETE"===e.type||"DROP_ANIMATE"===e.type||"FLUSH"===e.type}(r)&&t.stop(),e(r)}}},Mt=function(e,t){t()},_t=function(e,t){return{draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t}},Gt=function(e,t,r,n){if(e){var i=function(e){var t=!1,r=!1,n=setTimeout((function(){r=!0})),i=function(i){t||r||(t=!0,e(i),clearTimeout(n))};return i.wasCalled=function(){return t},i}(r);e(t,{announce:i}),i.wasCalled()||r(n(t))}else r(n(t))},kt=function(e,t){var r=function(e,t){var r,n=(r=[],{add:function(e){var t=setTimeout((function(){return function(e){var t=te(r,(function(t){return t.timerId===e}));-1===t&&R(!1),r.splice(t,1)[0].callback()}(t)})),n={timerId:t,callback:e};r.push(n)},flush:function(){if(r.length){var e=[].concat(r);r.length=0,e.forEach((function(e){clearTimeout(e.timerId),e.callback()}))}}}),i=null,o=function(r){i||R(!1),i=null,Mt(0,(function(){return Gt(e().onDragEnd,r,t,F.onDragEnd)}))};return{beforeCapture:function(t,r){i&&R(!1),Mt(0,(function(){var n=e().onBeforeCapture;n&&n({draggableId:t,mode:r})}))},beforeStart:function(t,r){i&&R(!1),Mt(0,(function(){var n=e().onBeforeDragStart;n&&n(_t(t,r))}))},start:function(r,o){i&&R(!1);var a=_t(r,o);i={mode:o,lastCritical:r,lastLocation:a.source,lastCombine:null},n.add((function(){Mt(0,(function(){return Gt(e().onDragStart,a,t,F.onDragStart)}))}))},update:function(r,o){var l=ce(o),u=se(o);i||R(!1);var c=!function(e,t){if(e===t)return!0;var r=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,n=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return r&&n}(r,i.lastCritical);c&&(i.lastCritical=r);var s,d,p=(d=l,!(null==(s=i.lastLocation)&&null==d||null!=s&&null!=d&&s.droppableId===d.droppableId&&s.index===d.index));p&&(i.lastLocation=l);var f=!function(e,t){return null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId}(i.lastCombine,u);if(f&&(i.lastCombine=u),c||p||f){var g=(0,a.A)({},_t(r,i.mode),{combine:u,destination:l});n.add((function(){Mt(0,(function(){return Gt(e().onDragUpdate,g,t,F.onDragUpdate)}))}))}},flush:function(){i||R(!1),n.flush()},drop:o,abort:function(){if(i){var e=(0,a.A)({},_t(i.lastCritical,i.mode),{combine:null,destination:null,reason:"CANCEL"});o(e)}}}}(e,t);return function(e){return function(t){return function(n){if("BEFORE_INITIAL_CAPTURE"!==n.type){if("INITIAL_PUBLISH"===n.type){var i=n.payload.critical;return r.beforeStart(i,n.payload.movementMode),t(n),void r.start(i,n.payload.movementMode)}if("DROP_COMPLETE"===n.type){var o=n.payload.completed.result;return r.flush(),t(n),void r.drop(o)}if(t(n),"FLUSH"!==n.type){var a=e.getState();"DRAGGING"===a.phase&&r.update(a.critical,a.impact)}else r.abort()}else r.beforeCapture(n.payload.draggableId,n.payload.movementMode)}}}},Ft=function(e){return function(t){return function(r){if("DROP_ANIMATION_FINISHED"===r.type){var n=e.getState();"DROP_ANIMATING"!==n.phase&&R(!1),e.dispatch(Dt({completed:n.completed}))}else t(r)}}},Ht=function(e){var t=null,r=null;return function(n){return function(i){if("FLUSH"!==i.type&&"DROP_COMPLETE"!==i.type&&"DROP_ANIMATION_FINISHED"!==i.type||(r&&(cancelAnimationFrame(r),r=null),t&&(t(),t=null)),n(i),"DROP_ANIMATE"===i.type){var o={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch({type:"DROP_ANIMATION_FINISHED",payload:null})}};r=requestAnimationFrame((function(){r=null,t=O(window,[o])}))}}}},Ut=function(e){return function(t){return function(r){if(t(r),"PUBLISH_WHILE_DRAGGING"===r.type){var n=e.getState();"DROP_PENDING"===n.phase&&(n.isWaiting||e.dispatch(It({reason:n.reason})))}}}},Wt=l.Zz,jt=function(e){var t,r=e.dimensionMarshal,n=e.focusMarshal,i=e.styleMarshal,o=e.getResponders,a=e.announce,u=e.autoScroller;return(0,l.y$)(lt,Wt((0,l.Tw)((t=i,function(){return function(e){return function(r){"INITIAL_PUBLISH"===r.type&&t.dragging(),"DROP_ANIMATE"===r.type&&t.dropping(r.payload.completed.result.reason),"FLUSH"!==r.type&&"DROP_COMPLETE"!==r.type||t.resting(),e(r)}}}),function(e){return function(){return function(t){return function(r){"DROP_COMPLETE"!==r.type&&"FLUSH"!==r.type&&"DROP_ANIMATE"!==r.type||e.stopPublishing(),t(r)}}}}(r),function(e){return function(t){var r=t.getState,n=t.dispatch;return function(t){return function(i){if("LIFT"===i.type){var o=i.payload,a=o.id,l=o.clientSelection,u=o.movementMode,c=r();"DROP_ANIMATING"===c.phase&&n(Dt({completed:c.completed})),"IDLE"!==r().phase&&R(!1),n(yt()),n({type:"BEFORE_INITIAL_CAPTURE",payload:{draggableId:a,movementMode:u}});var s={draggableId:a,scrollOptions:{shouldPublishImmediately:"SNAP"===u}},d=e.startPublishing(s),p=d.critical,f=d.dimensions,g=d.viewport;n({type:"INITIAL_PUBLISH",payload:{critical:p,dimensions:f,clientSelection:l,movementMode:u,viewport:g}})}else t(i)}}}}(r),Lt,Ft,Ht,Ut,function(e){return function(t){return function(r){return function(n){if(function(e){return"DROP_COMPLETE"===e.type||"DROP_ANIMATE"===e.type||"FLUSH"===e.type}(n))return e.stop(),void r(n);if("INITIAL_PUBLISH"===n.type){r(n);var i=t.getState();return"DRAGGING"!==i.phase&&R(!1),void e.start(i)}r(n),e.scroll(t.getState())}}}}(u),Tt,function(e){var t=!1;return function(){return function(r){return function(n){if("INITIAL_PUBLISH"===n.type)return t=!0,e.tryRecordFocus(n.payload.critical.draggable.id),r(n),void e.tryRestoreFocusRecorded();if(r(n),t){if("FLUSH"===n.type)return t=!1,void e.tryRestoreFocusRecorded();if("DROP_COMPLETE"===n.type){t=!1;var i=n.payload.completed.result;i.combine&&e.tryShiftRecord(i.draggableId,i.combine.draggableId),e.tryRestoreFocusRecorded()}}}}}}(n),kt(o,a))))},Vt=function(e){var t=e.scrollHeight,r=e.scrollWidth,n=e.height,i=e.width,o=W({x:r,y:t},{x:i,y:n});return{x:Math.max(0,o.x),y:Math.max(0,o.y)}},zt=function(){var e=document.documentElement;return e||R(!1),e},qt=function(){var e=zt();return Vt({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})};function Yt(e,t,r){return r.descriptor.id!==t.id&&r.descriptor.type===t.type&&"virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode}var Xt,Jt,Kt=function(e,t){var r=null,n=function(e){var t=e.registry,r=e.callbacks,n={additions:{},removals:{},modified:{}},i=null,o=function(){i||(r.collectionStarting(),i=requestAnimationFrame((function(){i=null;var e=n,o=e.additions,a=e.removals,l=e.modified,u=Object.keys(o).map((function(e){return t.draggable.getById(e).getDimension(H)})).sort((function(e,t){return e.descriptor.index-t.descriptor.index})),c=Object.keys(l).map((function(e){return{droppableId:e,scroll:t.droppable.getById(e).callbacks.getScrollWhileDragging()}})),s={additions:u,removals:Object.keys(a),modified:c};n={additions:{},removals:{},modified:{}},r.publish(s)})))};return{add:function(e){var t=e.descriptor.id;n.additions[t]=e,n.modified[e.descriptor.droppableId]=!0,n.removals[t]&&delete n.removals[t],o()},remove:function(e){var t=e.descriptor;n.removals[t.id]=!0,n.modified[t.droppableId]=!0,n.additions[t.id]&&delete n.additions[t.id],o()},stop:function(){i&&(cancelAnimationFrame(i),i=null,n={additions:{},removals:{},modified:{}})}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),i=function(t){r||R(!1);var i=r.critical.draggable;"ADDITION"===t.type&&Yt(e,i,t.value)&&n.add(t.value),"REMOVAL"===t.type&&Yt(e,i,t.value)&&n.remove(t.value)};return{updateDroppableIsEnabled:function(n,i){e.droppable.exists(n)||R(!1),r&&t.updateDroppableIsEnabled({id:n,isEnabled:i})},updateDroppableIsCombineEnabled:function(n,i){r&&(e.droppable.exists(n)||R(!1),t.updateDroppableIsCombineEnabled({id:n,isCombineEnabled:i}))},scrollDroppable:function(t,n){r&&e.droppable.getById(t).callbacks.scroll(n)},updateDroppableScroll:function(n,i){r&&(e.droppable.exists(n)||R(!1),t.updateDroppableScroll({id:n,newScroll:i}))},startPublishing:function(t){r&&R(!1);var n=e.draggable.getById(t.draggableId),o=e.droppable.getById(n.descriptor.droppableId),a={draggable:n.descriptor,droppable:o.descriptor},l=e.subscribe(i);return r={critical:a,unsubscribe:l},function(e){var t,r,n,i,o,a,l,u=e.critical,c=e.scrollOptions,s=e.registry,d=(t=Rt(),r=qt(),n=t.y,i=t.x,a=(o=zt()).clientWidth,l=o.clientHeight,{frame:f({top:n,left:i,right:i+a,bottom:n+l}),scroll:{initial:t,current:t,max:r,diff:{value:H,displacement:H}}}),p=d.scroll.current,g=u.droppable,v=s.droppable.getAllByType(g.type).map((function(e){return e.callbacks.getDimensionAndWatchScroll(p,c)})),m=s.draggable.getAllByType(u.draggable.type).map((function(e){return e.getDimension(p)}));return{dimensions:{draggables:oe(m),droppables:ie(v)},critical:u,viewport:d}}({critical:a,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:function(){if(r){n.stop();var t=r.critical.droppable;e.droppable.getAllByType(t.type).forEach((function(e){return e.callbacks.dragStopped()})),r.unsubscribe(),r=null}}}},Qt=function(e,t){return"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason},Zt=function(e){window.scrollBy(e.x,e.y)},$t=(0,A.A)((function(e){return ae(e).filter((function(e){return!!e.isEnabled&&!!e.frame}))})),er=function(e){return Math.pow(e,2)},tr=function(e){var t=e.startOfRange,r=e.endOfRange,n=e.current,i=r-t;return 0===i?0:(n-t)/i},rr=360,nr=1200,ir=function(e){var t=e.distanceToEdge,r=e.thresholds,n=e.dragStartTime,i=e.shouldUseTimeDampening,o=function(e,t){if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return 28;if(e===t.startScrollingFrom)return 1;var r=tr({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e}),n=28*er(1-r);return Math.ceil(n)}(t,r);return 0===o?0:i?Math.max(function(e,t){var r=t,n=nr,i=Date.now()-r;if(i>=nr)return e;if(i<rr)return 1;var o=tr({startOfRange:rr,endOfRange:n,current:i}),a=e*er(o);return Math.ceil(a)}(o,n),1):o},or=function(e){var t=e.container,r=e.distanceToEdges,n=e.dragStartTime,i=e.axis,o=e.shouldUseTimeDampening,a=function(e,t){return{startScrollingFrom:.25*e[t.size],maxScrollValueAt:.05*e[t.size]}}(t,i);return r[i.end]<r[i.start]?ir({distanceToEdge:r[i.end],thresholds:a,dragStartTime:n,shouldUseTimeDampening:o}):-1*ir({distanceToEdge:r[i.start],thresholds:a,dragStartTime:n,shouldUseTimeDampening:o})},ar=X((function(e){return 0===e?0:e})),lr=function(e){var t=e.dragStartTime,r=e.container,n=e.subject,i=e.center,o=e.shouldUseTimeDampening,a={top:i.y-r.top,right:r.right-i.x,bottom:r.bottom-i.y,left:i.x-r.left},l=or({container:r,distanceToEdges:a,dragStartTime:t,axis:ye,shouldUseTimeDampening:o}),u=or({container:r,distanceToEdges:a,dragStartTime:t,axis:De,shouldUseTimeDampening:o}),c=ar({x:u,y:l});if(j(c,H))return null;var s=function(e){var t=e.container,r=e.subject,n=e.proposedScroll,i=r.height>t.height,o=r.width>t.width;return o||i?o&&i?null:{x:o?0:n.x,y:i?0:n.y}:n}({container:r,subject:n,proposedScroll:c});return s?j(s,H)?null:s:null},ur=X((function(e){return 0===e?0:e>0?1:-1})),cr=(Xt=function(e,t){return e<0?e:e>t?e-t:0},function(e){var t=e.current,r=e.max,n=e.change,i=U(t,n),o={x:Xt(i.x,r.x),y:Xt(i.y,r.y)};return j(o,H)?null:o}),sr=function(e){var t=e.max,r=e.current,n=e.change,i={x:Math.max(r.x,t.x),y:Math.max(r.y,t.y)},o=ur(n),a=cr({max:i,current:r,change:o});return!a||0!==o.x&&0===a.x||0!==o.y&&0===a.y},dr=function(e,t){return sr({current:e.scroll.current,max:e.scroll.max,change:t})},pr=function(e,t){var r=e.frame;return!!r&&sr({current:r.scroll.current,max:r.scroll.max,change:t})},fr=function(e){var t=e.state,r=e.dragStartTime,n=e.shouldUseTimeDampening,i=e.scrollWindow,o=e.scrollDroppable,a=t.current.page.borderBoxCenter,l=t.dimensions.draggables[t.critical.draggable.id].page.marginBox;if(t.isWindowScrollAllowed){var u=function(e){var t=e.viewport,r=e.subject,n=e.center,i=e.shouldUseTimeDampening,o=lr({dragStartTime:e.dragStartTime,container:t.frame,subject:r,center:n,shouldUseTimeDampening:i});return o&&dr(t,o)?o:null}({dragStartTime:r,viewport:t.viewport,subject:l,center:a,shouldUseTimeDampening:n});if(u)return void i(u)}var c=function(e){var t=e.center,r=e.destination,n=e.droppables;if(r){var i=n[r];return i.frame?i:null}var o=function(e,t){var r=re($t(t),(function(t){return t.frame||R(!1),Ye(t.frame.pageMarginBox)(e)}));return r}(t,n);return o}({center:a,destination:Ve(t.impact),droppables:t.dimensions.droppables});if(c){var s=function(e){var t=e.droppable,r=e.subject,n=e.center,i=e.dragStartTime,o=e.shouldUseTimeDampening,a=t.frame;if(!a)return null;var l=lr({dragStartTime:i,container:a.pageMarginBox,subject:r,center:n,shouldUseTimeDampening:o});return l&&pr(t,l)?l:null}({dragStartTime:r,droppable:c,subject:l,center:a,shouldUseTimeDampening:n});s&&o(c.descriptor.id,s)}},gr=function(e){var t=e.move,r=e.scrollDroppable,n=e.scrollWindow;return function(e){var i=e.scrollJumpRequest;if(i){var o=Ve(e.impact);o||R(!1);var a=function(e,t){if(!pr(e,t))return t;var n=function(e,t){var r=e.frame;return r&&pr(e,t)?cr({current:r.scroll.current,max:r.scroll.max,change:t}):null}(e,t);if(!n)return r(e.descriptor.id,t),null;var i=W(t,n);return r(e.descriptor.id,i),W(t,i)}(e.dimensions.droppables[o],i);if(a){var l=e.viewport,u=function(e,t,r){if(!e)return r;if(!dr(t,r))return r;var i=function(e,t){if(!dr(e,t))return null;var r=e.scroll.max,n=e.scroll.current;return cr({current:n,max:r,change:t})}(t,r);if(!i)return n(r),null;var o=W(r,i);return n(o),W(r,o)}(e.isWindowScrollAllowed,l,a);u&&function(e,r){var n=U(e.current.client.selection,r);t({client:n})}(e,u)}}}},vr=function(e){var t=e.scrollDroppable,r=e.scrollWindow,n=e.move,i=function(e){var t=e.scrollDroppable,r=C(e.scrollWindow),n=C(t),i=null,o=function(e){i||R(!1);var t=i,o=t.shouldUseTimeDampening,a=t.dragStartTime;fr({state:e,scrollWindow:r,scrollDroppable:n,dragStartTime:a,shouldUseTimeDampening:o})};return{start:function(e){i&&R(!1);var t=Date.now(),r=!1,n=function(){r=!0};fr({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:n,scrollDroppable:n}),i={dragStartTime:t,shouldUseTimeDampening:r},r&&o(e)},stop:function(){i&&(r.cancel(),n.cancel(),i=null)},scroll:o}}({scrollWindow:r,scrollDroppable:t}),o=gr({move:n,scrollWindow:r,scrollDroppable:t});return{scroll:function(e){"DRAGGING"===e.phase&&("FLUID"!==e.movementMode?e.scrollJumpRequest&&o(e):i.scroll(e))},start:i.start,stop:i.stop}},mr="data-rbd",hr={base:Jt=mr+"-drag-handle",draggableId:Jt+"-draggable-id",contextId:Jt+"-context-id"},br=function(){var e=mr+"-draggable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),yr=function(){var e=mr+"-droppable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),Dr={contextId:mr+"-scroll-container-context-id"},Ir=function(e,t){return e.map((function(e){var r=e.styles[t];return r?e.selector+" { "+r+" }":""})).join(" ")},xr=function(e){var t,r,n,i=(t=e,function(e){return"["+e+'="'+t+'"]'}),o=(r="\n      cursor: -webkit-grab;\n      cursor: grab;\n    ",{selector:i(hr.contextId),styles:{always:"\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        ",resting:r,dragging:"pointer-events: none;",dropAnimating:r}}),a=[(n="\n      transition: "+Bt.outOfTheWay+";\n    ",{selector:i(br.contextId),styles:{dragging:n,dropAnimating:n,userCancel:n}}),o,{selector:i(yr.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:"\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      "}}];return{always:Ir(a,"always"),resting:Ir(a,"resting"),dragging:Ir(a,"dragging"),dropAnimating:Ir(a,"dropAnimating"),userCancel:Ir(a,"userCancel")}},Ar="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?n.useLayoutEffect:n.useEffect,Cr=function(){var e=document.querySelector("head");return e||R(!1),e},wr=function(e){var t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t},Er=function(e){return e&&e.ownerDocument?e.ownerDocument.defaultView:window};function Br(e){return e instanceof Er(e).HTMLElement}function Pr(){var e={draggables:{},droppables:{}},t=[];function r(e){t.length&&t.forEach((function(t){return t(e)}))}function n(t){return e.draggables[t]||null}function i(t){return e.droppables[t]||null}return{draggable:{register:function(t){e.draggables[t.descriptor.id]=t,r({type:"ADDITION",value:t})},update:function(t,r){var n=e.draggables[r.descriptor.id];n&&n.uniqueId===t.uniqueId&&(delete e.draggables[r.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:function(t){var i=t.descriptor.id,o=n(i);o&&t.uniqueId===o.uniqueId&&(delete e.draggables[i],r({type:"REMOVAL",value:t}))},getById:function(e){var t=n(e);return t||R(!1),t},findById:n,exists:function(e){return Boolean(n(e))},getAllByType:function(t){return ee(e.draggables).filter((function(e){return e.descriptor.type===t}))}},droppable:{register:function(t){e.droppables[t.descriptor.id]=t},unregister:function(t){var r=i(t.descriptor.id);r&&t.uniqueId===r.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){var t=i(e);return t||R(!1),t},findById:i,exists:function(e){return Boolean(i(e))},getAllByType:function(t){return ee(e.droppables).filter((function(e){return e.descriptor.type===t}))}},subscribe:function(e){return t.push(e),function(){var r=t.indexOf(e);-1!==r&&t.splice(r,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var Or=i().createContext(null),Sr=function(){var e=document.body;return e||R(!1),e},Nr={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},Lr=function(e){return"rbd-announcement-"+e},Rr=0,Tr={separator:"::"};function Mr(e,t){return void 0===t&&(t=Tr),s((function(){return""+e+t.separator+Rr++}),[t.separator,e])}var _r=i().createContext(null);function Gr(e){var t=(0,n.useRef)(e);return(0,n.useEffect)((function(){t.current=e})),t}var kr,Fr,Hr=((kr={})[13]=!0,kr[9]=!0,kr),Ur=function(e){Hr[e.keyCode]&&e.preventDefault()},Wr=function(){var e="visibilitychange";return"undefined"==typeof document?e:re([e,"ms"+e,"webkit"+e,"moz"+e,"o"+e],(function(e){return"on"+e in document}))||e}(),jr={type:"IDLE"};function Vr(){}var zr=((Fr={})[34]=!0,Fr[33]=!0,Fr[36]=!0,Fr[35]=!0,Fr);var qr={type:"IDLE"},Yr={input:!0,button:!0,textarea:!0,select:!0,option:!0,optgroup:!0,video:!0,audio:!0};function Xr(e,t){if(null==t)return!1;if(Boolean(Yr[t.tagName.toLowerCase()]))return!0;var r=t.getAttribute("contenteditable");return"true"===r||""===r||t!==e&&Xr(e,t.parentElement)}function Jr(e,t){var r=t.target;return!!Br(r)&&Xr(e,r)}var Kr=function(e){return f(e.getBoundingClientRect()).center},Qr=function(){var e="matches";return"undefined"==typeof document?e:re([e,"msMatchesSelector","webkitMatchesSelector"],(function(e){return e in Element.prototype}))||e}();function Zr(e,t){return null==e?null:e[Qr](t)?e:Zr(e.parentElement,t)}function $r(e,t){return e.closest?e.closest(t):Zr(e,t)}function en(e){e.preventDefault()}function tn(e){var t=e.expected,r=e.phase,n=e.isLockActive;return e.shouldWarn,!!n()&&t===r}function rn(e){var t=e.lockAPI,r=e.store,n=e.registry,i=e.draggableId;if(t.isClaimed())return!1;var o=n.draggable.findById(i);return!!o&&!!o.options.isEnabled&&!!Qt(r.getState(),i)}var nn=[function(e){var t=(0,n.useRef)(jr),r=(0,n.useRef)(P),i=s((function(){return{eventName:"mousedown",fn:function(t){if(!t.defaultPrevented&&0===t.button&&!(t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)){var n=e.findClosestDraggableId(t);if(n){var i=e.tryGetLock(n,l,{sourceEvent:t});if(i){t.preventDefault();var o={x:t.clientX,y:t.clientY};r.current(),p(i,o)}}}}}}),[e]),o=s((function(){return{eventName:"webkitmouseforcewillbegin",fn:function(t){if(!t.defaultPrevented){var r=e.findClosestDraggableId(t);if(r){var n=e.findOptionsForDraggable(r);n&&(n.shouldRespectForcePress||e.canGetLock(r)&&t.preventDefault())}}}}}),[e]),a=d((function(){r.current=O(window,[o,i],{passive:!1,capture:!0})}),[o,i]),l=d((function(){"IDLE"!==t.current.type&&(t.current=jr,r.current(),a())}),[a]),u=d((function(){var e=t.current;l(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[l]),c=d((function(){var e=function(e){var t=e.cancel,r=e.completed,n=e.getPhase,i=e.setPhase;return[{eventName:"mousemove",fn:function(e){var t=e.button,r=e.clientX,o=e.clientY;if(0===t){var a={x:r,y:o},l=n();if("DRAGGING"===l.type)return e.preventDefault(),void l.actions.move(a);if("PENDING"!==l.type&&R(!1),u=l.point,c=a,Math.abs(c.x-u.x)>=5||Math.abs(c.y-u.y)>=5){var u,c;e.preventDefault();var s=l.actions.fluidLift(a);i({type:"DRAGGING",actions:s})}}}},{eventName:"mouseup",fn:function(e){var i=n();"DRAGGING"===i.type?(e.preventDefault(),i.actions.drop({shouldBlockNextClick:!0}),r()):t()}},{eventName:"mousedown",fn:function(e){"DRAGGING"===n().type&&e.preventDefault(),t()}},{eventName:"keydown",fn:function(e){if("PENDING"!==n().type)return 27===e.keyCode?(e.preventDefault(),void t()):void Ur(e);t()}},{eventName:"resize",fn:t},{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(){"PENDING"===n().type&&t()}},{eventName:"webkitmouseforcedown",fn:function(e){var r=n();"IDLE"===r.type&&R(!1),r.actions.shouldRespectForcePress()?t():e.preventDefault()}},{eventName:Wr,fn:t}]}({cancel:u,completed:l,getPhase:function(){return t.current},setPhase:function(e){t.current=e}});r.current=O(window,e,{capture:!0,passive:!1})}),[u,l]),p=d((function(e,r){"IDLE"!==t.current.type&&R(!1),t.current={type:"PENDING",point:r,actions:e},c()}),[c]);Ar((function(){return a(),function(){r.current()}}),[a])},function(e){var t=(0,n.useRef)(Vr),r=s((function(){return{eventName:"keydown",fn:function(r){if(!r.defaultPrevented&&32===r.keyCode){var n=e.findClosestDraggableId(r);if(n){var o=e.tryGetLock(n,u,{sourceEvent:r});if(o){r.preventDefault();var a=!0,l=o.snapLift();t.current(),t.current=O(window,function(e,t){function r(){t(),e.cancel()}return[{eventName:"keydown",fn:function(n){return 27===n.keyCode?(n.preventDefault(),void r()):32===n.keyCode?(n.preventDefault(),t(),void e.drop()):40===n.keyCode?(n.preventDefault(),void e.moveDown()):38===n.keyCode?(n.preventDefault(),void e.moveUp()):39===n.keyCode?(n.preventDefault(),void e.moveRight()):37===n.keyCode?(n.preventDefault(),void e.moveLeft()):void(zr[n.keyCode]?n.preventDefault():Ur(n))}},{eventName:"mousedown",fn:r},{eventName:"mouseup",fn:r},{eventName:"click",fn:r},{eventName:"touchstart",fn:r},{eventName:"resize",fn:r},{eventName:"wheel",fn:r,options:{passive:!0}},{eventName:Wr,fn:r}]}(l,u),{capture:!0,passive:!1})}}}function u(){a||R(!1),a=!1,t.current(),i()}}}}),[e]),i=d((function(){t.current=O(window,[r],{passive:!1,capture:!0})}),[r]);Ar((function(){return i(),function(){t.current()}}),[i])},function(e){var t=(0,n.useRef)(qr),r=(0,n.useRef)(P),i=d((function(){return t.current}),[]),o=d((function(e){t.current=e}),[]),a=s((function(){return{eventName:"touchstart",fn:function(t){if(!t.defaultPrevented){var n=e.findClosestDraggableId(t);if(n){var i=e.tryGetLock(n,u,{sourceEvent:t});if(i){var o=t.touches[0],a={x:o.clientX,y:o.clientY};r.current(),g(i,a)}}}}}}),[e]),l=d((function(){r.current=O(window,[a],{capture:!0,passive:!1})}),[a]),u=d((function(){var e=t.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),o(qr),r.current(),l())}),[l,o]),c=d((function(){var e=t.current;u(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()}),[u]),p=d((function(){var e={capture:!0,passive:!1},t={cancel:c,completed:u,getPhase:i},n=O(window,function(e){var t=e.cancel,r=e.completed,n=e.getPhase;return[{eventName:"touchmove",options:{capture:!1},fn:function(e){var r=n();if("DRAGGING"===r.type){r.hasMoved=!0;var i=e.touches[0],o={x:i.clientX,y:i.clientY};e.preventDefault(),r.actions.move(o)}else t()}},{eventName:"touchend",fn:function(e){var i=n();"DRAGGING"===i.type?(e.preventDefault(),i.actions.drop({shouldBlockNextClick:!0}),r()):t()}},{eventName:"touchcancel",fn:function(e){"DRAGGING"===n().type?(e.preventDefault(),t()):t()}},{eventName:"touchforcechange",fn:function(e){var r=n();"IDLE"===r.type&&R(!1);var i=e.touches[0];if(i&&i.force>=.15){var o=r.actions.shouldRespectForcePress();if("PENDING"!==r.type)return o?r.hasMoved?void e.preventDefault():void t():void e.preventDefault();o&&t()}}},{eventName:Wr,fn:t}]}(t),e),o=O(window,function(e){var t=e.cancel,r=e.getPhase;return[{eventName:"orientationchange",fn:t},{eventName:"resize",fn:t},{eventName:"contextmenu",fn:function(e){e.preventDefault()}},{eventName:"keydown",fn:function(e){"DRAGGING"===r().type?(27===e.keyCode&&e.preventDefault(),t()):t()}},{eventName:Wr,fn:t}]}(t),e);r.current=function(){n(),o()}}),[c,i,u]),f=d((function(){var e=i();"PENDING"!==e.type&&R(!1);var t=e.actions.fluidLift(e.point);o({type:"DRAGGING",actions:t,hasMoved:!1})}),[i,o]),g=d((function(e,t){"IDLE"!==i().type&&R(!1);var r=setTimeout(f,120);o({type:"PENDING",point:t,actions:e,longPressTimerId:r}),p()}),[p,i,o,f]);Ar((function(){return l(),function(){r.current();var e=i();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),o(qr))}}),[i,l,o]),Ar((function(){return O(window,[{eventName:"touchmove",fn:function(){},options:{capture:!1,passive:!1}}])}),[])}];function on(e){var t=e.contextId,r=e.store,i=e.registry,o=e.customSensors,l=e.enableDefaultSensors,u=[].concat(l?nn:[],o||[]),c=(0,n.useState)((function(){return function(){var e=null;function t(){e||R(!1),e=null}return{isClaimed:function(){return Boolean(e)},isActive:function(t){return t===e},claim:function(t){e&&R(!1);var r={abandon:t};return e=r,r},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}}()}))[0],p=d((function(e,t){e.isDragging&&!t.isDragging&&c.tryAbandon()}),[c]);Ar((function(){var e=r.getState();return r.subscribe((function(){var t=r.getState();p(e,t),e=t}))}),[c,r,p]),Ar((function(){return c.tryAbandon}),[c.tryAbandon]);for(var f=d((function(e){return rn({lockAPI:c,registry:i,store:r,draggableId:e})}),[c,i,r]),g=d((function(e,n,o){return function(e){var t=e.lockAPI,r=e.contextId,n=e.store,i=e.registry,o=e.draggableId,l=e.forceSensorStop,u=e.sourceEvent;if(!rn({lockAPI:t,store:n,registry:i,draggableId:o}))return null;var c=i.draggable.getById(o),s=function(e,t){var r="["+br.contextId+'="'+e+'"]',n=re(ne(document.querySelectorAll(r)),(function(e){return e.getAttribute(br.id)===t}));return n&&Br(n)?n:null}(r,c.descriptor.id);if(!s)return null;if(u&&!c.options.canDragInteractiveElements&&Jr(s,u))return null;var d=t.claim(l||P),p="PRE_DRAG";function f(){return c.options.shouldRespectForcePress}function g(){return t.isActive(d)}var v=function(e,t){tn({expected:e,phase:p,isLockActive:g,shouldWarn:!0})&&n.dispatch(t())}.bind(null,"DRAGGING");function m(e){function r(){t.release(),p="COMPLETED"}function i(t,i){if(void 0===i&&(i={shouldBlockNextClick:!1}),e.cleanup(),i.shouldBlockNextClick){var o=O(window,[{eventName:"click",fn:en,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(o)}r(),n.dispatch(It({reason:t}))}return"PRE_DRAG"!==p&&(r(),"PRE_DRAG"!==p&&R(!1)),n.dispatch(ut(e.liftActionArgs)),p="DRAGGING",(0,a.A)({isActive:function(){return tn({expected:"DRAGGING",phase:p,isLockActive:g,shouldWarn:!1})},shouldRespectForcePress:f,drop:function(e){return i("DROP",e)},cancel:function(e){return i("CANCEL",e)}},e.actions)}return{isActive:function(){return tn({expected:"PRE_DRAG",phase:p,isLockActive:g,shouldWarn:!1})},shouldRespectForcePress:f,fluidLift:function(e){var t=C((function(e){v((function(){return gt({client:e})}))})),r=m({liftActionArgs:{id:o,clientSelection:e,movementMode:"FLUID"},cleanup:function(){return t.cancel()},actions:{move:t}});return(0,a.A)({},r,{move:t})},snapLift:function(){var e={moveUp:function(){return v(vt)},moveRight:function(){return v(ht)},moveDown:function(){return v(mt)},moveLeft:function(){return v(bt)}};return m({liftActionArgs:{id:o,clientSelection:Kr(s),movementMode:"SNAP"},cleanup:P,actions:e})},abort:function(){tn({expected:"PRE_DRAG",phase:p,isLockActive:g,shouldWarn:!0})&&t.release()}}}({lockAPI:c,registry:i,contextId:t,store:r,draggableId:e,forceSensorStop:n,sourceEvent:o&&o.sourceEvent?o.sourceEvent:null})}),[t,c,i,r]),v=d((function(e){return function(e,t){var r=function(e,t){var r,n=t.target;if(!((r=n)instanceof Er(r).Element))return null;var i=function(e){return"["+hr.contextId+'="'+e+'"]'}(e),o=$r(n,i);return o&&Br(o)?o:null}(e,t);return r?r.getAttribute(hr.draggableId):null}(t,e)}),[t]),m=d((function(e){var t=i.draggable.findById(e);return t?t.options:null}),[i.draggable]),h=d((function(){c.isClaimed()&&(c.tryAbandon(),"IDLE"!==r.getState().phase&&r.dispatch(yt()))}),[c,r]),b=d(c.isClaimed,[c]),y=s((function(){return{canGetLock:f,tryGetLock:g,findClosestDraggableId:v,findOptionsForDraggable:m,tryReleaseLock:h,isLockClaimed:b}}),[f,g,v,m,h,b]),D=0;D<u.length;D++)u[D](y)}var an=function(e){return{onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}};function ln(e){return e.current||R(!1),e.current}function un(e){var t=e.contextId,r=e.setCallbacks,o=e.sensors,c=e.nonce,p=e.dragHandleUsageInstructions,f=(0,n.useRef)(null),g=Gr(e),v=d((function(){return an(g.current)}),[g]),m=function(e){var t=s((function(){return Lr(e)}),[e]),r=(0,n.useRef)(null);return(0,n.useEffect)((function(){var e=document.createElement("div");return r.current=e,e.id=t,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),(0,a.A)(e.style,Nr),Sr().appendChild(e),function(){setTimeout((function(){var t=Sr();t.contains(e)&&t.removeChild(e),e===r.current&&(r.current=null)}))}}),[t]),d((function(e){var t=r.current;t&&(t.textContent=e)}),[])}(t),h=function(e){var t=e.contextId,r=e.text,i=Mr("hidden-text",{separator:"-"}),o=s((function(){return"rbd-hidden-text-"+(e={contextId:t,uniqueId:i}).contextId+"-"+e.uniqueId;var e}),[i,t]);return(0,n.useEffect)((function(){var e=document.createElement("div");return e.id=o,e.textContent=r,e.style.display="none",Sr().appendChild(e),function(){var t=Sr();t.contains(e)&&t.removeChild(e)}}),[o,r]),o}({contextId:t,text:p}),b=function(e,t){var r=s((function(){return xr(e)}),[e]),i=(0,n.useRef)(null),o=(0,n.useRef)(null),a=d((0,A.A)((function(e){var t=o.current;t||R(!1),t.textContent=e})),[]),l=d((function(e){var t=i.current;t||R(!1),t.textContent=e}),[]);Ar((function(){(i.current||o.current)&&R(!1);var n=wr(t),u=wr(t);return i.current=n,o.current=u,n.setAttribute(mr+"-always",e),u.setAttribute(mr+"-dynamic",e),Cr().appendChild(n),Cr().appendChild(u),l(r.always),a(r.resting),function(){var e=function(e){var t=e.current;t||R(!1),Cr().removeChild(t),e.current=null};e(i),e(o)}}),[t,l,a,r.always,r.resting,e]);var u=d((function(){return a(r.dragging)}),[a,r.dragging]),c=d((function(e){a("DROP"!==e?r.userCancel:r.dropAnimating)}),[a,r.dropAnimating,r.userCancel]),p=d((function(){o.current&&a(r.resting)}),[a,r.resting]);return s((function(){return{dragging:u,dropping:c,resting:p}}),[u,c,p])}(t,c),y=d((function(e){ln(f).dispatch(e)}),[]),D=s((function(){return(0,l.zH)({publishWhileDragging:ct,updateDroppableScroll:dt,updateDroppableIsEnabled:pt,updateDroppableIsCombineEnabled:ft,collectionStarting:st},y)}),[y]),I=function(){var e=s(Pr,[]);return(0,n.useEffect)((function(){return function(){requestAnimationFrame(e.clean)}}),[e]),e}(),x=s((function(){return Kt(I,D)}),[I,D]),C=s((function(){return vr((0,a.A)({scrollWindow:Zt,scrollDroppable:x.scrollDroppable},(0,l.zH)({move:gt},y)))}),[x.scrollDroppable,y]),w=function(e){var t=(0,n.useRef)({}),r=(0,n.useRef)(null),i=(0,n.useRef)(null),o=(0,n.useRef)(!1),a=d((function(e,r){var n={id:e,focus:r};return t.current[e]=n,function(){var r=t.current;r[e]!==n&&delete r[e]}}),[]),l=d((function(t){var r=function(e,t){var r="["+hr.contextId+'="'+e+'"]',n=ne(document.querySelectorAll(r));if(!n.length)return null;var i=re(n,(function(e){return e.getAttribute(hr.draggableId)===t}));return i&&Br(i)?i:null}(e,t);r&&r!==document.activeElement&&r.focus()}),[e]),u=d((function(e,t){r.current===e&&(r.current=t)}),[]),c=d((function(){i.current||o.current&&(i.current=requestAnimationFrame((function(){i.current=null;var e=r.current;e&&l(e)})))}),[l]),p=d((function(e){r.current=null;var t=document.activeElement;t&&t.getAttribute(hr.draggableId)===e&&(r.current=e)}),[]);return Ar((function(){return o.current=!0,function(){o.current=!1;var e=i.current;e&&cancelAnimationFrame(e)}}),[]),s((function(){return{register:a,tryRecordFocus:p,tryRestoreFocusRecorded:c,tryShiftRecord:u}}),[a,p,c,u])}(t),E=s((function(){return jt({announce:m,autoScroller:C,dimensionMarshal:x,focusMarshal:w,getResponders:v,styleMarshal:b})}),[m,C,x,w,v,b]);f.current=E;var B=d((function(){var e=ln(f);"IDLE"!==e.getState().phase&&e.dispatch(yt())}),[]),P=d((function(){var e=ln(f).getState();return e.isDragging||"DROP_ANIMATING"===e.phase}),[]);r(s((function(){return{isDragging:P,tryAbort:B}}),[P,B]));var O=d((function(e){return Qt(ln(f).getState(),e)}),[]),S=d((function(){return qe(ln(f).getState())}),[]),N=s((function(){return{marshal:x,focus:w,contextId:t,canLift:O,isMovementAllowed:S,dragHandleUsageInstructionsId:h,registry:I}}),[t,x,h,w,O,S,I]);return on({contextId:t,store:E,registry:I,customSensors:o,enableDefaultSensors:!1!==e.enableDefaultSensors}),(0,n.useEffect)((function(){return B}),[B]),i().createElement(_r.Provider,{value:N},i().createElement(u.Kq,{context:Or,store:E},e.children))}var cn=0;function sn(e){var t=s((function(){return""+cn++}),[]),r=e.dragHandleUsageInstructions||F.dragHandleUsageInstructions;return i().createElement(T,null,(function(n){return i().createElement(un,{nonce:e.nonce,contextId:t,setCallbacks:n,dragHandleUsageInstructions:r,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd},e.children)}))}var dn=function(e){return function(t){return e===t}},pn=dn("scroll"),fn=dn("auto"),gn=(dn("visible"),function(e,t){return t(e.overflowX)||t(e.overflowY)}),vn=function e(t){return null==t||t===document.body||t===document.documentElement?null:function(e){var t=window.getComputedStyle(e),r={overflowX:t.overflowX,overflowY:t.overflowY};return gn(r,pn)||gn(r,fn)}(t)?t:e(t.parentElement)},mn=function(e){return{x:e.scrollLeft,y:e.scrollTop}},hn=function e(t){return!!t&&("fixed"===window.getComputedStyle(t).position||e(t.parentElement))},bn={passive:!1},yn={passive:!0},Dn=function(e){return e.shouldPublishImmediately?bn:yn};function In(e){var t=(0,n.useContext)(e);return t||R(!1),t}var xn=function(e){return e&&e.env.closestScrollable||null};function An(){}var Cn={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},wn=i().memo((function(e){var t=(0,n.useRef)(null),r=d((function(){t.current&&(clearTimeout(t.current),t.current=null)}),[]),o=e.animate,a=e.onTransitionEnd,l=e.onClose,u=e.contextId,c=(0,n.useState)("open"===e.animate),s=c[0],p=c[1];(0,n.useEffect)((function(){return s?"open"!==o?(r(),p(!1),An):t.current?An:(t.current=setTimeout((function(){t.current=null,p(!1)})),r):An}),[o,s,r]);var f=d((function(e){"height"===e.propertyName&&(a(),"close"===o&&l())}),[o,l,a]),g=function(e){var t=e.isAnimatingOpenOnMount,r=e.placeholder,n=e.animate,i=function(e){var t=e.placeholder;return e.isAnimatingOpenOnMount||"close"===e.animate?Cn:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin}}({isAnimatingOpenOnMount:t,placeholder:r,animate:n});return{display:r.display,boxSizing:"border-box",width:i.width,height:i.height,marginTop:i.margin.top,marginRight:i.margin.right,marginBottom:i.margin.bottom,marginLeft:i.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==n?Bt.placeholder:null}}({isAnimatingOpenOnMount:s,animate:e.animate,placeholder:e.placeholder});return i().createElement(e.placeholder.tagName,{style:g,"data-rbd-placeholder-context-id":u,onTransitionEnd:f,ref:e.innerRef})})),En=i().createContext(null),Bn=function(e){function t(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return(t=e.call.apply(e,[this].concat(n))||this).state={isVisible:Boolean(t.props.on),data:t.props.on,animate:t.props.shouldAnimate&&t.props.on?"open":"none"},t.onClose=function(){"close"===t.state.animate&&t.setState({isVisible:!1})},t}return(0,o.A)(t,e),t.getDerivedStateFromProps=function(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:Boolean(e.on),data:e.on,animate:"none"}},t.prototype.render=function(){if(!this.state.isVisible)return null;var e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)},t}(i().PureComponent),Pn=function(e,t){return t?Bt.drop(t.duration):e?Bt.snap:Bt.fluid},On=function(e,t){return e?t?At:Ct:null};function Sn(e){return"DRAGGING"===e.type?function(e){var t=e.dimension.client,r=e.offset,n=e.combineWith,i=e.dropping,o=Boolean(n),a=function(e){return null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode}(e),l=Boolean(i),u=l?function(e,t){var r=Pt(e);return r?t?r+" scale("+wt+")":r:null}(r,o):Ot(r);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:Pn(a,i),transform:u,opacity:On(o,l),zIndex:l?4500:5e3,pointerEvents:"none"}}(e):{transform:Ot((t=e).offset),transition:t.shouldAnimateDisplacement?null:"none"};var t}function Nn(e){e.preventDefault()}var Ln=function(e,t){return e===t},Rn=function(e){var t=e.combine,r=e.destination;return r?r.droppableId:t?t.droppableId:null};function Tn(e){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}var Mn={mapped:{type:"SECONDARY",offset:H,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:Tn(null)}},Gn={dropAnimationFinished:function(){return{type:"DROP_ANIMATION_FINISHED",payload:null}}},kn=(0,u.Ng)((function(){var e,t,r,n=(e=(0,A.A)((function(e,t){return{x:e,y:t}})),t=(0,A.A)((function(e,t,r,n,i){return{isDragging:!0,isClone:t,isDropAnimating:Boolean(i),dropAnimation:i,mode:e,draggingOver:r,combineWith:n,combineTargetFor:null}})),r=(0,A.A)((function(e,r,n,i,o,a,l){return{mapped:{type:"DRAGGING",dropping:null,draggingOver:o,combineWith:a,mode:r,offset:e,dimension:n,forceShouldAnimate:l,snapshot:t(r,i,o,a,null)}}})),function(n,i){if(n.isDragging){if(n.critical.draggable.id!==i.draggableId)return null;var o=n.current.client.offset,a=n.dimensions.draggables[i.draggableId],l=Ve(n.impact),u=(s=n.impact).at&&"COMBINE"===s.at.type?s.at.combine.draggableId:null,c=n.forceShouldAnimate;return r(e(o.x,o.y),n.movementMode,a,i.isClone,l,u,c)}var s;if("DROP_ANIMATING"===n.phase){var d=n.completed;if(d.result.draggableId!==i.draggableId)return null;var p=i.isClone,f=n.dimensions.draggables[i.draggableId],g=d.result,v=g.mode,m=Rn(g),h=function(e){return e.combine?e.combine.draggableId:null}(g),b={duration:n.dropDuration,curve:xt,moveTo:n.newHomeClientOffset,opacity:h?At:null,scale:h?wt:null};return{mapped:{type:"DRAGGING",offset:n.newHomeClientOffset,dimension:f,dropping:b,draggingOver:m,combineWith:h,mode:v,forceShouldAnimate:null,snapshot:t(v,p,m,h,b)}}}return null}),i=function(){var e=(0,A.A)((function(e,t){return{x:e,y:t}})),t=(0,A.A)(Tn),r=(0,A.A)((function(e,r,n){return void 0===r&&(r=null),{mapped:{type:"SECONDARY",offset:e,combineTargetFor:r,shouldAnimateDisplacement:n,snapshot:t(r)}}})),n=function(e){return e?r(H,e,!0):null},i=function(t,i,o,a){var l=o.displaced.visible[t],u=Boolean(a.inVirtualList&&a.effected[t]),c=se(o),s=c&&c.draggableId===t?i:null;if(!l){if(!u)return n(s);if(o.displaced.invisible[t])return null;var d=V(a.displacedBy.point),p=e(d.x,d.y);return r(p,s,!0)}if(u)return n(s);var f=o.displacedBy.point,g=e(f.x,f.y);return r(g,s,l.shouldAnimate)};return function(e,t){if(e.isDragging)return e.critical.draggable.id===t.draggableId?null:i(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){var r=e.completed;return r.result.draggableId===t.draggableId?null:i(t.draggableId,r.result.draggableId,r.impact,r.afterCritical)}return null}}();return function(e,t){return n(e,t)||i(e,t)||Mn}}),Gn,null,{context:Or,pure:!0,areStatePropsEqual:Ln})((function(e){var t=(0,n.useRef)(null),r=d((function(e){t.current=e}),[]),i=d((function(){return t.current}),[]),o=In(_r),a=o.contextId,l=o.dragHandleUsageInstructionsId,u=o.registry,c=In(En),p=c.type,f=c.droppableId,g=s((function(){return{id:e.draggableId,index:e.index,type:p,droppableId:f}}),[e.draggableId,e.index,p,f]),v=e.children,m=e.draggableId,h=e.isEnabled,b=e.shouldRespectForcePress,y=e.canDragInteractiveElements,x=e.isClone,A=e.mapped,C=e.dropAnimationFinished;x||function(e){var t=Mr("draggable"),r=e.descriptor,i=e.registry,o=e.getDraggableRef,a=e.canDragInteractiveElements,l=e.shouldRespectForcePress,u=e.isEnabled,c=s((function(){return{canDragInteractiveElements:a,shouldRespectForcePress:l,isEnabled:u}}),[a,u,l]),p=d((function(e){var t=o();return t||R(!1),function(e,t,r){void 0===r&&(r=H);var n=window.getComputedStyle(t),i=t.getBoundingClientRect(),o=I(i,n),a=D(o,r);return{descriptor:e,placeholder:{client:o,tagName:t.tagName.toLowerCase(),display:n.display},displaceBy:{x:o.marginBox.width,y:o.marginBox.height},client:o,page:a}}(r,t,e)}),[r,o]),f=s((function(){return{uniqueId:t,descriptor:r,options:c,getDimension:p}}),[r,p,c,t]),g=(0,n.useRef)(f),v=(0,n.useRef)(!0);Ar((function(){return i.draggable.register(g.current),function(){return i.draggable.unregister(g.current)}}),[i.draggable]),Ar((function(){if(v.current)v.current=!1;else{var e=g.current;g.current=f,i.draggable.update(f,e)}}),[f,i.draggable])}(s((function(){return{descriptor:g,registry:u,getDraggableRef:i,canDragInteractiveElements:y,shouldRespectForcePress:b,isEnabled:h}}),[g,u,i,y,b,h]));var w=s((function(){return h?{tabIndex:0,role:"button","aria-describedby":l,"data-rbd-drag-handle-draggable-id":m,"data-rbd-drag-handle-context-id":a,draggable:!1,onDragStart:Nn}:null}),[a,l,m,h]),E=d((function(e){"DRAGGING"===A.type&&A.dropping&&"transform"===e.propertyName&&C()}),[C,A]),B=s((function(){var e=Sn(A),t="DRAGGING"===A.type&&A.dropping?E:null;return{innerRef:r,draggableProps:{"data-rbd-draggable-context-id":a,"data-rbd-draggable-id":m,style:e,onTransitionEnd:t},dragHandleProps:w}}),[a,w,m,A,E,r]),P=s((function(){return{draggableId:g.id,type:g.type,source:{index:g.index,droppableId:g.droppableId}}}),[g.droppableId,g.id,g.index,g.type]);return v(B,A.snapshot,P)}));function Fn(e){return In(En).isUsingCloneFor!==e.draggableId||e.isClone?i().createElement(kn,e):null}function Hn(e){var t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,r=Boolean(e.disableInteractiveElementBlocking),n=Boolean(e.shouldRespectForcePress);return i().createElement(Fn,(0,a.A)({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:r,shouldRespectForcePress:n}))}var Un=function(e,t){return e===t.droppable.type},Wn=function(e,t){return t.draggables[e.draggable.id]},jn={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||R(!1),document.body}},Vn=(0,u.Ng)((function(){var e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t=(0,a.A)({},e,{shouldAnimatePlaceholder:!1}),r=(0,A.A)((function(e){return{draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}}})),n=(0,A.A)((function(n,i,o,a,l,u){var c=l.descriptor.id;if(l.descriptor.droppableId===n){var s=u?{render:u,dragging:r(l.descriptor)}:null,d={isDraggingOver:o,draggingOverWith:o?c:null,draggingFromThisWith:c,isUsingPlaceholder:!0};return{placeholder:l.placeholder,shouldAnimatePlaceholder:!1,snapshot:d,useClone:s}}if(!i)return t;if(!a)return e;var p={isDraggingOver:o,draggingOverWith:c,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:l.placeholder,shouldAnimatePlaceholder:!0,snapshot:p,useClone:null}}));return function(r,i){var o=i.droppableId,a=i.type,l=!i.isDropDisabled,u=i.renderClone;if(r.isDragging){var c=r.critical;if(!Un(a,c))return t;var s=Wn(c,r.dimensions),d=Ve(r.impact)===o;return n(o,l,d,d,s,u)}if("DROP_ANIMATING"===r.phase){var p=r.completed;if(!Un(a,p.critical))return t;var f=Wn(p.critical,r.dimensions);return n(o,l,Rn(p.result)===o,Ve(p.impact)===o,f,u)}if("IDLE"===r.phase&&r.completed&&!r.shouldFlush){var g=r.completed;if(!Un(a,g.critical))return t;var v=Ve(g.impact)===o,m=Boolean(g.impact.at&&"COMBINE"===g.impact.at.type),h=g.critical.droppable.id===o;return v?m?e:t:h?e:t}return t}}),{updateViewportMaxScroll:function(e){return{type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e}}},null,{context:Or,pure:!0,areStatePropsEqual:Ln})((function(e){var t=(0,n.useContext)(_r);t||R(!1);var r=t.contextId,o=t.isMovementAllowed,a=(0,n.useRef)(null),l=(0,n.useRef)(null),u=e.children,c=e.droppableId,p=e.type,f=e.mode,v=e.direction,m=e.ignoreContainerClipping,b=e.isDropDisabled,y=e.isCombineEnabled,I=e.snapshot,w=e.useClone,B=e.updateViewportMaxScroll,P=e.getContainerForClone,O=d((function(){return a.current}),[]),S=d((function(e){a.current=e}),[]),N=(d((function(){return l.current}),[]),d((function(e){l.current=e}),[])),L=d((function(){o()&&B({maxScroll:qt()})}),[o,B]);!function(e){var t=(0,n.useRef)(null),r=In(_r),i=Mr("droppable"),o=r.registry,a=r.marshal,l=Gr(e),u=s((function(){return{id:e.droppableId,type:e.type,mode:e.mode}}),[e.droppableId,e.mode,e.type]),c=(0,n.useRef)(u),p=s((function(){return(0,A.A)((function(e,r){t.current||R(!1);var n={x:e,y:r};a.updateDroppableScroll(u.id,n)}))}),[u.id,a]),f=d((function(){var e=t.current;return e&&e.env.closestScrollable?mn(e.env.closestScrollable):H}),[]),v=d((function(){var e=f();p(e.x,e.y)}),[f,p]),m=s((function(){return C(v)}),[v]),b=d((function(){var e=t.current,r=xn(e);e&&r||R(!1),e.scrollOptions.shouldPublishImmediately?v():m()}),[m,v]),y=d((function(e,n){t.current&&R(!1);var i=l.current,o=i.getDroppableRef();o||R(!1);var a=function(e){return{closestScrollable:vn(e),isFixedOnPage:hn(e)}}(o),c={ref:o,descriptor:u,env:a,scrollOptions:n};t.current=c;var s=function(e){var t=e.ref,r=e.descriptor,n=e.env,i=e.windowScroll,o=e.direction,a=e.isDropDisabled,l=e.isCombineEnabled,u=e.shouldClipSubject,c=n.closestScrollable,s=function(e,t){var r=x(e);if(!t)return r;if(e!==t)return r;var n=r.paddingBox.top-t.scrollTop,i=r.paddingBox.left-t.scrollLeft,o=n+t.scrollHeight,a=i+t.scrollWidth,l=g({top:n,right:a,bottom:o,left:i},r.border);return h({borderBox:l,margin:r.margin,border:r.border,padding:r.padding})}(t,c),d=D(s,i),p=function(){if(!c)return null;var e=x(c),t={scrollHeight:c.scrollHeight,scrollWidth:c.scrollWidth};return{client:e,page:D(e,i),scroll:mn(c),scrollSize:t,shouldClipSubject:u}}(),f=function(e){var t=e.descriptor,r=e.isEnabled,n=e.isCombineEnabled,i=e.isFixedOnPage,o=e.direction,a=e.client,l=e.page,u=e.closest,c=function(){if(!u)return null;var e=u.scrollSize,t=u.client,r=Vt({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:u.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:u.shouldClipSubject,scroll:{initial:u.scroll,current:u.scroll,max:r,diff:{value:H,displacement:H}}}}(),s="vertical"===o?ye:De;return{descriptor:t,isCombineEnabled:n,isFixedOnPage:i,axis:s,isEnabled:r,client:a,page:l,frame:c,subject:Z({page:l,withPlaceholder:null,axis:s,frame:c})}}({descriptor:r,isEnabled:!a,isCombineEnabled:l,isFixedOnPage:n.isFixedOnPage,direction:o,client:s,page:d,closest:p});return f}({ref:o,descriptor:u,env:a,windowScroll:e,direction:i.direction,isDropDisabled:i.isDropDisabled,isCombineEnabled:i.isCombineEnabled,shouldClipSubject:!i.ignoreContainerClipping}),d=a.closestScrollable;return d&&(d.setAttribute(Dr.contextId,r.contextId),d.addEventListener("scroll",b,Dn(c.scrollOptions))),s}),[r.contextId,u,b,l]),I=d((function(){var e=t.current,r=xn(e);return e&&r||R(!1),mn(r)}),[]),w=d((function(){var e=t.current;e||R(!1);var r=xn(e);t.current=null,r&&(m.cancel(),r.removeAttribute(Dr.contextId),r.removeEventListener("scroll",b,Dn(e.scrollOptions)))}),[b,m]),E=d((function(e){var r=t.current;r||R(!1);var n=xn(r);n||R(!1),n.scrollTop+=e.y,n.scrollLeft+=e.x}),[]),B=s((function(){return{getDimensionAndWatchScroll:y,getScrollWhileDragging:I,dragStopped:w,scroll:E}}),[w,y,I,E]),P=s((function(){return{uniqueId:i,descriptor:u,callbacks:B}}),[B,u,i]);Ar((function(){return c.current=P.descriptor,o.droppable.register(P),function(){t.current&&w(),o.droppable.unregister(P)}}),[B,u,w,P,a,o.droppable]),Ar((function(){t.current&&a.updateDroppableIsEnabled(c.current.id,!e.isDropDisabled)}),[e.isDropDisabled,a]),Ar((function(){t.current&&a.updateDroppableIsCombineEnabled(c.current.id,e.isCombineEnabled)}),[e.isCombineEnabled,a])}({droppableId:c,type:p,mode:f,direction:v,isDropDisabled:b,isCombineEnabled:y,ignoreContainerClipping:m,getDroppableRef:O});var T=i().createElement(Bn,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},(function(e){var t=e.onClose,n=e.data,o=e.animate;return i().createElement(wn,{placeholder:n,onClose:t,innerRef:N,animate:o,contextId:r,onTransitionEnd:L})})),M=s((function(){return{innerRef:S,placeholder:T,droppableProps:{"data-rbd-droppable-id":c,"data-rbd-droppable-context-id":r}}}),[r,c,T,S]),_=w?w.dragging.draggableId:null,G=s((function(){return{droppableId:c,type:p,isUsingCloneFor:_}}),[c,_,p]);return i().createElement(En.Provider,{value:G},u(M,I),function(){if(!w)return null;var e=w.dragging,t=w.render,r=i().createElement(Fn,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(function(r,n){return t(r,n,e)}));return E().createPortal(r,P())}())}));Vn.defaultProps=jn},93503:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>D});var n,i,o,a=r(58168),l=r(45458),u=r(23029),c=r(92901),s=r(85501),d=r(70246),p=r(51609),f=r.n(p),g=r(84125),v=r(28294),m=(r(79132),r(75795),function(){var e=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0,r=String(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").toLowerCase(),n=String(t.getOptionValue(e)).toLowerCase(),i=String(t.getOptionLabel(e)).toLowerCase();return n===r||i===r}),h={formatCreateLabel:function(e){return'Create "'.concat(e,'"')},isValidNewOption:function(e,t,r,n){return!(!e||t.some((function(t){return m(e,t,n)}))||r.some((function(t){return m(e,t,n)})))},getNewOptionData:function(e,t){return{label:t,value:e,__isNew__:!0}},getOptionValue:g.g,getOptionLabel:g.a},b=(0,d.a)({allowCreateWhileLoading:!1,createOptionPosition:"last"},h),y=(n=g.S,o=i=function(e){(0,s.A)(r,e);var t=(0,d._)(r);function r(e){var n;(0,u.A)(this,r),(n=t.call(this,e)).select=void 0,n.onChange=function(e,t){var r=n.props,i=r.getNewOptionData,o=r.inputValue,a=r.isMulti,u=r.onChange,c=r.onCreateOption,s=r.value,p=r.name;if("select-option"!==t.action)return u(e,t);var f=n.state.newOption,g=Array.isArray(e)?e:[e];if(g[g.length-1]!==f)u(e,t);else if(c)c(o);else{var v=i(o,o),m={action:"create-option",name:p,option:v};u(a?[].concat((0,l.A)((0,d.E)(s)),[v]):v,m)}};var i=e.options||[];return n.state={newOption:void 0,options:i},n}return(0,c.A)(r,[{key:"focus",value:function(){this.select.focus()}},{key:"blur",value:function(){this.select.blur()}},{key:"render",value:function(){var e=this,t=this.state.options;return f().createElement(n,(0,a.A)({},this.props,{ref:function(t){e.select=t},options:t,onChange:this.onChange}))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var r=e.allowCreateWhileLoading,n=e.createOptionPosition,i=e.formatCreateLabel,o=e.getNewOptionData,a=e.inputValue,u=e.isLoading,c=e.isValidNewOption,s=e.value,p=e.getOptionValue,f=e.getOptionLabel,g=e.options||[],v=t.newOption;return{newOption:v=c(a,(0,d.E)(s),g,{getOptionValue:p,getOptionLabel:f})?o(a,i(a)):void 0,options:!r&&u||!v?g:"first"===n?[v].concat((0,l.A)(g)):[].concat((0,l.A)(g),[v])}}}]),r}(p.Component),i.defaultProps=b,o);const D=(0,v.m)(y)}}]);