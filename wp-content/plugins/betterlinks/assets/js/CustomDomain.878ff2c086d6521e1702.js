"use strict";(globalThis.webpackChunkbetterlinks=globalThis.webpackChunkbetterlinks||[]).push([[358],{58987:(e,t,n)=>{n.d(t,{A:()=>m});var l=n(64467),r=n(80045),a=n(51609),o=(n(27723),n(40150)),i=n(5556),c=["title","is_pro"];function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}var u={title:n.n(i)().string};function m(e){var t=e.title,n=void 0===t?"":t,i=e.is_pro,u=void 0!==i&&i,m=(0,r.A)(e,c);return(0,a.createElement)("div",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,l.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({className:"btl-role-item btl-form-group"},m),(0,a.createElement)("label",{className:"btl-form-label"},n,u&&(0,a.createElement)(o.A,null)),(0,a.createElement)("div",{className:"link-options__body"},(0,a.createElement)("label",{className:"btl-checkbox-field"},(0,a.createElement)("input",{type:"checkbox",className:"btl-check",disabled:!0}),(0,a.createElement)("span",{className:"text"}))))}m.propTypes=u},16560:(e,t,n)=>{n.d(t,{A:()=>C});var l=n(3453),r=n(80045),a=n(51609),o=n.n(a),i=n(49924),c=n(68238),s=n(27723),u=n(19735),m=n(74086),d=n(67154),p=n(58766),b=n(7400),v=n(20312),f=n.n(v),_=n(46005),y=[{label:(0,s.__)("Delete All","betterlinks"),value:!1},{label:(0,s.__)("Delete clicks older than 30 days","betterlinks"),value:30},{label:(0,s.__)("Delete clicks older than 90 days","betterlinks"),value:90}];const h=(0,i.Ng)((function(){return{}}),(function(e){return{fetchCustomClicksData:(0,c.zH)(p.lC,e),dispatch_new_links_data:(0,c.zH)(b.jT,e)}}))((function(e){var t=e.fetchCustomClicksData,n=e.dispatch_new_links_data,r=(e.propsForAnalytics||{}).customDateFilter,o=(0,a.useState)(0),i=(0,l.A)(o,2),c=i[0],s=i[1],m=(0,a.useState)(!1),d=(0,l.A)(m,2),p=d[0],b=d[1],v=(0,a.useState)(0),h=(0,l.A)(v,2),g=h[0],E=h[1],k=(0,a.useState)("reset_modal_step_1"),w=(0,l.A)(k,2),O=w[0],N=w[1],C=(0,a.useState)(y[0]),P=(0,l.A)(C,2),j=P[0],x=P[1];(0,a.useEffect)((function(){var e,t;return p?null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.add("betterlinks-delete-clicks-modal-popup-opened"):null===(t=document)||void 0===t||null===(t=t.body)||void 0===t||null===(t=t.classList)||void 0===t||t.remove("betterlinks-delete-clicks-modal-popup-opened"),function(){var e;null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.remove("betterlinks-delete-clicks-modal-popup-opened")}}),[p]);var A=function(){clearTimeout(c),N("reset_modal_step_1"),b(!1),x(y[0])};return(0,a.createElement)("div",{className:"btl-analytic-reset-wrapeer betterlinks"},(0,a.createElement)("button",{className:"button-primary btl-reset-analytics-initial-button",onClick:function(){b(!0),N("reset_modal_step_1")}},"Reset"),(0,a.createElement)(f(),{isOpen:p,onRequestClose:A,ariaHideApp:!1},(0,a.createElement)("div",{className:"btl-reset-modal-popup-wrapper "},(0,a.createElement)("span",{className:"btl-close-modal",onClick:A},(0,a.createElement)("i",{className:"btl btl-cancel"})),"reset_modal_step_1"===O&&(0,a.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-1 betterlinks-body"},(0,a.createElement)("h2",null,"Pick the range of BetterLinks Analytics that you want to reset."),(0,a.createElement)("div",{className:"select_apply"},(0,a.createElement)(_.Ay,{className:"btl-modal-select--full ",classNamePrefix:"btl-react-select",onChange:function(e){x(e)},options:y,value:j,isMulti:!1}),(0,a.createElement)("button",{className:"button-primary btl-btn-reset-analytics btl-btn-reset-apply-1",onClick:function(){N("reset_modal_step_2")}},"Apply"))),"reset_modal_step_2"===O&&(0,a.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-2 betterlinks-body"},(0,a.createElement)("h2",null,"This Action Cannot be undone. Are you sure you want to continue?"),(0,a.createElement)("h4",null,"Clicking ",(0,a.createElement)("span",{style:{fontWeight:700}},"Reset Clicks")," will permanently delete the clicks data from database and it cannot be restored again.",(0,a.createElement)("span",{style:{display:"Block"}},"Click 'cancel' to abort.")),(0,a.createElement)("div",{className:"btl-btn-reset-popup-step-2-buttons"},(0,a.createElement)("button",{className:"button-primary btl-btn-reset-apply-2",onClick:function(){if(r){var e=(0,u.Yq)(r[0].startDate,"yyyy-mm-dd"),l=(0,u.Yq)(r[0].endDate,"yyyy-mm-dd");N("deleting");var a=(null==j?void 0:j.value)||!1;(0,u.Xq)(a,e,l).then((function(e){var l,r,a,o,i=setTimeout((function(){A()}),3e3);s(i),null!=e&&null!==(l=e.data)&&void 0!==l&&l.success?(E(null==e||null===(r=e.data)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.count),t({data:null==e||null===(a=e.data)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.new_clicks_data}),n({data:null==e||null===(o=e.data)||void 0===o||null===(o=o.data)||void 0===o?void 0:o.new_links_data}),N("success")):N("failed")})).catch((function(e){console.log("---caught error on DeleteClicks",{err:e});var t=setTimeout((function(){A()}),3e3);s(t)}))}}},"Reset Clicks"),(0,a.createElement)("button",{className:"button-primary btl-btn-reset-cancel",onClick:function(){return N("reset_modal_step_1")}},"Cancel"))),"deleting"===O&&(0,a.createElement)("h2",null,"Deleting..."),"success"===O&&0!==g&&(0,a.createElement)("h2",null,"Success!!! ",(0,a.createElement)("span",{className:"success_delete_count"},g)," clicks record Deleted!!!"),"success"===O&&0===g&&(0,a.createElement)("h2",null,!1===(null==j?void 0:j.value)&&"You don't have any clicks data",30===(null==j?void 0:j.value)&&"You don't have clicks data older than 30 days",90===(null==j?void 0:j.value)&&"You don't have clicks data older than 90 days"),"failed"===O&&(0,a.createElement)("h2",null,"Failed!!"))))}));var g=n(5556),E=n.n(g),k=n(40150),w=["is_pro","render"],O={label:E().string,render:E().func},N=function(e){var t=e.is_pro,n=void 0!==t&&t,i=e.render,c=void 0===i?function(){}:i,m=(0,r.A)(e,w),d=m.propsForAnalytics,p=m.activity.darkMode,b=(0,a.useState)(p),v=(0,l.A)(b,2),f=v[0],_=v[1];(0,a.useEffect)((function(){p?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode")}),[]);var y=betterLinksQuery.get("page"),g=m.favouriteSort.sortByFav;return(0,a.createElement)("div",{className:"topbar"},(0,a.createElement)("div",{className:"topbar__logo_container"},(0,a.createElement)("div",{className:"topbar__logo"},(0,a.createElement)("img",{src:u.hq+"assets/images/logo-large".concat(f?"-white":"",".svg"),alt:"logo"}),(0,a.createElement)("span",{className:"topbar__logo__text"},m.label),n&&(0,a.createElement)(k.A,null)),c()),(0,a.createElement)("div",{className:"topbar-inner"},"betterlinks"===y&&(0,a.createElement)(o().Fragment,null,(0,a.createElement)("div",{className:"btl-view-control"},(0,a.createElement)("button",{title:(0,s.__)("Favorite Links","betterlinks"),className:"btl-link-view-toggler btl-sortby-fav ".concat(g?"active":""),onClick:function(){return m.sortFavourite(!g)}},(0,a.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"favorite-svg",viewBox:"0 0 512 512",xmlSpace:"preserve"},(0,a.createElement)("path",{className:"fav-icon-svg-path",d:"M392.2 317.5c-3 2.9-4.4 7.1-3.7 11.3L414 477.4c1.2 7-3.5 13.6-10.5 14.9-2.8.5-5.6 0-8.1-1.3L262 420.9c-3.7-2-8.2-2-12 0L116.6 491c-3.1 1.7-6.8 1.9-10.1.8-6-2.1-9.5-8.1-8.5-14.4l25.4-148.5c.7-4.2-.7-8.4-3.7-11.4L11.9 212.4c-5.1-5-5.2-13.1-.2-18.2 2-2 4.6-3.3 7.3-3.7l149.1-21.7c4.2-.6 7.8-3.2 9.7-7l66.7-135c2.6-5.3 8.4-8.1 14.2-6.9 3.9.7 7.2 3.3 8.9 6.9l66.7 135c1.9 3.8 5.5 6.4 9.7 7l149 21.6c7 1 11.9 7.6 10.9 14.6-.4 2.7-1.7 5.3-3.7 7.2l-108 105.3z"}))),(0,a.createElement)("button",{title:(0,s.__)("List View","betterlinks"),className:"btl-link-view-toggler ".concat("list"==m.activity.linksView?"active":""),onClick:function(){return m.linksView("list")}},(0,a.createElement)("i",{className:"btl btl-list"})),(0,a.createElement)("button",{title:(0,s.__)("Grid View","betterlinks"),className:"btl-link-view-toggler ".concat("grid"==m.activity.linksView?"active":""),onClick:function(){return m.linksView("grid")}},(0,a.createElement)("i",{className:"btl btl-grid"})))),(null==d?void 0:d.isResetAnalytics)&&(0,a.createElement)(h,{propsForAnalytics:d}),(0,a.createElement)("label",{className:"theme-mood-button",htmlFor:"theme-mood",title:(0,s.__)("Theme Mode","betterlinks")},(0,a.createElement)("input",{type:"checkbox",name:"theme-mood",id:"theme-mood",value:f,onChange:function(){return function(e){e?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode"),m.update_theme_mode(e),_(e)}(!f)},checked:f}),(0,a.createElement)("span",{className:"theme-mood"},(0,a.createElement)("span",{className:"icon"},(0,a.createElement)("i",{className:"btl btl-sun"})),(0,a.createElement)("span",{className:"icon"},(0,a.createElement)("i",{className:"btl btl-moon"}))))))};N.propTypes=O;const C=(0,i.Ng)((function(e){return{activity:e.activity,favouriteSort:e.favouriteSort}}),(function(e){return{linksView:(0,c.zH)(m.xb,e),sortFavourite:(0,c.zH)(d.sortFavourite,e),update_theme_mode:(0,c.zH)(m.Q7,e)}}))(N)},75740:(e,t,n)=>{n.r(t),n.d(t,{default:()=>R});var l,r,a=n(51609),o=n(27723),i=n(64467),c=n(3453),s=n(80702),u=n(2078),m=n(4949),d=n(19735),p=n(10138),b=n(58987),v=n(68238),f=n(49924),_=n(93832),y=n(7420),h=n.n(y),g=n(19555);function E(){return E=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},E.apply(null,arguments)}var k=function(e){return a.createElement("svg",E({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),l||(l=a.createElement("g",{stroke:"#19285D",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#copy-icon-1_svg__a)"},a.createElement("path",{d:"M8 10a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-8a2 2 0 0 1-2-2z"}),a.createElement("path",{d:"M16 8V6a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h2"}))),r||(r=a.createElement("defs",null,a.createElement("clipPath",{id:"copy-icon-1_svg__a"},a.createElement("path",{fill:"#fff",d:"M0 0h24v24H0z"})))))};const w=function(e){var t=e.enableCopy,n=void 0===t||t,l=e.code,r=e.copyCode,o=(0,a.useState)(!1),i=(0,c.A)(o,2),s=i[0],u=i[1];return(0,a.createElement)("div",{className:"btl-code-block",style:{position:"relative"}},n&&(0,a.createElement)("span",{className:"copy-btn",onClick:function(){(0,d.lW)(r||l),u(!0);var e=setTimeout((function(){u(!1),clearTimeout(e)}),1e3)}},s?(0,a.createElement)("span",{className:"dashicons dashicons-yes",style:{marginRight:"2px"}}):(0,a.createElement)(k,null)),(0,a.createElement)("code",{dangerouslySetInnerHTML:{__html:l}}))},O=function(e){var t=e.custom_host,n=e.siteIp;return(0,a.createElement)("div",{className:"btl-individual-config-blocks --btl-records-bg"},(0,a.createElement)("div",{className:"btl-instruction-block-wrapper"},(0,a.createElement)("span",null,(0,o.__)("Please add the following A record to your DNS settings. ","betterlinks-pro")),(0,a.createElement)("a",{target:"_blank",href:"https://betterlinks.io/docs/configure-custom-domain/#1-toc-title",style:{color:"inherit","font-weight":"700","text-decoration":"underline","font-size":"inherit"}},(0,o.__)("Learn More","betterlinks-pro"))),(0,a.createElement)("div",{className:"btl-code-block-wrapper"},(0,a.createElement)("strong",null,(0,o.__)("Record Type:","betterlinks"),(0,a.createElement)(w,{code:"A",copyCode:"A"})),(0,a.createElement)("br",null),(0,a.createElement)("strong",null,(0,o.__)("Host:","betterlinks")," ",(0,a.createElement)(w,{code:t,copyCode:t})),t&&(0,a.createElement)("span",null,"[",(0,o.__)("Use","betterlinks")," ",(0,a.createElement)("strong",null,"@")," ",(0,o.__)("for root domain","betterlinks"),"]"),(0,a.createElement)("br",null),(0,a.createElement)("br",null),(0,a.createElement)("strong",null,"Value:",(0,a.createElement)(w,{code:n||"",copyCode:n||""}))),(0,a.createElement)("div",{className:"btl-instruction-block-wrapper"},(0,o.__)("Please note that it may take up to","betterlinks")," ",(0,a.createElement)("strong",null,(0,o.__)("72 hours","betterlinks"))," ",(0,o.__)("for the A record to propagate, depending on your domain provider","betterlinks"),(0,a.createElement)("br",null),(0,a.createElement)("br",null),(0,o.__)("Now point to this WordPress installation, use the given redirect rule available in the following ","betterlinks"),(0,a.createElement)("strong",null,(0,o.__)('"Server Configuration"',"betterlinks")," "),(0,o.__)(" section. For more info ","betterlinks"),(0,a.createElement)("a",{target:"_blank",href:"https://betterlinks.io/docs/configure-custom-domain/#2-toc-title",style:{color:"inherit","font-weight":"700","text-decoration":"underline","font-size":"inherit"}},(0,o.__)("Click Here","betterlinks-pro"))))},N=function(e){var t=e.children;return(0,a.createElement)("div",{className:"code-container"},t)};function C(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?C(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):C(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const j=function(e){var t=e.willShowUpgradeToProModal;return(0,a.createElement)(a.Fragment,null,(0,a.createElement)("div",{className:"server-type-wrapper"},(0,a.createElement)("div",{id:"server-type",className:"server-type-title"},(0,o.__)("Select your server type","betterlinks")),(0,a.createElement)("div",{role:"group","aria-labelledby":"server-type",className:"server-type"},(0,a.createElement)("div",null,(0,a.createElement)("label",{htmlFor:"apache"},(0,a.createElement)(m.D0,P({id:"apache",type:"radio",name:"server_type",value:"apache"},t)),(0,o.__)("Apache","betterlinks"))),(0,a.createElement)("div",null,(0,a.createElement)("label",{htmlFor:"nginx"},(0,a.createElement)(m.D0,P({id:"nginx",type:"radio",name:"server_type",value:"nginx",disabled:!0},t)),(0,o.__)("Nginx","betterlinks"))))))},x=function(e){var t,n,l=e.host,r=e.custom_domain;return(0,a.createElement)("div",{className:"btl-individual-config-blocks --btl-records-bg"},(0,a.createElement)("div",{className:"btl-instruction-block-wrapper"},(0,o.__)("Copy and place the following rewrite rule at the beginning of the","betterlinks")," ",(0,a.createElement)("strong",null,".htaccess")," ",(0,o.__)("file in your website's root directory. For more info ","betterlinks-pro"),(0,a.createElement)("a",{target:"_blank",href:"https://betterlinks.io/docs/configure-custom-domain/#4-toc-title",style:{color:"inherit","font-weight":"700","text-decoration":"underline","font-size":"inherit"}},(0,o.__)("Click Here","betterlinks-pro"))),(0,a.createElement)("div",{className:"btl-code-block-wrapper"},(0,a.createElement)("strong",null,(0,a.createElement)(w,{code:"RewriteEngine On <br>RewriteCond %{HTTP_HOST} ^".concat(l,"$ [OR] <br>RewriteCond %{HTTP_HOST} ^www.").concat(l,"$ <br>RewriteRule (.*)$ ").concat(null==r||null===(t=r.shortlink_custom_domain)||void 0===t?void 0:t.replace(/\/$/,""),"/$1 [R=301,L]"),copyCode:"RewriteEngine On \nRewriteCond %{HTTP_HOST} ^".concat(l,"$ [OR] \nRewriteCond %{HTTP_HOST} ^www.").concat(l,"$ \nRewriteRule (.*)$ ").concat(null==r||null===(n=r.shortlink_custom_domain)||void 0===n?void 0:n.replace(/\/$/,""),"/$1 [R=301,L]")}))))};function A(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,l)}return n}function S(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?A(Object(n),!0).forEach((function(t){(0,i.A)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):A(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const T=(0,f.Ng)((function(e){return{settings:e.settings}}),(function(e){return{update_option:(0,v.zH)(p.YD,e)}}))((function(e){var t=e.update_option,n=(0,a.useState)((0,o.__)("Save Settings","betterlinks")),l=(0,c.A)(n,2),r=l[0],i=l[1],s=e.settings.settings;return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(m.l1,{enableReinitialize:!0,initialValues:S(S({},s),{},{server_type:"apache"}),onSubmit:function(e,n){var l=n.setFieldError;betterLinksHooks.applyFilters("BetterLinksCustomDomainSettings",!1,{values:e,setFieldError:l,isURL:_.isURL})?(0,d.PW)(e,t,i):window.scrollTo(0,250)}},(function(e){return(0,a.createElement)(m.lV,null,betterLinksHooks.applyFilters("BetterLinksCustomDomain",(0,a.createElement)(D,{values:e.values}),S(S({},e),{},{ClipLoader:h(),CodeBlock:w,formSubmitText:r},{Tab:g.oz,Tabs:g.tU,TabList:g.wb,TabPanel:g.Kp,Field:m.D0})))})))}));var D=function(e){var t=e.values,n=(0,s.c)(),l=(0,c.A)(n,3),r=l[0],i=l[1],m=l[2],p=!d.JT&&{onClick:i},v=(0,d.OS)("1.9.5");return(0,a.createElement)(a.Fragment,null,d.JT&&!v&&(0,a.createElement)("div",{className:"btl-form-group"},(0,a.createElement)("div",{className:"short-description"},(0,a.createElement)("b",{style:{fontWeight:700}},(0,o.__)("Note: ")),(0,o.__)("The Custom Domain feature is available in","betterlinks")," ",(0,a.createElement)("strong",null,(0,o.__)("BetterLinks Pro V2.0.0 and later","betterlinks")," ")," ",(0,o.__)(". Make sure you have updated to the latest version to use this feature","betterlinks"))),(0,a.createElement)(u.A,{isOpenModal:r,closeModal:m}),(0,a.createElement)(b.A,S(S({title:(0,o.__)("Enable Custom Domain","betterlinks")},p),{},{is_pro:!d.JT})),(0,a.createElement)("span",{className:"btl-form-group btl-form-group--top"},(0,a.createElement)("label",{className:"btl-form-label"},(0,o.__)("Custom Domain","betterlinks")),(0,a.createElement)("div",{className:"link-options__body",style:{flexDirection:"column"}},(0,a.createElement)("div",S({style:{maxWidth:250,display:"flex",alignItems:"center"}},p),(0,a.createElement)("input",{type:"text",className:"btl-text-field btl-text-field-teaser",placeholder:"http://example.com",disabled:!0}),(0,a.createElement)("button",{type:"button",className:"button button-secondary",style:{cursor:"not-allowed"},onClick:function(e){return e.preventDefault()}},(0,o.__)("Verify","betterlinks-pro"))))),(0,a.createElement)("span",{className:"btl-form-group btl-domain-configuration",style:{alignItems:"flex-start"}},(0,a.createElement)("label",{className:"btl-form-label",style:{marginTop:"10px"}},(0,o.__)("Domain Configuration","betterlinks-pro")),(0,a.createElement)("div",{className:"btl-form-field"},(0,a.createElement)("label",{className:"btl-checkbox-field block"}),(0,a.createElement)(N,{name:"A Records",type:"a_records",copy:!1,code:""},(0,a.createElement)(O,{custom_host:"example.com",siteIp:"127.0.0.1"})))),(0,a.createElement)("span",{className:"btl-form-group btl-domain-configuration",style:{alignItems:"flex-start"}},(0,a.createElement)("label",{className:"btl-form-label",style:{marginTop:"10px"}},(0,o.__)("Server Configuration","betterlinks-pro"),(0,a.createElement)("div",{className:"btl-tooltip"},(0,a.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,a.createElement)("span",{className:"btl-tooltiptext",style:{width:"255px","text-align":"left","line-height":"1.2em"}},(0,o.__)("Choose your server according to your WordPress installation server type. For more info ","betterlinks-pro"),(0,a.createElement)("a",{target:"_blank",href:"https://betterlinks.io/docs/configure-custom-domain/#3-toc-title",style:{color:"inherit","font-weight":"700","text-decoration":"underline","font-size":"inherit"}},(0,o.__)("Click Here","betterlinks-pro"))))),(0,a.createElement)("div",{className:"btl-form-field"},(0,a.createElement)("label",{className:"btl-checkbox-field block"}),(0,a.createElement)("div",null,(0,a.createElement)(j,{willShowUpgradeToProModal:p}),"apache"===(null==t?void 0:t.server_type)&&(0,a.createElement)(N,{name:".htaccess",type:"htaccess"},(0,a.createElement)(x,{host:"yoursite.com",custom_domain:"http://example.com"}))))))},L=n(16560);const R=function(){return(0,a.createElement)(a.Fragment,null,(0,a.createElement)(L.A,{label:(0,o.__)("BetterLinks Custom Domain","betterlinks"),is_pro:!d.JT}),(0,a.createElement)("div",{className:"btl-tab-panel-inner"},(0,a.createElement)(T,null)))}},7420:function(e,t,n){var l=this&&this.__assign||function(){return l=Object.assign||function(e){for(var t,n=1,l=arguments.length;n<l;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},l.apply(this,arguments)},r=this&&this.__createBinding||(Object.create?function(e,t,n,l){void 0===l&&(l=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,l,r)}:function(e,t,n,l){void 0===l&&(l=n),e[l]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t},i=this&&this.__rest||function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&t.indexOf(l)<0&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(l=Object.getOwnPropertySymbols(e);r<l.length;r++)t.indexOf(l[r])<0&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(n[l[r]]=e[l[r]])}return n};Object.defineProperty(t,"__esModule",{value:!0});var c=o(n(51609)),s=n(11665),u=(0,n(79489).createAnimation)("ClipLoader","0% {transform: rotate(0deg) scale(1)} 50% {transform: rotate(180deg) scale(0.8)} 100% {transform: rotate(360deg) scale(1)}","clip");t.default=function(e){var t=e.loading,n=void 0===t||t,r=e.color,a=void 0===r?"#000000":r,o=e.speedMultiplier,m=void 0===o?1:o,d=e.cssOverride,p=void 0===d?{}:d,b=e.size,v=void 0===b?35:b,f=i(e,["loading","color","speedMultiplier","cssOverride","size"]),_=l({background:"transparent !important",width:(0,s.cssValue)(v),height:(0,s.cssValue)(v),borderRadius:"100%",border:"2px solid",borderTopColor:a,borderBottomColor:"transparent",borderLeftColor:a,borderRightColor:a,display:"inline-block",animation:"".concat(u," ").concat(.75/m,"s 0s infinite linear"),animationFillMode:"both"},p);return n?c.createElement("span",l({style:_},f)):null}},79489:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createAnimation=void 0,t.createAnimation=function(e,t,n){var l="react-spinners-".concat(e,"-").concat(n);if("undefined"==typeof window||!window.document)return l;var r=document.createElement("style");document.head.appendChild(r);var a=r.sheet,o="\n    @keyframes ".concat(l," {\n      ").concat(t,"\n    }\n  ");return a&&a.insertRule(o,0),l}},11665:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.cssValue=t.parseLengthAndUnit=void 0;var n={cm:!0,mm:!0,in:!0,px:!0,pt:!0,pc:!0,em:!0,ex:!0,ch:!0,rem:!0,vw:!0,vh:!0,vmin:!0,vmax:!0,"%":!0};function l(e){if("number"==typeof e)return{value:e,unit:"px"};var t,l=(e.match(/^[0-9.]*/)||"").toString();t=l.includes(".")?parseFloat(l):parseInt(l,10);var r=(e.match(/[^0-9]*$/)||"").toString();return n[r]?{value:t,unit:r}:(console.warn("React Spinners: ".concat(e," is not a valid css value. Defaulting to ").concat(t,"px.")),{value:t,unit:"px"})}t.parseLengthAndUnit=l,t.cssValue=function(e){var t=l(e);return"".concat(t.value).concat(t.unit)}}}]);