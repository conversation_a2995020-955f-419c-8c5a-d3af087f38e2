!function(t){t(".btl-short-url-copy-button").click((function(){var n=t(this).find(".icon img"),o=t(this).find(".icon span");new ClipboardJS(".btl-short-url-copy-button"),n.hide(),o.show();var i=setTimeout((function(){n.show(),o.hide(),clearTimeout(i)}),2e3)}));for(var n="",o=0;o<400;o++)n+='<div class="ball" style="background: #'+Math.floor(16777215*Math.random()).toString(16),n+="; animation-duration: 3s; animation-delay: ",n+=2*Math.random()+0+'s;"></div>';document.getElementById("confetti").innerHTML=n}(jQuery);