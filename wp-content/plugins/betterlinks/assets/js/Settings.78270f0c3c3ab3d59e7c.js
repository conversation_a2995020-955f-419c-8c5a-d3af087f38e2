"use strict";(globalThis.webpackChunkbetterlinks=globalThis.webpackChunkbetterlinks||[]).push([[248],{99255:(e,t,l)=>{l.d(t,{A:()=>d});var a=l(64467),n=l(3453),r=l(51609),s=l.n(r),i=l(4949),c=l(46005),o=l(27723),m=l(19735);function u(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}function b(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?u(Object(l),!0).forEach((function(t){(0,a.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):u(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}const d=function(e){var t=(0,i.Mt)(e.name),l=(0,n.A)(t,3),a=l[0],u=l[2].setValue,d=(0,r.useState)("cloak"!=(null==a?void 0:a.value)&&m.JT?a:{label:(0,o.__)("307 (Temporary)","betterlinks"),value:"307"}),p=(0,n.A)(d,2),k=p[0],_=p[1];return(0,r.useEffect)((function(){"pro"===(null==a?void 0:a.value)?(u(null==k?void 0:k.value,!1),e.setFieldValue(a.name,null==k?void 0:k.value)):_((e.value||[]).find((function(e){return e.value==a.value})))}),[]),(0,r.createElement)(s().Fragment,null,(0,r.createElement)(c.Ay,{className:"btl-modal-select--full ".concat(e.value&&e.value.find((function(e){return"pro"==e.value}))?"btl-modal-select-need-pro-teaser":""),classNamePrefix:"btl-react-select",id:a.id,name:a.name,defaultValue:e.value&&e.value.filter((function(t){return t.value==(e.defaultValue||"307")})),onChange:function(t){return null==t?e.setFieldValue(a.name,""):(null!=e&&e.isQuickSetup&&("pro"===(null==t?void 0:t.value)?(e.setUpgradeToProModal(!0),u(null==k?void 0:k.value,a.value),e.setFieldValue(a.name,a.value)):(e.setFieldValue(a.name,null==t?void 0:t.value),_((e.value||[]).find((function(e){return e.value==t.value})))),null==e||e.setSettings((function(l){return b(b({},l),{},{redirect_type:e.isMulti?t.map((function(e){return e.value})):"pro"!==t.value?t.value:a.value})}))),e.setFieldValue(a.name,e.isMulti?t.map((function(e){return e.value})):"pro"!==t.value?t.value:a.value))},options:e.value,value:k,isMulti:e.isMulti}))}},5051:(e,t,l)=>{l.d(t,{A:()=>s});var a=l(51609),n=l(27723),r=l(19735);const s=function(e){var t=e.mode,l=void 0===t?"#f2f2f2":t,s=e.noticeType,i=void 0===s?"warning":s,c=e.compatibleProVersion,o=e.notice;if((0,r.OS)(c))return"";var m={group:{marginLeft:0,padding:0},notice:{padding:"15px",background:l}};return(0,a.createElement)("div",{className:"btl-form-group ".concat(""!==i?"notice notice-"+i:""),style:m.group},(0,a.createElement)("div",{style:m.notice},(0,a.createElement)("b",{style:{fontWeight:700}},(0,n.__)("Note: ")),o))}},58987:(e,t,l)=>{l.d(t,{A:()=>u});var a=l(64467),n=l(80045),r=l(51609),s=(l(27723),l(40150)),i=l(5556),c=["title","is_pro"];function o(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}var m={title:l.n(i)().string};function u(e){var t=e.title,l=void 0===t?"":t,i=e.is_pro,m=void 0!==i&&i,u=(0,n.A)(e,c);return(0,r.createElement)("div",function(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?o(Object(l),!0).forEach((function(t){(0,a.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):o(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}({className:"btl-role-item btl-form-group"},u),(0,r.createElement)("label",{className:"btl-form-label"},l,m&&(0,r.createElement)(s.A,null)),(0,r.createElement)("div",{className:"link-options__body"},(0,r.createElement)("label",{className:"btl-checkbox-field"},(0,r.createElement)("input",{type:"checkbox",className:"btl-check",disabled:!0}),(0,r.createElement)("span",{className:"text"}))))}u.propTypes=m},10700:(e,t,l)=>{l.d(t,{A:()=>b});var a=l(3453),n=l(51609),r=l.n(n),s=l(5556),i=l.n(s),c=l(4949),o=l(93503),m={catId:i().number,data:i().object,fieldName:i().string,setFieldValue:i().func},u=function(e){var t=e.catId,l=e.data,s=e.fieldName,i=e.setFieldValue,m=e.disabled,u=(0,c.Mt)(s),b=(0,a.A)(u,1)[0];return(0,n.createElement)(r().Fragment,null,l.terms?(0,n.createElement)(o.Ay,{className:"btl-modal-select",id:b.id,name:b.name,defaultValue:function(){if(!t){var e=l.terms.filter((function(e){return"uncategorized"==e.term_slug}))[0];return{value:e.ID,label:e.term_name}}var a=l.terms.filter((function(e){return e.ID==t}));if(a.length>0){var n=a[0];return{value:n.ID,label:n.term_name}}}(),classNamePrefix:"btl-react-select",onChange:function(e){return i(b.name,null==e?"":e.value)},options:l.terms.filter((function(e){return"category"==e.term_type&&"uncategorized"!=e.term_slug})).map((function(e){return{value:e.ID,label:e.term_name}})),isDisabled:m}):(0,n.createElement)("div",null,(0,n.createElement)(o.Ay,{className:"btl-modal-select",id:b.id,classNamePrefix:"btl-react-select",isDisabled:m})))};u.propTypes=m;const b=u},16560:(e,t,l)=>{l.d(t,{A:()=>x});var a=l(3453),n=l(80045),r=l(51609),s=l.n(r),i=l(49924),c=l(68238),o=l(27723),m=l(19735),u=l(74086),b=l(67154),d=l(58766),p=l(7400),k=l(20312),_=l.n(k),f=l(46005),E=[{label:(0,o.__)("Delete All","betterlinks"),value:!1},{label:(0,o.__)("Delete clicks older than 30 days","betterlinks"),value:30},{label:(0,o.__)("Delete clicks older than 90 days","betterlinks"),value:90}];const v=(0,i.Ng)((function(){return{}}),(function(e){return{fetchCustomClicksData:(0,c.zH)(d.lC,e),dispatch_new_links_data:(0,c.zH)(p.jT,e)}}))((function(e){var t=e.fetchCustomClicksData,l=e.dispatch_new_links_data,n=(e.propsForAnalytics||{}).customDateFilter,s=(0,r.useState)(0),i=(0,a.A)(s,2),c=i[0],o=i[1],u=(0,r.useState)(!1),b=(0,a.A)(u,2),d=b[0],p=b[1],k=(0,r.useState)(0),v=(0,a.A)(k,2),g=v[0],h=v[1],y=(0,r.useState)("reset_modal_step_1"),N=(0,a.A)(y,2),w=N[0],O=N[1],x=(0,r.useState)(E[0]),A=(0,a.A)(x,2),C=A[0],F=A[1];(0,r.useEffect)((function(){var e,t;return d?null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.add("betterlinks-delete-clicks-modal-popup-opened"):null===(t=document)||void 0===t||null===(t=t.body)||void 0===t||null===(t=t.classList)||void 0===t||t.remove("betterlinks-delete-clicks-modal-popup-opened"),function(){var e;null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.remove("betterlinks-delete-clicks-modal-popup-opened")}}),[d]);var P=function(){clearTimeout(c),O("reset_modal_step_1"),p(!1),F(E[0])};return(0,r.createElement)("div",{className:"btl-analytic-reset-wrapeer betterlinks"},(0,r.createElement)("button",{className:"button-primary btl-reset-analytics-initial-button",onClick:function(){p(!0),O("reset_modal_step_1")}},"Reset"),(0,r.createElement)(_(),{isOpen:d,onRequestClose:P,ariaHideApp:!1},(0,r.createElement)("div",{className:"btl-reset-modal-popup-wrapper "},(0,r.createElement)("span",{className:"btl-close-modal",onClick:P},(0,r.createElement)("i",{className:"btl btl-cancel"})),"reset_modal_step_1"===w&&(0,r.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-1 betterlinks-body"},(0,r.createElement)("h2",null,"Pick the range of BetterLinks Analytics that you want to reset."),(0,r.createElement)("div",{className:"select_apply"},(0,r.createElement)(f.Ay,{className:"btl-modal-select--full ",classNamePrefix:"btl-react-select",onChange:function(e){F(e)},options:E,value:C,isMulti:!1}),(0,r.createElement)("button",{className:"button-primary btl-btn-reset-analytics btl-btn-reset-apply-1",onClick:function(){O("reset_modal_step_2")}},"Apply"))),"reset_modal_step_2"===w&&(0,r.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-2 betterlinks-body"},(0,r.createElement)("h2",null,"This Action Cannot be undone. Are you sure you want to continue?"),(0,r.createElement)("h4",null,"Clicking ",(0,r.createElement)("span",{style:{fontWeight:700}},"Reset Clicks")," will permanently delete the clicks data from database and it cannot be restored again.",(0,r.createElement)("span",{style:{display:"Block"}},"Click 'cancel' to abort.")),(0,r.createElement)("div",{className:"btl-btn-reset-popup-step-2-buttons"},(0,r.createElement)("button",{className:"button-primary btl-btn-reset-apply-2",onClick:function(){if(n){var e=(0,m.Yq)(n[0].startDate,"yyyy-mm-dd"),a=(0,m.Yq)(n[0].endDate,"yyyy-mm-dd");O("deleting");var r=(null==C?void 0:C.value)||!1;(0,m.Xq)(r,e,a).then((function(e){var a,n,r,s,i=setTimeout((function(){P()}),3e3);o(i),null!=e&&null!==(a=e.data)&&void 0!==a&&a.success?(h(null==e||null===(n=e.data)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.count),t({data:null==e||null===(r=e.data)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.new_clicks_data}),l({data:null==e||null===(s=e.data)||void 0===s||null===(s=s.data)||void 0===s?void 0:s.new_links_data}),O("success")):O("failed")})).catch((function(e){console.log("---caught error on DeleteClicks",{err:e});var t=setTimeout((function(){P()}),3e3);o(t)}))}}},"Reset Clicks"),(0,r.createElement)("button",{className:"button-primary btl-btn-reset-cancel",onClick:function(){return O("reset_modal_step_1")}},"Cancel"))),"deleting"===w&&(0,r.createElement)("h2",null,"Deleting..."),"success"===w&&0!==g&&(0,r.createElement)("h2",null,"Success!!! ",(0,r.createElement)("span",{className:"success_delete_count"},g)," clicks record Deleted!!!"),"success"===w&&0===g&&(0,r.createElement)("h2",null,!1===(null==C?void 0:C.value)&&"You don't have any clicks data",30===(null==C?void 0:C.value)&&"You don't have clicks data older than 30 days",90===(null==C?void 0:C.value)&&"You don't have clicks data older than 90 days"),"failed"===w&&(0,r.createElement)("h2",null,"Failed!!"))))}));var g=l(5556),h=l.n(g),y=l(40150),N=["is_pro","render"],w={label:h().string,render:h().func},O=function(e){var t=e.is_pro,l=void 0!==t&&t,i=e.render,c=void 0===i?function(){}:i,u=(0,n.A)(e,N),b=u.propsForAnalytics,d=u.activity.darkMode,p=(0,r.useState)(d),k=(0,a.A)(p,2),_=k[0],f=k[1];(0,r.useEffect)((function(){d?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode")}),[]);var E=betterLinksQuery.get("page"),g=u.favouriteSort.sortByFav;return(0,r.createElement)("div",{className:"topbar"},(0,r.createElement)("div",{className:"topbar__logo_container"},(0,r.createElement)("div",{className:"topbar__logo"},(0,r.createElement)("img",{src:m.hq+"assets/images/logo-large".concat(_?"-white":"",".svg"),alt:"logo"}),(0,r.createElement)("span",{className:"topbar__logo__text"},u.label),l&&(0,r.createElement)(y.A,null)),c()),(0,r.createElement)("div",{className:"topbar-inner"},"betterlinks"===E&&(0,r.createElement)(s().Fragment,null,(0,r.createElement)("div",{className:"btl-view-control"},(0,r.createElement)("button",{title:(0,o.__)("Favorite Links","betterlinks"),className:"btl-link-view-toggler btl-sortby-fav ".concat(g?"active":""),onClick:function(){return u.sortFavourite(!g)}},(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"favorite-svg",viewBox:"0 0 512 512",xmlSpace:"preserve"},(0,r.createElement)("path",{className:"fav-icon-svg-path",d:"M392.2 317.5c-3 2.9-4.4 7.1-3.7 11.3L414 477.4c1.2 7-3.5 13.6-10.5 14.9-2.8.5-5.6 0-8.1-1.3L262 420.9c-3.7-2-8.2-2-12 0L116.6 491c-3.1 1.7-6.8 1.9-10.1.8-6-2.1-9.5-8.1-8.5-14.4l25.4-148.5c.7-4.2-.7-8.4-3.7-11.4L11.9 212.4c-5.1-5-5.2-13.1-.2-18.2 2-2 4.6-3.3 7.3-3.7l149.1-21.7c4.2-.6 7.8-3.2 9.7-7l66.7-135c2.6-5.3 8.4-8.1 14.2-6.9 3.9.7 7.2 3.3 8.9 6.9l66.7 135c1.9 3.8 5.5 6.4 9.7 7l149 21.6c7 1 11.9 7.6 10.9 14.6-.4 2.7-1.7 5.3-3.7 7.2l-108 105.3z"}))),(0,r.createElement)("button",{title:(0,o.__)("List View","betterlinks"),className:"btl-link-view-toggler ".concat("list"==u.activity.linksView?"active":""),onClick:function(){return u.linksView("list")}},(0,r.createElement)("i",{className:"btl btl-list"})),(0,r.createElement)("button",{title:(0,o.__)("Grid View","betterlinks"),className:"btl-link-view-toggler ".concat("grid"==u.activity.linksView?"active":""),onClick:function(){return u.linksView("grid")}},(0,r.createElement)("i",{className:"btl btl-grid"})))),(null==b?void 0:b.isResetAnalytics)&&(0,r.createElement)(v,{propsForAnalytics:b}),(0,r.createElement)("label",{className:"theme-mood-button",htmlFor:"theme-mood",title:(0,o.__)("Theme Mode","betterlinks")},(0,r.createElement)("input",{type:"checkbox",name:"theme-mood",id:"theme-mood",value:_,onChange:function(){return function(e){e?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode"),u.update_theme_mode(e),f(e)}(!_)},checked:_}),(0,r.createElement)("span",{className:"theme-mood"},(0,r.createElement)("span",{className:"icon"},(0,r.createElement)("i",{className:"btl btl-sun"})),(0,r.createElement)("span",{className:"icon"},(0,r.createElement)("i",{className:"btl btl-moon"}))))))};O.propTypes=w;const x=(0,i.Ng)((function(e){return{activity:e.activity,favouriteSort:e.favouriteSort}}),(function(e){return{linksView:(0,c.zH)(u.xb,e),sortFavourite:(0,c.zH)(b.sortFavourite,e),update_theme_mode:(0,c.zH)(u.Q7,e)}}))(O)},13558:(e,t,l)=>{l.r(t),l.d(t,{default:()=>Be});var a=l(3453),n=l(51609),r=l.n(n),s=l(27723),i=l(19555),c=l(93348),o=l(49924),m=l(68238),u=l(10138),b=l(41846),d=l(16560),p=l(45458),k=l(64467),_=l(4949),f=l(72505),E=l.n(f),v=l(99255),g=l(58766),h=l(61582),y=l(67783),N=l(2078),w=l(19735),O=l(40150);function x(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}function A(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?x(Object(l),!0).forEach((function(t){(0,k.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):x(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}const C=(0,o.Ng)((function(e){return{clicks:e.clicks,postdatas:e.postdatas,terms:e.terms}}),(function(e){return{update_option:(0,m.zH)(u.YD,e),fetch_clicks_data:(0,m.zH)(g.AK,e),fetch_post_types_data:(0,m.zH)(b.P,e),fetch_terms_data:(0,m.zH)(h.M3,e)}}))((function(e){var t=e.settings,l=e.fetch_clicks_data,i=e.fetch_terms_data,c=e.terms,o=e.update_option,m=e.postdatas,u=(0,n.useState)((0,s.__)("Refresh Stats","betterlinks")),b=(0,a.A)(u,2),d=b[0],k=b[1],f=(0,n.useState)((0,s.__)("Active Now","betterlinks")),g=(0,a.A)(f,2),h=g[0],x=g[1],C=(0,n.useState)((0,s.__)("Save Settings","betterlinks")),F=(0,a.A)(C,2),P=F[0],S=F[1],j=(0,n.useState)(w.aT),L=(0,a.A)(j,2),D=L[0],T=L[1],M=(0,n.useState)((0,s.__)("Active Now","betterlinks")),V=(0,a.A)(M,2),R=V[0],H=V[1],z=(0,n.useState)(w.cw),B=(0,a.A)(z,2),W=B[0],I=B[1],U=(0,n.useState)(!1),q=(0,a.A)(U,2),Y=q[0],J=q[1];(0,n.useEffect)((function(){!w.JT||null!=c&&c.terms||i()}),[]);var G=function(){x((0,s.__)("Activating...","betterlinks")),E().post("".concat(ajaxurl,"?action=betterlinks/admin/write_json_links&security=").concat(w.sL)).then((function(e){e.data&&((0,w.o6)(null,(0,s.__)("Activated!","betterlinks"),(0,s.__)("Active Now","betterlinks"),x),setTimeout((function(){T(!0)}),1500))}),(function(e){console.log(e)}))},K=function(){H((0,s.__)("Activating...","betterlinks")),E().post("".concat(ajaxurl,"?action=betterlinks/admin/write_json_clicks&security=").concat(w.sL)).then((function(e){e.data&&((0,w.o6)(null,(0,s.__)("Activated!","betterlinks"),(0,s.__)("Refresh Stats","betterlinks"),H),setTimeout((function(){I(!0)}),1500))}),(function(e){console.log(e)}))},Q=function(){k((0,s.__)("Refreshing...","betterlinks")),E().post("".concat(ajaxurl,"?action=betterlinks/admin/analytics&security=").concat(w.sL)).then((function(e){e.data&&((0,w.o6)(null,(0,s.__)("Done!","betterlinks"),(0,s.__)("Refresh Stats","betterlinks"),k),l({}))}),(function(e){console.log(e)}))},X=function(){J(!0)};return(0,n.createElement)(r().Fragment,null,(0,n.createElement)(N.A,{isOpenModal:Y,closeModal:function(){J(!1)}}),(0,n.createElement)(_.l1,{enableReinitialize:!0,initialValues:A({},t),onSubmit:function(e){return(0,w.PW)(e,o,S)}},(function(e){var l,a;return(0,n.createElement)(_.lV,null,(0,n.createElement)("div",{className:"btl-tab-panel-inner"},(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Link Redirection Status","betterlinks")," ",(0,n.createElement)("br",null),(0,s.__)("(Fast Mode)","betterlinks")),(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)("div",{className:"status"},(0,n.createElement)("div",{className:"active-status ".concat(D?"Active":"Disable")},D?"Active":"Disable"),!D&&(0,n.createElement)("button",{type:"button",onClick:G,className:"button button-primary"},h)),(0,n.createElement)("div",{className:"short-description"},(0,n.createElement)("b",{style:{fontWeight:700}},(0,s.__)("Note: ")),(0,s.__)("If it's enabled, when you click on the link, it will fetch the target URL from the .json file and will redirect it. Otherwise, it will fetch directly from the database","betterlinks")))),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},W?(0,s.__)("Fetch Analytics Data","betterlinks"):(0,n.createElement)(r().Fragment,null,(0,s.__)("Click Data Status ","betterlinks")," ",(0,n.createElement)("br",null)," ",(0,s.__)("(Fast Mode)","betterlinks"))),(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)("div",{className:"status"},(0,n.createElement)("div",{className:"active-status ".concat(W?"Active":"Disable")},W?"Active":"Disable"),W?(0,n.createElement)("button",{type:"button",onClick:Q,className:"button button-primary"},d):(0,n.createElement)("button",{type:"button",onClick:K,className:"button button-primary"},R)),(0,n.createElement)("div",{className:"short-description"},(0,n.createElement)("b",{style:{fontWeight:700}},(0,s.__)("Note: ")),W?(0,s.__)("Analytics data is updated within 1 hour interval. Hit the 'Refresh Stats' button to instantly update your analytics data","betterlinks"):(0,s.__)("If it's enabled, before a link is redirected, the click data will be saved in the json file in 1 hour time interval. Otherwise, it will be directly inserted into the database","betterlinks")))),t&&(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Redirect Type","betterlinks")),(0,n.createElement)(v.A,{className:"btl-modal-select--full",classNamePrefix:"btl-react-select",id:"redirect_type",name:"redirect_type",setUpgradeToProModal:J,value:[].concat((0,p.A)(y.XN),[{value:w.JT?"cloak":"pro",label:w.JT?(0,s.__)("Cloaked","betterlinks"):(0,n.createElement)(n.Fragment,null,(0,s.__)("Cloaked","betterlinks"),(0,n.createElement)(O.A,null))}]),defaultValue:"cloak"!=t.redirect_type||w.JT?t.redirect_type:"307",setFieldValue:e.setFieldValue,isMulti:!1})),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Link Options","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(_.D0,{className:"btl-check",name:"nofollow",type:"checkbox",onChange:function(){return e.setFieldValue("nofollow",!e.values.nofollow)}}),(0,n.createElement)("span",{className:"text"},(0,s.__)("No Follow","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will add nofollow attribute to your link. (Recommended)","betterlinks"))))),(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(_.D0,{className:"btl-check",name:"sponsored",type:"checkbox",onChange:function(){return e.setFieldValue("sponsored",!e.values.sponsored)}}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Sponsored","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will add sponsored attribute to your link. (Recommended for Affiliate links)","betterlinks"))))),(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(_.D0,{className:"btl-check",name:"param_forwarding",type:"checkbox",onChange:function(){return e.setFieldValue("param_forwarding",!e.values.param_forwarding)}}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Parameter Forwarding","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will pass the parameters you have set in the target URL","betterlinks"))))),(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(_.D0,{className:"btl-check",name:"track_me",type:"checkbox",onChange:function(){return e.setFieldValue("track_me",!e.values.track_me)}}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Tracking","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will let you check Analytics report of your links","betterlinks"))))))),(0,n.createElement)("span",{className:"btl-form-group btl-form-group--make-center"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Random URL Slug","betterlinks")),(0,n.createElement)("div",{className:"link-options__body",style:{flexDirection:"column"}},(0,n.createElement)("label",{className:"btl-checkbox-field block",style:{marginBottom:0}},(0,n.createElement)(_.D0,{type:"checkbox",className:"btl-check",name:"is_random_string"}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Enable Random URL Slug","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will randomly generate strings for your shortened URL","betterlinks"))))))),(0,n.createElement)("span",{className:"btl-form-group btl-form-group--make-center"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Case Sensitivity","betterlinks")),(0,n.createElement)("div",{className:"link-options__body",style:{flexDirection:"column"}},(0,n.createElement)("label",{className:"btl-checkbox-field block",style:{marginBottom:0}},(0,n.createElement)(_.D0,{type:"checkbox",className:"btl-check",name:"is_case_sensitive"}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Enable Case Sensitive Links","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will make your shortened URLs case sensitive","betterlinks"))))))),(0,n.createElement)("span",{className:"btl-form-group btl-form-group--top"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Link Prefix","betterlinks")),(0,n.createElement)("div",{className:"link-options__body",style:{flexDirection:"column"}},(0,n.createElement)("div",{style:{maxWidth:"200px"}},(0,n.createElement)(_.D0,{className:"btl-text-field",name:"prefix",value:null!==(l=null===(a=e.values)||void 0===a?void 0:a.prefix)&&void 0!==l?l:"go",type:"text"})),(0,n.createElement)("div",{className:"short-description"},(0,n.createElement)("b",{style:{fontWeight:700}},(0,s.__)("Note:","betterlinks")," "),(0,s.__)("The prefix will be added before your Shortened URL’s slug eg.","betterlinks"),betterLinksHooks.applyFilters("site_url",w.IV),e.values.prefix&&(0,n.createElement)(n.Fragment,null,"/",(0,n.createElement)("strong",null,e.values.prefix)),(0,s.__)("/your-affiliate-link-name.","betterlinks")))),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Custom Domain","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(_.D0,{className:"btl-check",name:"enable_custom_domain_menu",type:"checkbox",onChange:function(){var t;return e.setFieldValue("enable_custom_domain_menu",!(null!==(t=e.values)&&void 0!==t&&t.enable_custom_domain_menu))}}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Enable Custom Domain Menu","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will allow you to show Custom Domain on the BetterLinks submenu for quick access.","betterlinks"))))))),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("QR Codes","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(_.D0,{className:"btl-check",name:"is_allow_qr",type:"checkbox",onChange:function(){return e.setFieldValue("is_allow_qr",!e.values.is_allow_qr)}}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Enable QR Code Generator","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will allow you to generate & download QR Code for each of your shortened URL","betterlinks"))))))),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Wildcards","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(_.D0,{className:"btl-check",name:"wildcards",type:"checkbox",onChange:function(){return e.setFieldValue("wildcards",!e.values.wildcards)}}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Use Wildcards?","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("To use wildcards, put an asterisk (*) after the folder name that you want to redirect.","betterlinks"))))))),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Bot Clicks","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(_.D0,{className:"btl-check",name:"disablebotclicks",type:"checkbox",onChange:function(){return e.setFieldValue("disablebotclicks",!e.values.disablebotclicks)}}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Disable Bot Clicks","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will prevent your site from bot traffic","betterlinks"))))))),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Instant Redirect","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(_.D0,{className:"btl-check",name:"is_allow_gutenberg",type:"checkbox",onChange:function(){return e.setFieldValue("is_allow_gutenberg",!e.values.is_allow_gutenberg)}}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Allow Instant Redirect","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will allow you to redirect your links instantly from Gutenberg and Elementor Editor.","betterlinks"))))))),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Disable Clicks IP","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(_.D0,{className:"btl-check",name:"is_disable_analytics_ip",type:"checkbox",onChange:function(){var t;return e.setFieldValue("is_disable_analytics_ip",!(null!=e&&null!==(t=e.values)&&void 0!==t&&t.is_disable_analytics_ip))}}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Disable IP Addresses for Analytics","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("If checked, users' ip addresses won't be saved & won't be shown in analytics section","betterlinks"))))))),!w.JT&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)("span",{className:"btl-form-group btl-form-group--teaser"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Force HTTPS","betterlinks")," ",(0,n.createElement)(O.A,null)),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block",onClick:X},(0,n.createElement)("input",{className:"btl-check",name:"force_https",type:"checkbox",disabled:!0}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Enable HTTPS Redirection","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will allow you to redirect your Target URLs in HTTPS.","betterlinks"))))))),(0,n.createElement)("span",{className:"btl-form-group btl-form-group--teaser btl-form-group-uncloaked-categories"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Uncloak Categories","betterlinks")," ",(0,n.createElement)(O.A,null)),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block",onClick:X},(0,n.createElement)("input",{className:"btl-check",name:"is_autolink_headings",type:"checkbox",disabled:!0}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Enable uncloaking categories","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip"},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext"},(0,s.__)("This will allow you to uncloak categories","betterlinks")))))))),betterLinksHooks.applyFilters("BetterLinksAddOptionSettingsTabGeneral",null,A(A({},e),{},{postdatas:m,terms:c})),(0,n.createElement)("button",{className:"button-primary btn-save-settings",type:"submit"},P)))})))}));var F=l(10467),P=l(89280),S=l.n(P);const j=function(e){var t=e.query,l=(0,n.useState)("default"),i=(0,a.A)(l,2),c=i[0],o=i[1],m=(0,n.useState)(""),u=(0,a.A)(m,2),b=u[0],d=u[1],p=(0,n.useState)("links"),k=(0,a.A)(p,2),_=k[0],f=k[1],v=(0,n.useState)({}),g=(0,a.A)(v,2),h=g[0],y=g[1],N=function(e){o(e.target.value)},O=function(e){f(e.target.value)};(0,n.useEffect)((function(){t.get("import")&&E().post("".concat(ajaxurl,"?action=betterlinks/tools/get_import_info&security=").concat(w.KU)).then((function(e){y(JSON.parse(e.data.data))}),(function(e){console.log(e)}))}),[]);var x=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"links",l=new FormData;return"prettylinks"===e?(l.append("action","betterlinks/admin/run_prettylinks_migration"),l.append("re_run",!0)):"simple301redirects"===e?l.append("action","betterlinks/admin/run_simple301redirects_migration"):"thirstyaffiliates"===e&&l.append("action","betterlinks/admin/run_thirstyaffiliates_migration"),l.append("security",w.sL),l.append("type",t),E().post(ajaxurl,l).then((function(e){return e}),(function(e){return e}))},A=function(){var e=(0,F.A)(S().mark((function e(t){var l,a,n;return S().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.preventDefault(),"default"===c){e.next=10;break}return e.next=4,x(c,"links");case 4:if(a=e.sent,null==(n=null===(l=a.data)||void 0===l?void 0:l.data)||!n.btl_prettylinks_migration_running_in_background){e.next=9;break}return y({btl_prettylinks_migration_running_in_background:"PrettyLinks migration running in background"}),e.abrupt("return");case 9:y(n);case 10:case"end":return e.stop()}}),e)})));return function(_x){return e.apply(this,arguments)}}();return(0,n.createElement)(r().Fragment,null,(0,n.createElement)("div",{className:"btl-tab-inner-divider"},(0,n.createElement)("div",{className:"btl-tab-panel-inner"},(0,n.createElement)("h3",{className:"btl-tab-panel-header"},(0,s.__)("Choose an Option You want to Export","betterlinks")),(0,n.createElement)("form",{action:"admin.php?page="+t.get("page")+"&export=true&nonce="+w.sL,method:"POST"},(0,n.createElement)("div",{role:"group",className:"btl-radio-group","aria-labelledby":"my-radio-group"},(0,n.createElement)("div",null,(0,n.createElement)("label",{className:"btl-radio"},(0,n.createElement)("input",{type:"radio",name:"content",value:"links",checked:"links"===_,onChange:O}),(0,n.createElement)("span",null,(0,s.__)("Links","betterlinks")))),(0,n.createElement)("div",null,(0,n.createElement)("label",{className:"btl-radio"},(0,n.createElement)("input",{type:"radio",name:"content",value:"clicks",checked:"clicks"===_,onChange:O}),(0,n.createElement)("span",null,(0,s.__)("Analytics","betterlinks")))),(0,n.createElement)("div",null,(0,n.createElement)("label",{className:"btl-radio"},(0,n.createElement)("input",{type:"radio",name:"content",value:"simplecsvfile",checked:"simplecsvfile"===_,onChange:O}),(0,n.createElement)("span",null,(0,s.__)("Sample CSV File","betterlinks"))))),(0,n.createElement)("button",{type:"submit",className:"btl-export-download-button"},(0,s.__)("Export File","betterlinks")))),(0,n.createElement)("div",{className:"btl-tab-panel-inner"},(0,n.createElement)("h3",{className:"btl-tab-panel-header"},(0,s.__)("Choose the Plugin You Want to Import from","betterlinks")),(0,n.createElement)("form",{action:"admin.php?page="+t.get("page")+"&import=true&nonce="+w.sL,method:"POST",encType:"multipart/form-data"},(0,n.createElement)("div",{role:"group",className:"btl-radio-group","aria-labelledby":"my-radio-group"},(0,n.createElement)("div",null,(0,n.createElement)("label",{className:"btl-radio"},(0,n.createElement)("input",{type:"radio",name:"mode",value:"default",checked:"default"===c,onChange:N}),(0,n.createElement)("span",null,(0,s.__)("BetterLinks","betterlinks")))),(0,n.createElement)("div",null,(0,n.createElement)("label",{className:"btl-radio"},(0,n.createElement)("input",{type:"radio",name:"mode",value:"prettylinks",checked:"prettylinks"===c,onChange:N}),(0,n.createElement)("span",null,(0,s.__)("Pretty Links","betterlinks")))),(0,n.createElement)("div",null,(0,n.createElement)("label",{className:"btl-radio"},(0,n.createElement)("input",{type:"radio",name:"mode",value:"simple301redirects",checked:"simple301redirects"===c,onChange:N}),(0,n.createElement)("span",null,(0,s.__)("Simple 301 Redirects","betterlinks")))),(0,n.createElement)("div",null,(0,n.createElement)("label",{className:"btl-radio"},(0,n.createElement)("input",{type:"radio",name:"mode",value:"thirstyaffiliates",checked:"thirstyaffiliates"===c,onChange:N}),(0,n.createElement)("span",null,(0,s.__)("ThirstyAffiliates","betterlinks")))),"thirstyaffiliates"===c&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)("input",{name:"ta_prefix",id:"ta_prefix",type:"text",placeholder:"Link Prefix",value:b,onChange:function(e){return d(e.target.value)}})),(0,n.createElement)("p",{className:"btl-file-chooser"},(0,n.createElement)("label",{htmlFor:"upload"},(0,s.__)("Choose the File You Want to Import","betterlinks")),(0,n.createElement)("input",{type:"file",id:"upload_file",name:"upload_file",size:"25",required:!0})),(0,n.createElement)("p",{className:"submit",style:{display:"flex",alignItems:"center",columnGap:"5px"}},(0,n.createElement)("input",{type:"submit",name:"submit",id:"submit",className:"button button-primary",value:(0,s.__)("Import File","betterlinks"),disabled:""}),"default"!==c&&(null===w.MH||void 0===w.MH?void 0:w.MH[c])&&(0,n.createElement)(n.Fragment,null,(0,s.__)("Or","betterlinks"),(0,n.createElement)("input",{type:"button",className:"button button-primary",value:(0,s.__)("Migrate from Database","betterlinks"),onClick:A}))))),(0,n.createElement)("div",{id:"response"},Object.keys(h||{}).length>0&&(0,n.createElement)("div",{className:"btl-migration-logs"},(0,n.createElement)("div",{className:"btl-migration-logs__item"},Object.entries(h||{}).map((function(e){var t=(0,a.A)(e,2),l=t[0],r=t[1];return Array.isArray(r)?null==r?void 0:r.map((function(e,t){return(0,n.createElement)("span",{key:t},e)})):(0,n.createElement)("span",{key:l},r)}))))))))};var L=l(20312),D=l.n(L),T=function(){return(0,n.createElement)("svg",{height:25,width:25,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 511.182 511.182",fill:"#F9A946",style:{marginLeft:"5px"}},(0,n.createElement)("path",{d:"M436.623 74.482c-95.297-99.308-266.746-99.313-362.039.003-96.332 92.527-99.652 257.411-7.629 354.056a7.5 7.5 0 0 0 11.052-10.142C-63.205 265.068 47.079 13.962 255.606 14.503c129.533-2.671 243.68 111.455 240.995 241.001.961 199.912-234.049 313.784-390.303 189.182a7.5 7.5 0 1 0-9.301 11.769c97.909 80.188 251.709 71.178 339.625-19.935 99.309-95.283 99.314-266.754.001-362.038z"}),(0,n.createElement)("path",{d:"M255.603 446.502c-42.759 0-81.317-15.354-95.577-25.033a7.5 7.5 0 0 0-8.423 12.411c131.426 78.739 311.216-18.2 309.999-178.38.002-113.586-92.41-205.998-205.998-205.998a7.5 7.5 0 0 0 0 15c253.06 9.612 252.95 372.444-.001 382z"}),(0,n.createElement)("path",{d:"M138.579 255.562c-2.237 8.349.153 16.809 6.556 23.211l58.132 58.132c9.62 9.62 25.949 9.641 35.591 0l84.563-84.562a7.5 7.5 0 0 0 0-10.606 7.5 7.5 0 0 0-10.606 0l-84.563 84.562c-3.923 3.922-10.455 3.922-14.378 0l-58.132-58.132c-8.729-8.729 5.493-23.262 14.377-14.378l45.64 45.64a7.5 7.5 0 0 0 10.606 0l114.724-114.724c4.181-4.18 10.048-3.493 14.065.525 1.183 1.183 6.756 7.409.312 13.853l-14.612 14.612a7.5 7.5 0 0 0 0 10.606 7.5 7.5 0 0 0 10.606 0l14.612-14.612c22.664-23.515-12.121-58.017-35.59-35.591l-109.421 109.42-40.337-40.337c-13.824-13.822-37.244-5.91-42.145 12.381zM204.42 55.696c-9.652 0-9.668 15 0 15 9.652 0 9.668-15 0-15zM156.392 75.496c-9.652 0-9.668 15 0 15 9.652 0 9.668-15 0-15zM115.125 122.052c9.652 0 9.668-15 0-15-9.652 0-9.668 15 0 15zM83.431 148.213c-9.652 0-9.668 15 0 15 9.652 0 9.668-15 0-15zM63.47 196.175c-9.652 0-9.668 15 0 15 9.652 0 9.668-15 0-15zM56.603 247.669c-9.652 0-9.668 15 0 15 9.652 0 9.668-15 0-15zM63.298 314.185c9.652 0 9.668-15 0-15-9.652 0-9.668 15 0 15zM83.098 347.213c-9.652 0-9.668 15 0 15 9.651 0 9.667-15 0-15zM114.653 403.48c9.652 0 9.668-15 0-15-9.651 0-9.667 15 0 15z"}))};const M=function(e){var t=(0,n.useState)("Run Migration"),l=(0,a.A)(t,2),i=l[0],o=l[1],m=(0,n.useState)(!0),u=(0,a.A)(m,2),b=u[0],d=u[1],p=(0,n.useState)(!1),k=(0,a.A)(p,2),f=k[0],v=k[1],g=(0,n.useState)({}),h=(0,a.A)(g,2),y=h[0],N=h[1],O=(0,n.useState)({}),x=(0,a.A)(O,2),A=x[0],C=x[1],F=(0,n.useState)({}),P=(0,a.A)(F,2),S=P[0],j=P[1],L=(0,n.useState)({}),M=(0,a.A)(L,2),V=M[0],R=M[1],H=(0,c.useHistory)(),z=e.redirect,B=void 0===z||z;function W(){d(!1),B&&(H.push(w.Y3+"admin.php?page=betterlinks"),H.go(0))}return(0,n.useEffect)((function(){"simple301redirects"===e.mode?(v(!0),E().post("".concat(ajaxurl,"?action=betterlinks/admin/get_simple301redirects_data&security=").concat(w.sL)).then((function(e){e&&(C(e.data.data),v(!1))}),(function(e){console.log(e)}))):"prettylinks"===e.mode?(v(!0),E().post("".concat(ajaxurl,"?action=betterlinks/admin/get_prettylinks_data&security=").concat(w.sL)).then((function(e){e.data&&(N(e.data.data),v(!1))}),(function(e){console.log(e)}))):"thirstyaffiliates"===e.mode&&(v(!0),E().post("".concat(ajaxurl,"?action=betterlinks/admin/get_thirstyaffiliates_data&security=").concat(w.sL)).then((function(e){e.data&&(j(e.data.data),v(!1))}),(function(e){console.log(e)})))}),[]),(0,n.createElement)(r().Fragment,null,(0,n.createElement)(D(),{isOpen:b,shouldCloseOnOverlayClick:!1,onRequestClose:W,style:w.vu,ariaHideApp:!1},(0,n.createElement)("span",{className:"btl-close-modal",onClick:W},(0,n.createElement)("i",{className:"btl btl-cancel"})),0===Object.keys(V).length?(0,n.createElement)(_.l1,{initialValues:{checked:[]},onSubmit:function(t){return function(t){o((0,s.__)("Migration is in Progress...","betterlinks"));var l=new FormData;"prettylinks"===e.mode?l.append("action","betterlinks/admin/run_prettylinks_migration"):"simple301redirects"===e.mode?l.append("action","betterlinks/admin/run_simple301redirects_migration"):"thirstyaffiliates"===e.mode&&l.append("action","betterlinks/admin/run_thirstyaffiliates_migration"),l.append("security",w.sL),l.append("type",t.checked),E().post(ajaxurl,l).then((function(e){e.data&&(o((0,s.__)("Done!","betterlinks")),R(e.data.data))}),(function(e){console.log(e)}))}(t)}},(function(e){return e.values,(0,n.createElement)(_.lV,null,(0,n.createElement)("div",{className:"btl-modal-migration",role:"group","aria-labelledby":"checkbox-group"},f&&(0,n.createElement)("div",null,(0,s.__)("Please Wait...","betterlinks")),Object.keys(y).length>0&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)("h3",{className:"btl-modal-migration__title"},(0,s.__)("Pick Data that You want to Import","betterlinks")," ",(0,n.createElement)("img",{width:"25",src:w.hq+"assets/images/pointing-down.svg",alt:"icon"})),(0,n.createElement)("div",{className:"btl-modal-migration__item"},(null==y?void 0:y.links_count)>0&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)(_.D0,{id:"links",type:"checkbox",name:"checked",value:"links"}),(0,n.createElement)("label",{htmlFor:"links"},(0,s.__)("Links ","betterlinks"),"(".concat(y.links_count,")")))),(0,n.createElement)("div",{className:"btl-modal-migration__item"},(null==y?void 0:y.clicks_count)>0&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)(_.D0,{id:"clicks",type:"checkbox",name:"checked",value:"clicks"}),(0,n.createElement)("label",{htmlFor:"clicks"},(0,s.__)("Clicks ","betterlinks"),"(".concat(y.clicks_count,")"))))),Object.keys(A).length>0&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)("h3",{className:"btl-modal-migration__title"},(0,s.__)("Pick Data that You want to Import","betterlinks")," ",(0,n.createElement)("img",{width:"25",src:w.hq+"assets/images/pointing-down.svg",alt:"icon"})),(0,n.createElement)("div",{className:"btl-modal-migration__item"},A&&Object.keys(A).length>0&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)(_.D0,{id:"links",type:"checkbox",name:"checked",value:"links"}),(0,n.createElement)("label",{htmlFor:"links"},(0,s.__)("Links ","betterlinks"),"(".concat(Object.keys(A).length,")"))))),Object.keys(S).length>0&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)("h3",{className:"btl-modal-migration__title"},(0,s.__)("Pick Data that You want to Import","betterlinks")," ",(0,n.createElement)("img",{width:"25",src:w.hq+"assets/images/pointing-down.svg",alt:"icon"})),(0,n.createElement)("div",{className:"btl-modal-migration__item"},S&&Object.keys(S).length>0&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)(_.D0,{id:"links",type:"checkbox",name:"checked",value:"links"}),(0,n.createElement)("label",{htmlFor:"links"},(0,s.__)("Links ","betterlinks"),"(".concat(Object.keys(S).length,")"))))),y.links&&0==y.links.length&&y.clicks&&0==y.clicks.length?(0,n.createElement)("h3",null,(0,s.__)("Nothing Found To Import","betterlinks")):(0,n.createElement)("button",{className:"button button-primary",type:"submit"},i)))})):(0,n.createElement)("div",{className:"btl-modal-migration"},(0,n.createElement)("div",{id:"response"},(0,n.createElement)("h3",null,V.btl_prettylinks_migration_running_in_background?(0,n.createElement)(n.Fragment,null,(0,s.__)("Migration is running in the background ","betterlinks"),(0,n.createElement)(T,null)):(0,n.createElement)(n.Fragment,null,(0,s.__)("Migration is Complete","betterlinks"),(0,n.createElement)("img",{width:"25",src:w.hq+"assets/images/checkmark.svg",alt:"icon"}))),!V.btl_prettylinks_migration_running_in_background&&Object.entries(V).map((function(e){var t=(0,a.A)(e,2),l=(t[0],t[1]);return Object.entries(l).length>0&&Object.entries(l).map((function(e){var t=(0,a.A)(e,2),l=t[0],r=t[1];return(0,n.createElement)("div",{key:l},Array.isArray(r)?r.map((function(e,t){return(0,n.createElement)("div",{key:t},e)})):r)}))}))),(0,n.createElement)("p",{style:{textAlign:"left"}},(0,n.createElement)("button",{className:"button button-primary",type:"button",onClick:W},(0,s.__)("Ok","betterlinks"))))))};var V=l(23029),R=l(92901),H=l(56822),z=l(53954),B=l(85501);function W(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(W=function(){return!!e})()}var I=function(e){function t(e){var l,a,n,r;return(0,V.A)(this,t),a=this,n=t,r=[e],n=(0,z.A)(n),(l=(0,H.A)(a,W()?Reflect.construct(n,r||[],(0,z.A)(a).constructor):n.apply(a,r))).state={isOpenModal:!1},l.openModal=l.openModal.bind(l),l.closeModal=l.closeModal.bind(l),l}return(0,B.A)(t,e),(0,R.A)(t,[{key:"openModal",value:function(){this.setState({isOpenModal:!0})}},{key:"closeModal",value:function(){this.setState({isOpenModal:!1})}},{key:"render",value:function(){var e=this,t={editor:"Editor",author:"Author",contributor:"Contributor",subscriber:"Subscriber"};return(0,n.createElement)(r().Fragment,null,(0,n.createElement)(N.A,{isOpenModal:this.state.isOpenModal,closeModal:this.closeModal}),(0,n.createElement)("div",{className:"btl-tab-inner-divider-2"},(0,n.createElement)("div",{className:"btl-tab-panel-inner"},(0,n.createElement)("div",{className:"btl-role-container teaser"},(0,n.createElement)("form",{className:"form",onSubmit:function(t){t.preventDefault(),e.openModal()},id:"rolemanagement"},(0,n.createElement)("div",{className:"btl-role-item btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Who Can View Links?","betterlinks")," ",(0,n.createElement)(O.A,null)),(0,n.createElement)("div",{className:"writelinks link-options__body",onClick:function(){return e.openModal()}},Object.entries(t).map((function(e,t){var l=(0,a.A)(e,2),r=l[0],s=l[1];return(0,n.createElement)("label",{htmlFor:"viewlinks_".concat(t),key:"viewlinks_".concat(t),className:"btl-checkbox-field block"},(0,n.createElement)("input",{id:"viewlinks_".concat(t),type:"checkbox",name:"viewlinks",className:"btl-check",value:r,disabled:!0})," ",(0,n.createElement)("span",{className:"text"},s))})))),(0,n.createElement)("div",{className:"btl-role-item btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Who Can Create Links?","betterlinks")," ",(0,n.createElement)(O.A,null)),(0,n.createElement)("div",{className:"writelinks link-options__body",onClick:function(){return e.openModal()}},Object.entries(t).map((function(e,t){var l=(0,a.A)(e,2),r=l[0],s=l[1];return(0,n.createElement)("label",{htmlFor:"writelinks_".concat(t),key:"writelinks_".concat(t),className:"btl-checkbox-field block"},(0,n.createElement)("input",{id:"writelinks_".concat(t),type:"checkbox",name:"writelinks",className:"btl-check",value:r,disabled:!0})," ",(0,n.createElement)("span",{className:"text"},s))})))),(0,n.createElement)("div",{className:"btl-role-item btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Who Can Edit Links?","betterlinks")," ",(0,n.createElement)(O.A,null)),(0,n.createElement)("div",{className:"editlinks link-options__body",onClick:function(){return e.openModal()}},Object.entries(t).map((function(e,t){var l=(0,a.A)(e,2),r=l[0],s=l[1];return(0,n.createElement)("label",{htmlFor:"editlinks_".concat(t),key:"editlinks_".concat(t),className:"btl-checkbox-field block"},(0,n.createElement)("input",{id:"editlinks_".concat(t),type:"checkbox",name:"editlinks",className:"btl-check",value:r,disabled:!0})," ",(0,n.createElement)("span",{className:"text"},s))})))),(0,n.createElement)("div",{className:"btl-role-item btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Who Can Check Analytics?","betterlinks")," ",(0,n.createElement)(O.A,null)),(0,n.createElement)("div",{className:"checkanalytics link-options__body",onClick:function(){return e.openModal()}},Object.entries(t).map((function(e,t){var l=(0,a.A)(e,2),r=l[0],s=l[1];return(0,n.createElement)("label",{htmlFor:"checkanalytics_".concat(t),key:"checkanalytics_".concat(t),className:"btl-checkbox-field block"},(0,n.createElement)("input",{id:"checkanalytics_".concat(t),type:"checkbox",name:"checkanalytics",className:"btl-check",value:r,disabled:!0})," ",(0,n.createElement)("span",{className:"text"},s))})))),(0,n.createElement)("div",{className:"btl-role-item btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Who Can Edit Settings?","betterlinks")," ",(0,n.createElement)(O.A,null)),(0,n.createElement)("div",{className:"checkanalytics link-options__body",onClick:function(){return e.openModal()}},Object.entries(t).map((function(e,t){var l=(0,a.A)(e,2),r=l[0],s=l[1];return(0,n.createElement)("label",{htmlFor:"editsettings_".concat(t),key:"editsettings_".concat(t),className:"btl-checkbox-field block"},(0,n.createElement)("input",{id:"editsettings_".concat(t),type:"checkbox",name:"editsettings",className:"btl-check",value:r,disabled:!0})," ",(0,n.createElement)("span",{className:"text"},s))})))),(0,n.createElement)("div",{className:"btl-role-item btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,n.createElement)("span",null,(0,s.__)("Who can Mark Links as \n Favorite/Unfavorite?","betterlinks"),(0,n.createElement)(O.A,null))),(0,n.createElement)("div",{className:"editFavorite link-options__body",onClick:function(){return e.openModal()}},Object.entries(t).map((function(e,t){var l=(0,a.A)(e,2),r=l[0],s=l[1];return(0,n.createElement)("label",{htmlFor:"editFavorite_".concat(t),key:"editFavorite_".concat(t),className:"btl-checkbox-field block"},(0,n.createElement)("input",{id:"editFavorite_".concat(t),type:"checkbox",name:"editFavorite",className:"btl-check",value:r,disabled:!0}),(0,n.createElement)("span",{className:"text"},s))})))))))))}}])}(r().Component);function U(){return(0,n.createElement)(r().Fragment,null,(0,n.createElement)("div",{className:"btl-tab-inner-divider"},(0,n.createElement)("div",{className:"btl-tab-panel-inner"},(0,n.createElement)("div",{className:"btl-gopremium-container"},(0,n.createElement)("h3",{className:"btl-gopremium-title"},(0,s.__)("Why upgrade to ","betterlinks"),(0,n.createElement)("span",{className:"btl-gopremium-focus-text btl-text-orange"},"Premium Version?")),(0,n.createElement)("div",{className:"btl-gopremium-content"},(0,n.createElement)("p",null,(0,s.__)("Get access to Individual Analytics, Social Share for UTM Builder, Role Management, Google Analytics Integration & many more to run successful marketing campaigns.","betterlinks"))),(0,n.createElement)("div",{className:"btl-gopremium-footer"},(0,n.createElement)("p",null,(0,n.createElement)("img",{src:w.hq+"/assets/images/support.svg",alt:"support icon"}),(0,s.__)("World class support from our ","betterlinks"),(0,n.createElement)("span",{className:"btl-gopremium-focus-text btl-text-orange"},(0,s.__)(" dedicated team, 24/7.","betterlinks"))),(0,n.createElement)("a",{href:"https://wpdeveloper.com/in/upgrade-betterlinks",target:"_blank",title:(0,s.__)("Upgrade to PRO","betterlinks")},(0,n.createElement)("img",{src:w.hq+"/assets/images/crown.svg",alt:"crown icon"}),(0,s.__)("Upgrade to PRO","betterlinks")))))))}function q(e){return(0,n.createElement)(r().Fragment,null,(0,n.createElement)("div",{className:"btl-docs"},(0,n.createElement)("div",{className:"btl-doc"},(0,n.createElement)("div",{className:"btl-doc__icon"},(0,n.createElement)("img",{src:w.hq+"assets/images/doc.svg",alt:""})),(0,n.createElement)("h3",{className:"btl-doc__title"},(0,s.__)("Documentation","betterlinks")),(0,n.createElement)("p",{className:"btl-doc__content"},(0,s.__)("Get started by spending some time with the documentation to get familiar with BetterLinks. Create Shortened URLs and start cross-promoting your brands & products.","betterlinks")),(0,n.createElement)("a",{href:"https://betterlinks.io/docs/",className:"btl-doc__url",target:"_blank"},(0,s.__)("Documentation","betterlinks"),(0,n.createElement)("img",{src:w.hq+"assets/images/arrow-right.svg",alt:""}))),(0,n.createElement)("div",{className:"btl-doc"},(0,n.createElement)("div",{className:"btl-doc__icon"},(0,n.createElement)("img",{src:w.hq+"assets/images/user.svg",alt:""})),(0,n.createElement)("h3",{className:"btl-doc__title"},(0,s.__)("Need Help?","betterlinks")),(0,n.createElement)("p",{className:"btl-doc__content"},(0,s.__)("Stuck with something? Feel free to reach out to our Live Chat agent or create a support ticket.","betterlinks")),(0,n.createElement)("a",{href:"https://wpdeveloper.com/support/",className:"btl-doc__url",target:"_blank"},(0,s.__)("Get Help","betterlinks"),(0,n.createElement)("img",{src:w.hq+"assets/images/arrow-right.svg",alt:""}))),(0,n.createElement)("div",{className:"btl-doc"},(0,n.createElement)("div",{className:"btl-doc__icon"},(0,n.createElement)("img",{src:w.hq+"assets/images/community.svg",alt:""})),(0,n.createElement)("h3",{className:"btl-doc__title"},(0,s.__)("Join the Community","betterlinks")),(0,n.createElement)("p",{className:"btl-doc__content"},(0,s.__)("Join the Facebook community and discuss with fellow developers and users. Best way to connect with people and get feedback on your projects.","betterlinks")),(0,n.createElement)("a",{href:"https://www.facebook.com/groups/wpdeveloper.net/",className:"btl-doc__url",target:"_blank"},(0,s.__)("Join the Community","betterlinks"),(0,n.createElement)("img",{src:w.hq+"assets/images/arrow-right.svg",alt:""}))),(0,n.createElement)("div",{className:"btl-doc"},(0,n.createElement)("div",{className:"btl-doc__icon"},(0,n.createElement)("img",{src:w.hq+"assets/images/heart.svg",alt:""})),(0,n.createElement)("h3",{className:"btl-doc__title"},(0,s.__)("Show Your Love","betterlinks")),(0,n.createElement)("p",{className:"btl-doc__content"},(0,s.__)("We love to have you in BetterLinks family. We are making it more awesome everyday. Take your 2 minutes to review the plugin and spread the love to encourage us to keep it going.","betterlinks")),(0,n.createElement)("a",{href:"https://wpdeveloper.com/review-betterlinks",className:"btl-doc__url",target:"_blank"},(0,s.__)("Leave a Review","betterlinks"),(0,n.createElement)("img",{src:w.hq+"assets/images/arrow-right.svg",alt:""})))))}q.propTypes={};var Y=l(80702),J=l(80045),G=l(46005),K=["title","is_pro","isMulti","defaultValue"];function Q(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}var X={value:"",label:(0,s.__)("Select...","betterlinks")};const Z=function(e){var t=e.title,l=void 0===t?"":t,a=e.is_pro,r=void 0!==a&&a,s=e.isMulti,i=void 0!==s&&s,c=e.defaultValue,o=void 0===c?X:c,m=(0,J.A)(e,K);return(0,n.createElement)("div",function(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?Q(Object(l),!0).forEach((function(t){(0,k.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):Q(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}({className:"btl-role-item btl-form-group"},m),(0,n.createElement)("label",{className:"btl-form-label"},l,r&&(0,n.createElement)("span",{class:"pro-badge"},"Pro")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)(G.Ay,{isMulti:i,isDisabled:!0,defaultValue:o,className:"btl-modal-select--full",classNamePrefix:"btl-react-select"})))};var $=l(58987);const ee=function(e){var t,l,r=e.props;if(null!==(t=r.values)&&void 0!==t&&null!==(t=t.cle)&&void 0!==t&&t.enable_cle&&null!==(l=r.values)&&void 0!==l&&null!==(l=l.cle)&&void 0!==l&&l.advanced_options){var i=(0,Y.c)(),c=(0,a.A)(i,3),o=c[0],m=c[1],u=c[2];return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(N.A,{isOpenModal:o,closeModal:u}),(0,n.createElement)("div",{className:"btl-cle-teaser",onClick:m},(0,n.createElement)($.A,{title:(0,s.__)("No Follow","betterlinks")}),(0,n.createElement)($.A,{title:(0,s.__)("Sponsored","betterlinks")}),(0,n.createElement)($.A,{title:(0,s.__)("Parameter Forwarding","betterlinks")}),(0,n.createElement)($.A,{title:(0,s.__)("Tracking","betterlinks")}),(0,n.createElement)($.A,{title:(0,s.__)("Social Share","betterlinks")}),(0,n.createElement)("div",{className:"btl-cle-select-teaser"},(0,n.createElement)(Z,{title:(0,s.__)("BetterLink Category")}),(0,n.createElement)(Z,{title:(0,s.__)("Redirect Type")}))))}};function te(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}function le(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?te(Object(l),!0).forEach((function(t){(0,k.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):te(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}const ae=(0,o.Ng)(null,(function(e){return{update_option:(0,m.zH)(u.YD,e)}}))((function(e){var t=e.settings,l=e.terms,r=e.update_option,i=(0,n.useState)((0,s.__)("Save Settings","betterlinks")),c=(0,a.A)(i,2),o=c[0],m=c[1],u=(0,w.OS)("1.9.4");return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(_.l1,{enableReinitialize:!0,initialValues:le({},t),onSubmit:function(e){var t;!w.l3&&null!=e&&null!==(t=e.cle)&&void 0!==t&&t.enable_cle||(0,w.PW)(e,r,m)}},(function(e){var a,r,i,c,m;return(0,n.createElement)(_.lV,{className:"btl-cle"},(0,n.createElement)(re,null),(null===(a=e.values)||void 0===a||null===(a=a.cle)||void 0===a?void 0:a.enable_cle)&&(0,n.createElement)(se,{cle:null===(r=e.values)||void 0===r?void 0:r.cle}),(0,n.createElement)(ne,{props:e,isLatestVersion:u}),(null===(i=e.values)||void 0===i||null===(i=i.cle)||void 0===i?void 0:i.enable_cle)&&(null===(c=e.values)||void 0===c||null===(c=c.cle)||void 0===c?void 0:c.advanced_options)&&u&&betterLinksHooks.applyFilters("betterLinksCleAdvanced",(0,n.createElement)(ee,{props:e}),le(le({},e),{},{settings:t,terms:l,redirectType:y.XN,Select2:G.Ay})),!u&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)(ee,{props:e}),(0,n.createElement)("div",{className:"btl-form-group"},(0,n.createElement)("div",{className:"short-description"},(0,n.createElement)("b",{style:{fontWeight:700}},(0,s.__)("Note: ")),(0,s.__)("To Configure the Advanced Options, kindly ensure that you have updated to the latest version of BetterLinks Pro","betterlinks")))),(0,n.createElement)("button",{className:"button-primary btn-save-settings",type:"submit",style:{cursor:w.l3||null===(m=e.values)||void 0===m||null===(m=m.cle)||void 0===m||!m.enable_cle?"pointer":"not-allowed"}},o))})))}));var ne=function(e){var t,l,r,i,c,o=e.props,m=e.isLatestVersion,u=(0,Y.c)(),b=(0,a.A)(u,3),d=b[0],p=b[1],k=b[2];return(0,n.useEffect)((function(){var e,t=null==o||null===(e=o.values)||void 0===e?void 0:e.cle;"string"==typeof t&&(t=JSON.parse(t)),t&&"powered_by"in t||o.setFieldValue("cle.powered_by",!0)}),[]),(0,n.createElement)(n.Fragment,null,(0,n.createElement)(N.A,{isOpenModal:d,closeModal:k}),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label",style:{"min-width":"120px"}},(0,s.__)("Enable Quick Link Creation","betterlinks")),(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)("input",{className:"btl-check",name:"cle.enable_cle",type:"checkbox",onChange:function(e){return o.setFieldValue("cle.enable_cle",e.target.checked)},checked:null===(t=o.values)||void 0===t||null===(t=t.cle)||void 0===t?void 0:t.enable_cle}),(0,n.createElement)("span",{className:"text"},(0,s.__)("","betterlinks"))))),(null===(l=o.values)||void 0===l||null===(l=l.cle)||void 0===l?void 0:l.enable_cle)&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label",style:{"min-width":"120px"}},(0,s.__)("Enable Powered By","betterlinks")),(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)("input",{className:"btl-check",name:"cle.powered_by",type:"checkbox",onChange:function(e){return o.setFieldValue("cle.powered_by",e.target.checked)},checked:null===(r=o.values)||void 0===r||null===(r=r.cle)||void 0===r?void 0:r.powered_by}),(0,n.createElement)("span",{className:"text"},(0,s.__)("","betterlinks"))))),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label",style:{"min-width":"120px"}},(0,s.__)("Advanced Options","betterlinks"),!w.JT&&(0,n.createElement)("span",{onClick:p,className:"pro-badge"},"Pro")),(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},w.JT&&m?(0,n.createElement)(n.Fragment,null,(0,n.createElement)("input",{className:"btl-check",name:"cle.advanced_options",type:"checkbox",onChange:function(e){return o.setFieldValue("cle.advanced_options",e.target.checked)},checked:null===(i=o.values)||void 0===i||null===(i=i.cle)||void 0===i?void 0:i.advanced_options}),(0,n.createElement)("span",{className:"text"},(0,s.__)("","betterlinks"))):(0,n.createElement)("span",{onClick:function(){var e;return o.setFieldValue("cle.advanced_options",!(null!==(e=o.values)&&void 0!==e&&null!==(e=e.cle)&&void 0!==e&&e.advanced_options))},className:"dashicons dashicons-arrow-".concat(null!==(c=o.values)&&void 0!==c&&null!==(c=c.cle)&&void 0!==c&&c.advanced_options?"up":"down","-alt2")}))))))},re=function(){return(0,n.createElement)("div",{className:"btl-form-group"},(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)("div",{className:"short-description"},(0,n.createElement)("b",{style:{fontWeight:700}},(0,s.__)("Note","betterlinks"),": "),(0,n.createElement)("span",null,(0,s.__)("It will allow you to create ","betterlinks")),(0,n.createElement)("span",null,(0,n.createElement)("strong",null,(0,s.__)("Quick Link ","betterlinks"))),(0,n.createElement)("span",null,(0,s.__)("directly from your bookmark. For more info, ","betterlinks")),(0,n.createElement)("a",{className:"external-analytic-tooltip-anchor",href:"https://betterlinks.io/docs/configure-quick-link-creation/",target:"_blank",style:{color:"inherit"}},(0,s.__)("Click here","betterlinks")))))},se=function(){return w.l3?(0,n.createElement)("div",{className:"btl-cle-dragable-section"},(0,n.createElement)("a",{onClick:function(e){return e.preventDefault()},href:"javascript:location.href='".concat(w.IV,"/index.php?action=btl_cle&api_key=").concat(w.l3,"&target_url='+escape(location.href)+'&title='+document.title"),className:"button button-primary"},(0,s.__)("Quick Link Creation","betterlinks")),(0,n.createElement)("span",null,(0,s.__)("Just Drag & Drop this button in your bookmark","betterlinks"))):(0,n.createElement)("div",{className:"notice notice-error",style:{marginLeft:"0",marginBottom:"15px",padding:"10px"}},(0,s.__)("'AUTH_KEY' is missing in your wp-config.php file. Please ensure that AUTH_KEY is defined in your wp-config.php file. For more info, ","betterlinks"),(0,n.createElement)("a",{className:"external-analytic-tooltip-anchor",href:"https://betterlinks.io/docs/configure-quick-link-creation/#8-toc-title",target:"_blank",style:{color:"inherit"}},(0,s.__)("Click here","betterlinks")))},ie=l(45708),ce=l.n(ie);function oe(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}function me(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?oe(Object(l),!0).forEach((function(t){(0,k.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):oe(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}const ue=(0,o.Ng)(null,(function(e){return{update_option:(0,m.zH)(u.YD,e)}}))((function(e){var t=e.settings,l=e.update_option,r=e.postTypes,i=(0,n.useState)((0,s.__)("Save Settings","betterlinks")),c=(0,a.A)(i,2),o=c[0],m=c[1],u=(0,Y.c)(),b=(0,a.A)(u,3),d=b[0],p=b[1],k=b[2];return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(N.A,{isOpenModal:d,closeModal:k}),(0,n.createElement)(_.l1,{enableReinitialize:!0,initialValues:me({},t),onSubmit:function(e){return(0,w.PW)(e,l,m)}},(function(e){return(0,n.createElement)(_.lV,null,!w.JT&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)($.A,{title:(0,s.__)("Affiliate Link Disclosure","betterlinks"),onClick:p}),(0,n.createElement)($.A,{title:(0,s.__)("Enable Preview","betterlinks"),onClick:p}),(0,n.createElement)(Z,{title:(0,s.__)("Enable by Default","betterlinks"),onClick:p}),(0,n.createElement)(Z,{title:(0,s.__)("Disclosure Position","betterlinks"),onClick:p}),(0,n.createElement)("div",{className:"btl-role-item btl-form-group",style:{marginBottom:"60px"}},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Disclosure Content","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)(ce(),{theme:"snow",value:"",onChange:p}))),(0,n.createElement)($.A,{title:(0,s.__)("Advanced Options","betterlinks"),onClick:p})),betterLinksHooks.applyFilters("BetterLinksOptionsTabSettings",null,me(me({},e),{},{postTypes:r,ReactQuill:ce()})),w.JT&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)("button",{className:"button-primary btn-save-settings",type:"submit"},o)))})))}));function be(e){var t=e.autoCreateLinkSettings,l=e.terms,a=e.setAutoCreateLinkSettings;return betterLinksHooks.applyFilters("BetterLinksAutoCreateLinksPro",(0,n.createElement)(de,null),{autoCreateLinkSettings:t,terms:l,setAutoCreateLinkSettings:a})}var de=function(){var e=(0,Y.c)(),t=(0,a.A)(e,3),l=t[0],r=t[1],i=t[2];return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(N.A,{isOpenModal:l,closeModal:i}),(0,n.createElement)(_.l1,null,(0,n.createElement)(_.lV,null,(0,n.createElement)($.A,{title:(0,s.__)("Enable Auto-Create Links","betterlinks"),onClick:r}),(0,n.createElement)($.A,{title:(0,s.__)("Post Shortlinks","betterlinks"),onClick:r}),(0,n.createElement)(Z,{title:(0,s.__)("BetterLinks Category"),onClick:r}),(0,n.createElement)($.A,{title:(0,s.__)("Page Shortlinks","betterlinks"),onClick:r}),(0,n.createElement)(Z,{title:(0,s.__)("BetterLinks Category"),onClick:r}))))};function pe(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}function ke(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?pe(Object(l),!0).forEach((function(t){(0,k.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):pe(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}const _e=(0,o.Ng)(null,(function(e){return{update_option:(0,m.zH)(u.YD,e)}}))((function(e){var t=e.settings,l=e.update_option,r=(0,n.useState)((0,s.__)("Save Settings","betterlinks")),i=(0,a.A)(r,2),c=i[0],o=i[1],m=(0,Y.c)(),u=(0,a.A)(m,3),b=u[0],d=u[1],p=u[2];return(0,w.OS)("1.8.0")?(0,n.createElement)(n.Fragment,null,(0,n.createElement)(N.A,{isOpenModal:b,closeModal:p}),(0,n.createElement)(_.l1,{enableReinitialize:!0,initialValues:ke({},t),onSubmit:function(e){return(0,w.PW)(e,l,o)}},(function(e){return(0,n.createElement)(_.lV,null,!w.JT&&(0,n.createElement)($.A,{title:(0,s.__)("Enable Customize Link Preview","betterlinks"),onClick:d}),betterLinksHooks.applyFilters("BetterLinksCustomizeMetaTags",null,e),w.JT&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)("button",{className:"button-primary btn-save-settings",type:"submit"},c)))}))):(0,n.createElement)("div",{className:"btl-form-group"},(0,n.createElement)("div",{className:"short-description"},(0,n.createElement)("b",{style:{fontWeight:700}},(0,s.__)("Note: ")),(0,s.__)("To Utilize the Customize Link Preview Feature, kindly ensure that you have updated to the latest version of BetterLinks Pro v-1.8.0","betterlinks")))})),fe=function(e){var t=e.title,l=void 0===t?"":t,a=e.onClick,r=void 0===a?function(){}:a,s=e.placeholder,i=void 0===s?"":s;return(0,n.createElement)("div",{className:"btl-role-item btl-form-group",onClick:r},(0,n.createElement)("label",{className:"btl-form-label"},l),(0,n.createElement)("div",{className:"link-options__body link-options__body_tracking"},(0,n.createElement)("input",{type:"text",className:"btl-text-field",disabled:!0,onClick:r,placeholder:i}),(0,n.createElement)("span",{className:"text"})))};function Ee(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}function ve(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?Ee(Object(l),!0).forEach((function(t){(0,k.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):Ee(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}const ge=(0,o.Ng)(null,(function(e){return{update_tracking_settings:(0,m.zH)(u.OK,e)}}))((function(e){var t=e.trackingSettings,l=e.update_tracking_settings,r=(0,Y.c)(),i=(0,a.A)(r,3),c=i[0],o=i[1],m=i[2];return w.JT?betterLinksHooks.applyFilters("BetterLinksTrackingPro",null,ve(ve({},t),{},{update_tracking_settings:l})):(0,n.createElement)(n.Fragment,null,(0,n.createElement)(N.A,{isOpenModal:c,closeModal:m}),(0,n.createElement)("div",{className:"btl-tab-inner-divider"},(0,n.createElement)("div",{className:"btl-tracking-settings"},(0,n.createElement)("div",{className:"btl-external-analytics-container btl-googleanalytics-container teaser"},(0,n.createElement)("form",{className:"form",id:"googleAnalytics",onSubmit:function(e){e.preventDefault(),o()},action:"#"},(0,n.createElement)("div",{className:"btl-role-item btl-form-group",onClick:function(){return o()}},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Enable Google Analytics","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field"},(0,n.createElement)("input",{type:"checkbox",className:"btl-check",name:"is_enable_ga",disabled:!0}),(0,n.createElement)("span",{className:"text"})))))),(0,n.createElement)("div",{className:"btl-external-analytics-container btl-fb-pixel-container teaser"},(0,n.createElement)("form",{className:"form",id:"fbPixel",onSubmit:function(e){e.preventDefault(),o()},action:"#"},(0,n.createElement)("div",{className:"btl-role-item btl-form-group",onClick:function(){return o()}},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Enable Facebook Pixel Tracking","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field"},(0,n.createElement)("input",{type:"checkbox",className:"btl-check",name:"is_enable_pixel",disabled:!0}),(0,n.createElement)("span",{className:"text"})))))),(0,n.createElement)(fe,{title:(0,s.__)("Custom Scripts","betterlinks"),onClick:o}),(0,n.createElement)("span",{className:"btl-form-group btl-multi-checkbox",style:{alignItems:"baseline"}},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Parameter Tracking","betterlinks")),(0,n.createElement)("div",{className:"link-options__body",onClick:o},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)("input",{className:"btl-check",type:"checkbox",disabled:!0}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Forwarded Parameters","betterlinks"))),(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)("input",{className:"btl-check",type:"checkbox",disabled:!0}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Target URL Parameters","betterlinks"))),(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)("input",{className:"btl-check",type:"checkbox",disabled:!0}),(0,n.createElement)("span",{className:"text"},(0,s.__)("UTM Parameters","betterlinks"))))))))}));function he(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}function ye(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?he(Object(l),!0).forEach((function(t){(0,k.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):he(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}const Ne=(0,o.Ng)(null,(function(e){return{update_option:(0,m.zH)(u.YD,e)}}))((function(e){var t=e.settings,l=e.update_option,r=(0,n.useState)((0,s.__)("Save Settings","betterlinks")),i=(0,a.A)(r,2),c=i[0],o=i[1],m=(0,Y.c)(),u=(0,a.A)(m,3),b=u[0],d=u[1],p=u[2];return(0,w.OS)("1.6.3")?(0,n.createElement)(n.Fragment,null,(0,n.createElement)(N.A,{isOpenModal:b,closeModal:p}),(0,n.createElement)(_.l1,{enableReinitialize:!0,initialValues:ye({},t),onSubmit:function(e){return(0,w.PW)(e,l,o)}},(function(e){return(0,n.createElement)(_.lV,null,betterLinksHooks.applyFilters("BetterLinksPasswordProtection",(0,n.createElement)(we,{openUpgradeToProModal:d}),ye(ye({},e),{},{ReactQuill:ce()})),w.JT&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)("button",{className:"button-primary btn-save-settings",type:"submit"},c)))}))):(0,n.createElement)("div",{className:"btl-form-group"},(0,n.createElement)("div",{className:"short-description"},(0,n.createElement)("b",{style:{fontWeight:700}},(0,s.__)("Note: ")),(0,s.__)("To Utilize the Password Protected Redirect Feature, kindly ensure that you have updated to the latest version of BetterLinks Pro","betterlinks")))}));var we=function(e){var t=e.openUpgradeToProModal;return(0,n.createElement)(n.Fragment,null,(0,n.createElement)($.A,{title:(0,s.__)("Password Protected Redirect","betterlinks"),onClick:t}),(0,n.createElement)($.A,{title:(0,s.__)("Enable Cookie","betterlinks"),onClick:t}),(0,n.createElement)($.A,{title:(0,s.__)("Advanced Settings","betterlinks"),onClick:t}),(0,n.createElement)(Z,{title:(0,s.__)("Form Template","betterlinks"),onClick:t}),(0,n.createElement)($.A,{title:(0,s.__)("Enable Title","betterlinks"),onClick:t}),(0,n.createElement)($.A,{title:(0,s.__)("Enable Instruction","betterlinks"),onClick:t}),(0,n.createElement)($.A,{title:(0,s.__)("Show Protected URL","betterlinks"),onClick:t}))},Oe=l(66087),xe=l.n(Oe);function Ae(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}function Ce(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?Ae(Object(l),!0).forEach((function(t){(0,k.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):Ae(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}var Fe={overlay:{background:"rgba(35, 40, 45, 0.62)"},content:{top:"50%",left:"50%",right:"auto",bottom:"auto",width:"auto",marginRight:"-50%",transform:"translate(-50%, -50%)"}};const Pe=(0,o.Ng)(null,(function(e){return{update_option:(0,m.zH)(u.YD,e)}}))((function(e){var t=e.settings,l=e.update_option,r=(0,n.useState)((0,s.__)("Save Settings","betterlinks")),i=(0,a.A)(r,2),c=i[0],o=i[1],m=(0,n.useState)(!1),u=(0,a.A)(m,2),b=u[0],d=u[1],k=function(){d(!1)};return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(_.l1,{initialValues:Ce({},t),onSubmit:function(e,a){var n=a.setFieldError,r=xe().map((null==e?void 0:e.customFields)||[],"value"),i=xe().some(r,(function(e){return!e||""===e||e.includes(" ")}));if(0!==r.length||0!==t.customFields.length)if(i)n("customFields",(0,s.__)("Please fill all the fields","betterlinks"));else{var c=(0,p.A)(new Set(r));JSON.stringify(r)===JSON.stringify(c)?(0,w.PW)(e,l,o):n("customFields",(0,s.__)("Field label must be unique","betterlinks"))}else n("customFields",(0,s.__)("Please create at least one Custom Field to save.","betterlinks"))}},(function(e){var t=e.values;return(0,n.createElement)(_.lV,null,(0,n.createElement)("div",{className:"btl-form-group"},(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)("div",{className:"short-description"},(0,n.createElement)("b",{style:{fontWeight:700}},"Note: "),(0,n.createElement)("span",null,(0,s.__)("It will allow you to add Custom Text Fields to store additional information alongside BetterLinks default fields for each link. For more info, ")),(0,n.createElement)("a",{className:"external-analytic-tooltip-anchor",href:"https://betterlinks.io/docs/add-custom-fields-in-betterlinks/",target:"_blank",style:{color:"inherit"}},(0,s.__)("Click here","betterlinks-pro"))))),(0,n.createElement)(_.ED,{name:"customFields",render:function(e){var l,a,r,i,c,o,m,u=!(null!=t&&null!==(l=t.customFields)&&void 0!==l&&l.length)||(null==t||null===(a=t.customFields)||void 0===a||null===(a=a[(null==t||null===(r=t.customFields)||void 0===r?void 0:r.length)-1])||void 0===a?void 0:a.value),p=(null==t||null===(i=t.customFields)||void 0===i?void 0:i.length)-1;return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(D(),{isOpen:b,onRequestClose:k,style:Fe,ariaHideApp:!1},(0,n.createElement)("div",null,(0,n.createElement)("span",{className:"btl-close-modal",onClick:k},(0,n.createElement)("i",{className:"btl btl-cancel"})),(0,n.createElement)("div",{className:"btl-confirmation-alert"},(0,n.createElement)("h3",{className:"btl-modal-utm-builder__title"},(0,s.__)("Are you sure to delete this field?","betterlinks")),(0,n.createElement)("div",{className:"btl-confirmation-buttons"},(0,n.createElement)("button",{type:"button",onClick:function(){var t,l=null===(t=e.form)||void 0===t||null===(t=t.errors)||void 0===t?void 0:t.deleteFieldIndex;e.remove(l),k()}},(0,s.__)("Yes","betterlinks")),(0,n.createElement)("button",{type:"button",onClick:k},(0,s.__)("Cancel","betterlinks")))))),(0,n.createElement)("div",null,(null==t||null===(c=t.customFields)||void 0===c?void 0:c.length)>0?t.customFields.map((function(t,l){return(0,n.createElement)("div",{key:"".concat(t.value,"_").concat(l),className:"btl-form-group",style:{columnGap:"5px"}},(0,n.createElement)(_.D0,{className:"btl-form-control",name:"customFields.".concat(l,".label"),placeholder:"Custom field label",onChange:function(t){var a=(0,w.z9)(t.target.value);e.form.setFieldValue("customFields.".concat(l,".label"),t.target.value),e.form.setFieldValue("customFields.".concat(l,".value"),a)},autoFocus:!0}),(0,n.createElement)("div",{className:"btl-utm-action-btns"},(0,n.createElement)("button",{className:"button",type:"button",style:{lineHeight:"0"},onClick:function(){d(!0),e.form.setFieldError("deleteFieldIndex",l)}},(0,n.createElement)("span",{className:"dashicons dashicons-trash"}))),p===l&&(0,n.createElement)("button",{type:"button",className:"button",style:{lineHeight:"0"},onClick:function(){u&&!xe().includes(u," ")?e.push(""):e.form.setFieldError("customFields",(0,s.__)("Please fill all the fields","betterlinks"))}},(0,n.createElement)("span",{className:"dashicons dashicons-plus-alt2"})))})):(0,n.createElement)("div",{className:"btl-form-group",style:{columnGap:"5px"}},(0,n.createElement)(_.D0,{className:"btl-form-control",name:"customFields.0.label",placeholder:(0,s.__)('To create Custom Field Label, click on "+" icon here 👉',"betterlinks"),disabled:!0,style:{cursor:"not-allowed"},onChange:function(t){var l=(0,w.z9)(t.target.value);e.form.setFieldValue("customFields.0.label",t.target.value),e.form.setFieldValue("customFields.0.value",l)}}),(0,n.createElement)("button",{type:"button",className:"button",style:{lineHeight:"0"},onClick:function(){return e.push("")}},(0,n.createElement)("span",{className:"dashicons dashicons-plus-alt2"})))),!(null===(o=e.form)||void 0===o||null===(o=o.errors)||void 0===o||!o.customFields)&&(0,n.createElement)("span",{style:{color:"red",display:"block",marginTop:"5px"}},null===(m=e.form)||void 0===m||null===(m=m.errors)||void 0===m?void 0:m.customFields))}}),(0,n.createElement)("button",{className:"button-primary btn-save-settings",type:"submit",style:{marginTop:"20px"}},c))})))}));var Se=l(10700);function je(e,t){var l=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),l.push.apply(l,a)}return l}function Le(e){for(var t=1;t<arguments.length;t++){var l=null!=arguments[t]?arguments[t]:{};t%2?je(Object(l),!0).forEach((function(t){(0,k.A)(e,t,l[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(l)):je(Object(l)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(l,t))}))}return e}var De={tooltipText:{width:"255px",textAlign:"left",lineHeight:"1.2em"},tooltipTextAnchor:{color:"inherit"}},Te=[{value:"task_delete",label:"Task Delete"},{value:"task_archive",label:"Task Archive"}];const Me=(0,o.Ng)(null,(function(e){return{update_option:(0,m.zH)(u.YD,e),fetch_terms_data:(0,m.zH)(h.M3,e)}}))((function(e){var t=e.settings,l=e.terms,r=e.update_option,i=e.fetch_terms_data,c=(0,n.useState)((0,s.__)("Save Settings","betterlinks")),o=(0,a.A)(c,2),m=o[0],u=o[1];return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(_.l1,{enableReinitialize:!0,initialValues:Le({},t),onSubmit:function(e){(0,w.PW)(e,r,u),i()}},(function(e){var t,a=e.values.fbs,r=Te.filter((function(e){return e.value===((null==a?void 0:a.delete_on)||"task_delete")}));return(0,n.createElement)(_.lV,{className:"btl-fbs"},(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label",style:{"min-width":"245px"}},(0,s.__)("Enable Link Management","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip",style:{marginLeft:"5px"}},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext",style:De.tooltipText},(0,s.__)("Enabling this option will allow you to create short links for tasks directly within the Fluent Boards. For more info, ","betterlinks"),(0,n.createElement)("a",{href:"https://betterlinks.io/docs/fluent-boards-link-management-with-betterlinks",target:"_blank",style:De.tooltipTextAnchor},(0,s.__)("Click Here","betterlinks"))))),(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)("input",{className:"btl-check",name:"fbs.enable_fbs",type:"checkbox",onChange:function(t){return e.setFieldValue("fbs.enable_fbs",t.target.checked)},checked:null==a?void 0:a.enable_fbs}),(0,n.createElement)("span",{className:"text"},(0,s.__)("","betterlinks"))))),(null===(t=e.values)||void 0===t||null===(t=t.fbs)||void 0===t?void 0:t.enable_fbs)&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label",style:{"min-width":"245px"}},(0,s.__)("Choose a Default Category","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip",style:{marginLeft:"5px"}},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext",style:De.tooltipText},(0,s.__)("This category will be assigned by default when you create links inside Fluent Boards for your tasks. You can manage your links from BetterLinks Dashboard afterwards.","betterlinks")))),(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)(Se.A,{catId:parseInt(null==a?void 0:a.cat_id),data:{terms:l},fieldName:"fbs.cat_id",setFieldValue:e.setFieldValue}))),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label",style:{"min-width":"245px"}},(0,s.__)("Show Category on Dashboard","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip",style:{marginLeft:"5px"}},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext",style:De.tooltipText},(0,s.__)("Enable this option to display the selected category in Manage Links.","betterlinks")))),(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)("input",{className:"btl-check",name:"fbs.show_fbs_category",type:"checkbox",onChange:function(t){return e.setFieldValue("fbs.show_fbs_category",t.target.checked)},checked:null==a?void 0:a.show_fbs_category}),(0,n.createElement)("span",{className:"text"},(0,s.__)("","betterlinks"))))),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label",style:{"min-width":"245px"}},(0,s.__)("Delete Link on","betterlinks"),(0,n.createElement)("div",{className:"btl-tooltip",style:{marginLeft:"5px"}},(0,n.createElement)("span",{className:"dashicons dashicons-info-outline"}),(0,n.createElement)("span",{className:"btl-tooltiptext",style:De.tooltipText},(0,s.__)("By selecting these options, you can specify when to delete Fluent Board Links created by BetterLinks.","betterlinks")))),(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)(G.Ay,{className:"btl-modal-select--full",classNamePrefix:"btl-react-select",id:"fbs.delete_on",name:"fbs.delete_on",defaultValue:r,onChange:function(t){return null===t?e.setFieldValue("fbs.delete_on",""):e.setFieldValue("fbs.delete_on",t.value)},options:Te,value:r,isMulti:!1})))),(0,n.createElement)("button",{className:"button-primary btn-save-settings",type:"submit"},m))})))}));var Ve=l(5051);const Re=(0,o.Ng)(null,(function(e){return{update_option:(0,m.zH)(u.YD,e)}}))((function(e){var t=e.settings,l=e.postdatas,a=e.update_option;return betterLinksHooks.applyFilters("BetterLinksAutoLinkKeywords",(0,n.createElement)(He,null),{settings:t,postdatas:l,update_option:a,saveSettingsHandler:w.PW})}));var He=function(){var e=(0,Y.c)(),t=(0,a.A)(e,3),l=t[0],r=t[1],i=t[2];return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(N.A,{isOpenModal:l,closeModal:i}),(0,n.createElement)(Ve.A,{notice:(0,s.__)("To Utilize the Auto-Link Keywords Feature, kindly ensure that you have at least BetterLinks Pro v2.0.2 installed & activated","betterlinks"),compatibleProVersion:"2.0.2"}),(0,n.createElement)("div",{className:"btl-form-group"},(0,n.createElement)("div",{className:"btl-form-field"},(0,n.createElement)("div",{className:"short-description"},(0,n.createElement)("b",{style:{fontWeight:700}},(0,s.__)("Note: ","betterlinks-pro")),(0,n.createElement)("span",null,(0,s.__)("The following settings will work as a preset for your new keyword added afterward","betterlinks-pro"))))),(0,n.createElement)("span",{className:"btl-form-group btl-form-group--teaser btl-form-group-autolink-keyword-icon"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Disable Auto-Link Keywords","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block",onClick:r},(0,n.createElement)("input",{className:"btl-check",name:"disable_autolink",type:"checkbox",disabled:!0}),(0,n.createElement)("span",{className:"text"})))),(0,n.createElement)("span",{className:"btl-form-group"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("HTML Options","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block",onClick:r},(0,n.createElement)("input",{type:"checkbox",disabled:!0,className:"btl-check"}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Open New Tab","betterlinks"))),(0,n.createElement)("label",{className:"btl-checkbox-field block",onClick:r},(0,n.createElement)("input",{type:"checkbox",disabled:!0,className:"btl-check"}),(0,n.createElement)("span",{className:"text"},(0,s.__)("No Follow","betterlinks"))),(0,n.createElement)("label",{className:"btl-checkbox-field block",onClick:r},(0,n.createElement)("input",{type:"checkbox",disabled:!0,className:"btl-check"}),(0,n.createElement)("span",{className:"text"},(0,s.__)("Case Sensitive","betterlinks"))))),(0,n.createElement)(Z,{title:(0,s.__)("Left Boundary","betterlinks"),onClick:r,defaultValue:{value:"",label:(0,s.__)("None","betterlinks")}}),(0,n.createElement)(fe,{title:(0,s.__)("Keyword Before","betterlinks"),onClick:r}),(0,n.createElement)(Z,{title:(0,s.__)("Right Boundary","betterlinks"),onClick:r,defaultValue:{value:"",label:(0,s.__)("None","betterlinks")}}),(0,n.createElement)(fe,{title:(0,s.__)("Keyword After","betterlinks"),onClick:r}),(0,n.createElement)(fe,{title:(0,s.__)("Limit","betterlinks"),onClick:r}),(0,n.createElement)(Z,{title:(0,s.__)("Default Post Types","betterlinks"),onClick:r,isMulti:!0,defaultValue:[{value:"",label:(0,s.__)("Post","betterlinks")}]}),(0,n.createElement)(Z,{title:(0,s.__)("Post Category","betterlinks"),onClick:r,isMulti:!0,defaultValue:[{value:"",label:(0,s.__)("Uncategorized","betterlinks")}]}),(0,n.createElement)(Z,{title:(0,s.__)("Post Tags","betterlinks"),onClick:r}),(0,n.createElement)("hr",{className:"btl-settings-devider",style:{marginTop:"20px"}}),(0,n.createElement)("span",{className:"btl-form-group btl-form-group--teaser btl-form-group-autolink-keyword-icon"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Auto-Linked Keywords Icon","betterlinks")),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block",onClick:r},(0,n.createElement)("input",{className:"btl-check",name:"is_autolink_icon",type:"checkbox",disabled:!0}),(0,n.createElement)("span",{className:"text"})))),(0,n.createElement)("span",{className:"btl-form-group btl-form-group--teaser btl-form-group-autolink-keyword-icon"},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Auto-Link Keywords inside","betterlinks-pro"),(0,n.createElement)("span",{style:{display:"block"}},(0,s.__)("Headings","betterlinks-pro"))),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block",onClick:r},(0,n.createElement)("input",{className:"btl-check",name:"is_autolink_headings",type:"checkbox",disabled:!0}),(0,n.createElement)("span",{className:"text"})))),(0,n.createElement)(Z,{title:(0,n.createElement)(n.Fragment,null,(0,s.__)("Disable Auto-link keywords ","betterlinks"),(0,n.createElement)("span",{style:{display:"block"}},(0,s.__)("for Post Types","betterlinks"))),onClick:r,isMulti:!0,defaultValue:[{value:"",label:(0,s.__)("Page","betterlinks")}]}))};const ze=function(e){var t=e.settings,l=e.postdatas,a=e.autoCreateLinkSettings,r=e.terms,s=e.trackingSettings,c=e.setAutoCreateLinkSettings,o=[(0,n.createElement)(Pe,{settings:t}),(0,n.createElement)(ae,{settings:t,terms:r}),w.Ry&&(0,n.createElement)(Me,{settings:t,terms:r}),(0,n.createElement)(ge,{trackingSettings:s}),(0,n.createElement)(be,{autoCreateLinkSettings:a,terms:r,setAutoCreateLinkSettings:c}),(0,n.createElement)(ue,{settings:t,postTypes:(null==l?void 0:l.postTypes)||[]}),(0,n.createElement)(Ne,{settings:t}),(0,n.createElement)(_e,{settings:t}),(0,n.createElement)(Re,{settings:t,postdatas:l})].filter(Boolean),m=betterLinksHooks.applyFilters("betterLinksSettingsOptionsTabList",y.gj),u=betterLinksHooks.applyFilters("betterLinksSettingsOptionsTabPanelList",o);return(0,n.createElement)("div",{className:"betterlinks-options-tabs-wrapper"},(0,n.createElement)(i.tU,null,(0,n.createElement)(i.wb,null,m.map((function(e,t){return(0,n.createElement)(i.oz,{key:t},e.label,"pro"===e.type&&!w.JT&&(0,n.createElement)(O.A,null))}))),(0,n.createElement)("div",{className:"btl-tab-panel-inner",style:{height:"fit-content",width:"800px"}},u.map((function(e,t){return(0,n.createElement)(i.Kp,{key:t},e)})))))},Be=(0,o.Ng)((function(e){return{settings:e.settings,postdatas:e.postdatas,terms:e.terms}}),(function(e){return{fetch_settings_data:(0,m.zH)(u.kc,e),fetch_tracking_settings:(0,m.zH)(u.nQ,e),fetch_post_types_data:(0,m.zH)(b.P,e),fetch_terms_data:(0,m.zH)(h.M3,e)}}))((function(e){var t,l=(0,n.useState)({}),o=(0,a.A)(l,2),m=o[0],u=o[1],b=(0,n.useState)({}),p=(0,a.A)(b,2),k=(p[0],p[1]),_=new URLSearchParams((0,c.useLocation)().search),f=_.get("import"),E=_.get("migration"),v=e.settings.settings,g=e.terms.terms,h=betterLinksHooks.applyFilters("betterLinksSettingsFilterTabList",[(0,s.__)("General","betterlinks"),(0,s.__)("Advanced Options","betterlinks"),(0,s.__)("Tools","betterlinks"),(0,s.__)("Role Management","betterlinks"),(0,s.__)("Go Premium","betterlinks")]),y=betterLinksHooks.applyFilters("betterLinksSettingsFilterTabPanel",[(0,n.createElement)(C,{settings:v}),(0,n.createElement)(ze,{settings:v,postdatas:(null==e?void 0:e.postdatas)||{},autoCreateLinkSettings:m,terms:g,trackingSettings:null===(t=e.settings)||void 0===t?void 0:t.tracking,setTrackingSettings:k,setAutoCreateLinkSettings:u}),(0,n.createElement)(j,{query:_}),(0,n.createElement)(I,null),(0,n.createElement)(U,null)]);return(0,n.useEffect)((function(){var t;v||e.fetch_settings_data(),null!=e&&null!==(t=e.settings)&&void 0!==t&&t.tracking||e.fetch_tracking_settings(),e.postdatas.fetchedAll||e.fetch_post_types_data(),w.JT&&(0,w.In)({action:"betterlinks/admin/get_auto_create_links_settings"}).then((function(e){if(e.data.data){var t=e.data.data;u({enable_auto_link:t.enable_auto_link,post_shortlinks:t.enable_auto_link&&t.post_shortlinks,post_default_cat:t.enable_auto_link&&t.post_shortlinks&&t.post_default_cat,page_shortlinks:t.enable_auto_link&&t.page_shortlinks,page_default_cat:t.enable_auto_link&&t.page_shortlinks&&t.page_default_cat})}})),g||e.fetch_terms_data()}),[]),(0,n.createElement)(r().Fragment,null,(0,n.createElement)(d.A,{label:(0,s.__)("BetterLinks Settings","betterlinks")}),(0,n.createElement)(i.tU,{defaultIndex:"true"==f?2:0},(0,n.createElement)(i.wb,null,h.map((function(e,t){return(0,n.createElement)(i.oz,{key:t},e)}))),y.map((function(e,t){return(0,n.createElement)(i.Kp,{key:t},e)}))),E&&(0,n.createElement)(M,{mode:E}),(0,n.createElement)(q,null))}))},93503:(e,t,l)=>{l.d(t,{Ay:()=>g});var a,n,r,s=l(58168),i=l(45458),c=l(23029),o=l(92901),m=l(85501),u=l(70246),b=l(51609),d=l.n(b),p=l(84125),k=l(28294),_=(l(79132),l(75795),function(){var e=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0,l=String(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").toLowerCase(),a=String(t.getOptionValue(e)).toLowerCase(),n=String(t.getOptionLabel(e)).toLowerCase();return a===l||n===l}),f={formatCreateLabel:function(e){return'Create "'.concat(e,'"')},isValidNewOption:function(e,t,l,a){return!(!e||t.some((function(t){return _(e,t,a)}))||l.some((function(t){return _(e,t,a)})))},getNewOptionData:function(e,t){return{label:t,value:e,__isNew__:!0}},getOptionValue:p.g,getOptionLabel:p.a},E=(0,u.a)({allowCreateWhileLoading:!1,createOptionPosition:"last"},f),v=(a=p.S,r=n=function(e){(0,m.A)(l,e);var t=(0,u._)(l);function l(e){var a;(0,c.A)(this,l),(a=t.call(this,e)).select=void 0,a.onChange=function(e,t){var l=a.props,n=l.getNewOptionData,r=l.inputValue,s=l.isMulti,c=l.onChange,o=l.onCreateOption,m=l.value,b=l.name;if("select-option"!==t.action)return c(e,t);var d=a.state.newOption,p=Array.isArray(e)?e:[e];if(p[p.length-1]!==d)c(e,t);else if(o)o(r);else{var k=n(r,r),_={action:"create-option",name:b,option:k};c(s?[].concat((0,i.A)((0,u.E)(m)),[k]):k,_)}};var n=e.options||[];return a.state={newOption:void 0,options:n},a}return(0,o.A)(l,[{key:"focus",value:function(){this.select.focus()}},{key:"blur",value:function(){this.select.blur()}},{key:"render",value:function(){var e=this,t=this.state.options;return d().createElement(a,(0,s.A)({},this.props,{ref:function(t){e.select=t},options:t,onChange:this.onChange}))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var l=e.allowCreateWhileLoading,a=e.createOptionPosition,n=e.formatCreateLabel,r=e.getNewOptionData,s=e.inputValue,c=e.isLoading,o=e.isValidNewOption,m=e.value,b=e.getOptionValue,d=e.getOptionLabel,p=e.options||[],k=t.newOption;return{newOption:k=o(s,(0,u.E)(m),p,{getOptionValue:b,getOptionLabel:d})?r(s,n(s)):void 0,options:!l&&c||!k?p:"first"===a?[k].concat((0,i.A)(p)):[].concat((0,i.A)(p),[k])}}}]),l}(b.Component),n.defaultProps=E,r);const g=(0,k.m)(v)}}]);