"use strict";(globalThis.webpackChunkbetterlinks=globalThis.webpackChunkbetterlinks||[]).push([[216],{52360:(e,t,l)=>{l.d(t,{A:()=>c});var a=l(51609),n=l(27723);const c=function(e){var t=e.note,l=e.title,c=void 0===l?(0,n.__)("Note","betterlinks"):l;return(0,a.createElement)("span",{className:"btl-modal-customize-link-preview--note"},c,": ",t)}},89726:(e,t,l)=>{l.r(t),l.d(t,{default:()=>E});var a=l(3453),n=l(51609),c=l(4949),s=l(27723),r=l(46005),m=l(72572),i=l(19735),o=l(2078),b=l(80702),d=l(40150),k=l(52360),u=new Array("Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday");function E(){return betterLinksHooks.applyFilters("betterLinksSettingsBrokenLinkChecker",(0,n.createElement)(p,null))}function p(e){var t=(0,b.c)(),l=(0,a.A)(t,3),E=l[0],p=l[1],N=l[2],_=(0,i.OS)("2.2");return(0,n.createElement)(React.Fragment,null,!_&&(0,n.createElement)("div",{className:"btl-notes notice notice-warning",style:{marginLeft:0,padding:"5px",fontSize:"12px"}},(0,n.createElement)(k.A,{note:"In this update, we've relocated the BetterLinks Broken Link Checker. To access it from the new location, please update the BetterLinks Pro plugin to at least v2.2. If you haven’t updated yet, you can still find the Broken Link Checker on the settings page."})),(0,n.createElement)(o.A,{isOpenModal:E,closeModal:N}),(0,n.createElement)("div",{className:"btl-tab-panel-inner btl-broken-links-panel btl-broken-links-panel-disabled"},(0,n.createElement)("div",{className:"btl-broken-link-checker-wrapper"},(0,n.createElement)("div",{className:"btl-broken-link-checker",style:{width:"55%"}},(0,n.createElement)("h3",null,(0,s.__)("Scheduled Scan","betterlinks")),(0,n.createElement)("p",null,(0,s.__)("Enable","betterlinks")," ",(0,n.createElement)("strong",null,(0,s.__)("“Scheduled Scan”","betterlinks"))," ",(0,s.__)("to automatically scan broken links on your website.","betterlinks")),(0,n.createElement)(c.l1,null,(0,n.createElement)(c.lV,null,(0,n.createElement)("div",{className:"btl-role-item btl-form-group",onClick:function(){return p()}},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Enable Scheduled Scan","betterlinks"),(0,n.createElement)(d.A,null)),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field"},(0,n.createElement)(c.D0,{type:"checkbox",className:"btl-check",disabled:!0}),(0,n.createElement)("sapan",{className:"text"})))),(0,n.createElement)("div",{className:"btl-form-group",onClick:function(){return p()}},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Frequency","betterlinks"),(0,n.createElement)(d.A,null)),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)(r.Ay,{className:"btl-select",classNamePrefix:"btl",isDisabled:!0}))),(0,n.createElement)("div",{className:"btl-form-group",onClick:function(){return p()}},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Day","betterlinks"),(0,n.createElement)(d.A,null)),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("div",{className:"scheduleweekdayselect"},u.map((function(e,t){return(0,n.createElement)("label",{key:t},(0,n.createElement)(c.D0,{type:"radio",checked:!1,disabled:!0}),(0,n.createElement)("span",null,e.slice(0,3)))}))))),(0,n.createElement)("div",{className:"btl-form-group",onClick:function(){return p()}},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Time","betterlinks"),(0,n.createElement)(d.A,null)),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)(m.A,{disabled:!0}))))),(0,n.createElement)("div",{className:"btl-scan-outputs",style:{marginTop:30},onClick:function(){return p()}},(0,n.createElement)("div",{className:"btl-scan-output"},(0,n.createElement)("span",{className:"icon"},(0,n.createElement)("img",{src:i.hq+"assets/images/padlock.svg",alt:""})),(0,n.createElement)("h4",{className:"count"},"0"),(0,n.createElement)("p",{className:"title"},(0,s.__)("Total Links","betterlinks"))),(0,n.createElement)("div",{className:"btl-scan-output"},(0,n.createElement)("span",{className:"icon"},(0,n.createElement)("img",{src:i.hq+"assets/images/padlock.svg",alt:""})),(0,n.createElement)("h4",{className:"count"},"0"),(0,n.createElement)("p",{className:"title"},(0,s.__)("Links Scanned","betterlinks"))),(0,n.createElement)("div",{className:"btl-scan-output"},(0,n.createElement)("span",{className:"icon"},(0,n.createElement)("img",{src:i.hq+"assets/images/padlock.svg",alt:""})),(0,n.createElement)("h4",{className:"count"},"0"),(0,n.createElement)("p",{className:"title"},(0,s.__)(" Broken Links Found","betterlinks"))))),(0,n.createElement)("div",{className:"btl-broken-link-checker"},(0,n.createElement)(c.l1,null,(0,n.createElement)(c.lV,null,(0,n.createElement)("span",{className:"btl-form-group",onClick:function(){return p()}},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Enable Reporting","betterlinks"),(0,n.createElement)(d.A,null)),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field"},(0,n.createElement)(c.D0,{type:"checkbox",className:"btl-check",disabled:!0}),(0,n.createElement)("span",{className:"text"})))),(0,n.createElement)("span",{className:"btl-form-group",onClick:function(){return p()}},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Reporting Email","betterlinks"),(0,n.createElement)(d.A,null)),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(c.D0,{type:"text",className:"btl-form-control",disabled:!0})))),(0,n.createElement)("span",{className:"btl-form-group",onClick:function(){return p()}},(0,n.createElement)("label",{className:"btl-form-label"},(0,s.__)("Reporting Email Subject","betterlinks"),(0,n.createElement)(d.A,null)),(0,n.createElement)("div",{className:"link-options__body"},(0,n.createElement)("label",{className:"btl-checkbox-field block"},(0,n.createElement)(c.D0,{type:"text",className:"btl-form-control",disabled:!0}))))))))))}E.propTypes={}}}]);