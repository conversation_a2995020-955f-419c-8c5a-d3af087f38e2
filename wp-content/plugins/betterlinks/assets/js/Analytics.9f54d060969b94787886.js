"use strict";(globalThis.webpackChunkbetterlinks=globalThis.webpackChunkbetterlinks||[]).push([[841],{80220:(e,t,a)=>{a.d(t,{h:()=>i});var n,l=a(51609);function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},r.apply(null,arguments)}var i=function(e){return l.createElement("svg",r({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 40 47"},e),n||(n=l.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"m19.557 26.171-2.514-1.864a5.6 5.6 0 0 0-7.834 1.162l-5.873 7.92a5.6 5.6 0 0 0 1.162 7.834l2.514 1.864a5.6 5.6 0 0 0 7.834-1.162l5.874-7.92a5.6 5.6 0 0 0-1.163-7.834M35.109 5.2l-2.514-1.864a5.6 5.6 0 0 0-7.834 1.162l-5.873 7.92a5.6 5.6 0 0 0 1.162 7.834l2.514 1.864a5.6 5.6 0 0 0 7.834-1.162l5.873-7.92A5.6 5.6 0 0 0 35.11 5.2M12.89 32.524l13.82-18.63"})))}},60907:(e,t,a)=>{a.d(t,{A:()=>c});var n=a(64467),l=a(51609),r=a(43516);function i(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}const c=function(e){return(0,l.createElement)(r.Ay,function(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?i(Object(a),!0).forEach((function(t){(0,n.A)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):i(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}({speed:2,width:"100%",height:"100%",viewBox:"0 0 532 148",backgroundColor:"#e8e8e8",foregroundColor:"#c2c2c2"},e),(0,l.createElement)("rect",{x:"2",y:"2",rx:"0",ry:"0",width:"530",height:"11"}),(0,l.createElement)("rect",{x:"3",y:"21",rx:"0",ry:"0",width:"527",height:"10"}),(0,l.createElement)("rect",{x:"2",y:"46",rx:"0",ry:"0",width:"530",height:"3"}),(0,l.createElement)("rect",{x:"2",y:"61",rx:"0",ry:"0",width:"530",height:"3"}),(0,l.createElement)("rect",{x:"2",y:"76",rx:"0",ry:"0",width:"530",height:"3"}),(0,l.createElement)("rect",{x:"2",y:"91",rx:"0",ry:"0",width:"530",height:"3"}),(0,l.createElement)("rect",{x:"2",y:"106",rx:"0",ry:"0",width:"530",height:"3"}),(0,l.createElement)("rect",{x:"1",y:"2",rx:"0",ry:"0",width:"3",height:"107"}),(0,l.createElement)("rect",{x:"529",y:"2",rx:"0",ry:"0",width:"3",height:"106"}),(0,l.createElement)("rect",{x:"2",y:"8",rx:"0",ry:"0",width:"8",height:"16"}),(0,l.createElement)("rect",{x:"25",y:"8",rx:"0",ry:"0",width:"84",height:"17"}),(0,l.createElement)("rect",{x:"144",y:"10",rx:"0",ry:"0",width:"83",height:"14"}),(0,l.createElement)("rect",{x:"264",y:"9",rx:"0",ry:"0",width:"77",height:"16"}),(0,l.createElement)("rect",{x:"476",y:"8",rx:"0",ry:"0",width:"54",height:"18"}),(0,l.createElement)("rect",{x:"374",y:"8",rx:"0",ry:"0",width:"71",height:"16"}),(0,l.createElement)("rect",{x:"10",y:"39",rx:"0",ry:"0",width:"16",height:"2"}),(0,l.createElement)("rect",{x:"110",y:"38",rx:"0",ry:"0",width:"39",height:"2"}),(0,l.createElement)("rect",{x:"227",y:"38",rx:"0",ry:"0",width:"65",height:"3"}),(0,l.createElement)("rect",{x:"341",y:"38",rx:"0",ry:"0",width:"39",height:"2"}),(0,l.createElement)("rect",{x:"445",y:"38",rx:"0",ry:"0",width:"54",height:"3"}),(0,l.createElement)("rect",{x:"10",y:"55",rx:"0",ry:"0",width:"16",height:"2"}),(0,l.createElement)("rect",{x:"110",y:"54",rx:"0",ry:"0",width:"39",height:"2"}),(0,l.createElement)("rect",{x:"227",y:"54",rx:"0",ry:"0",width:"58",height:"3"}),(0,l.createElement)("rect",{x:"341",y:"54",rx:"0",ry:"0",width:"39",height:"2"}),(0,l.createElement)("rect",{x:"445",y:"54",rx:"0",ry:"0",width:"49",height:"3"}),(0,l.createElement)("rect",{x:"10",y:"70",rx:"0",ry:"0",width:"16",height:"2"}),(0,l.createElement)("rect",{x:"110",y:"69",rx:"0",ry:"0",width:"39",height:"2"}),(0,l.createElement)("rect",{x:"227",y:"69",rx:"0",ry:"0",width:"60",height:"3"}),(0,l.createElement)("rect",{x:"341",y:"69",rx:"0",ry:"0",width:"39",height:"2"}),(0,l.createElement)("rect",{x:"445",y:"68",rx:"0",ry:"0",width:"56",height:"3"}),(0,l.createElement)("rect",{x:"10",y:"85",rx:"0",ry:"0",width:"16",height:"2"}),(0,l.createElement)("rect",{x:"110",y:"84",rx:"0",ry:"0",width:"39",height:"2"}),(0,l.createElement)("rect",{x:"227",y:"84",rx:"0",ry:"0",width:"54",height:"3"}),(0,l.createElement)("rect",{x:"341",y:"84",rx:"0",ry:"0",width:"39",height:"2"}),(0,l.createElement)("rect",{x:"445",y:"84",rx:"0",ry:"0",width:"45",height:"3"}),(0,l.createElement)("rect",{x:"10",y:"100",rx:"0",ry:"0",width:"16",height:"2"}),(0,l.createElement)("rect",{x:"110",y:"99",rx:"0",ry:"0",width:"39",height:"2"}),(0,l.createElement)("rect",{x:"227",y:"99",rx:"0",ry:"0",width:"58",height:"3"}),(0,l.createElement)("rect",{x:"341",y:"99",rx:"0",ry:"0",width:"39",height:"2"}),(0,l.createElement)("rect",{x:"445",y:"99",rx:"0",ry:"0",width:"54",height:"3"}))}},5051:(e,t,a)=>{a.d(t,{A:()=>i});var n=a(51609),l=a(27723),r=a(19735);const i=function(e){var t=e.mode,a=void 0===t?"#f2f2f2":t,i=e.noticeType,c=void 0===i?"warning":i,s=e.compatibleProVersion,o=e.notice;if((0,r.OS)(s))return"";var u={group:{marginLeft:0,padding:0},notice:{padding:"15px",background:a}};return(0,n.createElement)("div",{className:"btl-form-group ".concat(""!==c?"notice notice-"+c:""),style:u.group},(0,n.createElement)("div",{style:u.notice},(0,n.createElement)("b",{style:{fontWeight:700}},(0,l.__)("Note: ")),o))}},72057:(e,t,a)=>{a.d(t,{M:()=>h});var n,l,r=a(51609),i=a(20426),c=a(6272),s=a(54392),o=a(27723);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},u.apply(null,arguments)}var d=function(e){return r.createElement("svg",u({xmlns:"http://www.w3.org/2000/svg",width:21,height:21,fill:"none"},e),n||(n=r.createElement("g",{clipPath:"url(#locked-2_svg__a)"},r.createElement("path",{fill:"#3C3A60",d:"M15.171 11.09a.807.807 0 0 1-.806-.806V5.979a3.87 3.87 0 0 0-3.866-3.866 3.87 3.87 0 0 0-3.867 3.866v4.305a.806.806 0 1 1-1.612 0V5.979A5.485 5.485 0 0 1 10.499.5a5.485 5.485 0 0 1 5.479 5.48v4.304a.807.807 0 0 1-.807.806"}),r.createElement("path",{fill:"#9FACBA",d:"M10.5.5q-.41 0-.805.06a5.49 5.49 0 0 1 4.675 5.42v4.304l-.002.046a.806.806 0 0 0 1.61-.046V5.979A5.485 5.485 0 0 0 10.5.5"}),r.createElement("path",{fill:"#FEB137",d:"M16.999 20.5H4a1.36 1.36 0 0 1-1.36-1.36V9.506c0-.75.61-1.359 1.36-1.359h12.999c.75 0 1.359.609 1.359 1.36v9.634c0 .75-.608 1.359-1.36 1.359"}),r.createElement("path",{fill:"#F6AB31",d:"M16.999 8.147H15.39c.75 0 1.359.609 1.359 1.36v9.634c0 .75-.609 1.359-1.36 1.359H17c.75 0 1.36-.609 1.36-1.36V9.506c0-.75-.61-1.359-1.36-1.359"}),r.createElement("path",{fill:"#272847",d:"M12.56 13.249a2.061 2.061 0 1 0-3.095 1.783v2.377a1.034 1.034 0 1 0 2.067 0v-2.377a2.06 2.06 0 0 0 1.028-1.783"}))),l||(l=r.createElement("defs",null,r.createElement("clipPath",{id:"locked-2_svg__a"},r.createElement("path",{fill:"#fff",d:"M.5.5h20v20H.5z"})))))},m=a(19735),h=function(e){var t=e.title,a=void 0===t?null:t,n=e.description,l=void 0===n?"":n,u=e.children;return m.JT?null:(0,r.createElement)("div",{className:"btl-analytics-chart-overlay",style:{zIndex:"10"}},(0,r.createElement)(i.A,{sx:{minWidth:275}},(0,r.createElement)(c.A,null,(0,r.createElement)(s.A,{variant:"h5",color:"text.secondary",gutterBottom:!0},a||(0,o.__)("Get BetterLinks PRO ","betterlinks"),(0,r.createElement)(d,null)),(0,r.createElement)(s.A,{variant:"p",color:"text.secondary",gutterBottom:!0},l),(0,r.createElement)("div",null,u),(0,r.createElement)("div",{className:"analytics-upgrade-btn"},(0,r.createElement)("a",{href:"https://wpdeveloper.com/in/upgrade-betterlinks",target:"_blank"},(0,r.createElement)("img",{src:m.hq+"/assets/images/crown.svg",alt:""}),(0,o.__)("Upgrade To BetterLinks PRO","betterlinks"))))))}},16560:(e,t,a)=>{a.d(t,{A:()=>A});var n=a(3453),l=a(80045),r=a(51609),i=a.n(r),c=a(49924),s=a(68238),o=a(27723),u=a(19735),d=a(74086),m=a(67154),h=a(58766),g=a(7400),y=a(20312),p=a.n(y),v=a(46005),f=[{label:(0,o.__)("Delete All","betterlinks"),value:!1},{label:(0,o.__)("Delete clicks older than 30 days","betterlinks"),value:30},{label:(0,o.__)("Delete clicks older than 90 days","betterlinks"),value:90}];const b=(0,c.Ng)((function(){return{}}),(function(e){return{fetchCustomClicksData:(0,s.zH)(h.lC,e),dispatch_new_links_data:(0,s.zH)(g.jT,e)}}))((function(e){var t=e.fetchCustomClicksData,a=e.dispatch_new_links_data,l=(e.propsForAnalytics||{}).customDateFilter,i=(0,r.useState)(0),c=(0,n.A)(i,2),s=c[0],o=c[1],d=(0,r.useState)(!1),m=(0,n.A)(d,2),h=m[0],g=m[1],y=(0,r.useState)(0),b=(0,n.A)(y,2),_=b[0],E=b[1],k=(0,r.useState)("reset_modal_step_1"),w=(0,n.A)(k,2),x=w[0],N=w[1],A=(0,r.useState)(f[0]),L=(0,n.A)(A,2),O=L[0],C=L[1];(0,r.useEffect)((function(){var e,t;return h?null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.add("betterlinks-delete-clicks-modal-popup-opened"):null===(t=document)||void 0===t||null===(t=t.body)||void 0===t||null===(t=t.classList)||void 0===t||t.remove("betterlinks-delete-clicks-modal-popup-opened"),function(){var e;null===(e=document)||void 0===e||null===(e=e.body)||void 0===e||null===(e=e.classList)||void 0===e||e.remove("betterlinks-delete-clicks-modal-popup-opened")}}),[h]);var D=function(){clearTimeout(s),N("reset_modal_step_1"),g(!1),C(f[0])};return(0,r.createElement)("div",{className:"btl-analytic-reset-wrapeer betterlinks"},(0,r.createElement)("button",{className:"button-primary btl-reset-analytics-initial-button",onClick:function(){g(!0),N("reset_modal_step_1")}},"Reset"),(0,r.createElement)(p(),{isOpen:h,onRequestClose:D,ariaHideApp:!1},(0,r.createElement)("div",{className:"btl-reset-modal-popup-wrapper "},(0,r.createElement)("span",{className:"btl-close-modal",onClick:D},(0,r.createElement)("i",{className:"btl btl-cancel"})),"reset_modal_step_1"===x&&(0,r.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-1 betterlinks-body"},(0,r.createElement)("h2",null,"Pick the range of BetterLinks Analytics that you want to reset."),(0,r.createElement)("div",{className:"select_apply"},(0,r.createElement)(v.Ay,{className:"btl-modal-select--full ",classNamePrefix:"btl-react-select",onChange:function(e){C(e)},options:f,value:O,isMulti:!1}),(0,r.createElement)("button",{className:"button-primary btl-btn-reset-analytics btl-btn-reset-apply-1",onClick:function(){N("reset_modal_step_2")}},"Apply"))),"reset_modal_step_2"===x&&(0,r.createElement)("div",{className:"btl-reset-modal-popup btl-reset-modal-popup-step-2 betterlinks-body"},(0,r.createElement)("h2",null,"This Action Cannot be undone. Are you sure you want to continue?"),(0,r.createElement)("h4",null,"Clicking ",(0,r.createElement)("span",{style:{fontWeight:700}},"Reset Clicks")," will permanently delete the clicks data from database and it cannot be restored again.",(0,r.createElement)("span",{style:{display:"Block"}},"Click 'cancel' to abort.")),(0,r.createElement)("div",{className:"btl-btn-reset-popup-step-2-buttons"},(0,r.createElement)("button",{className:"button-primary btl-btn-reset-apply-2",onClick:function(){if(l){var e=(0,u.Yq)(l[0].startDate,"yyyy-mm-dd"),n=(0,u.Yq)(l[0].endDate,"yyyy-mm-dd");N("deleting");var r=(null==O?void 0:O.value)||!1;(0,u.Xq)(r,e,n).then((function(e){var n,l,r,i,c=setTimeout((function(){D()}),3e3);o(c),null!=e&&null!==(n=e.data)&&void 0!==n&&n.success?(E(null==e||null===(l=e.data)||void 0===l||null===(l=l.data)||void 0===l?void 0:l.count),t({data:null==e||null===(r=e.data)||void 0===r||null===(r=r.data)||void 0===r?void 0:r.new_clicks_data}),a({data:null==e||null===(i=e.data)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.new_links_data}),N("success")):N("failed")})).catch((function(e){console.log("---caught error on DeleteClicks",{err:e});var t=setTimeout((function(){D()}),3e3);o(t)}))}}},"Reset Clicks"),(0,r.createElement)("button",{className:"button-primary btl-btn-reset-cancel",onClick:function(){return N("reset_modal_step_1")}},"Cancel"))),"deleting"===x&&(0,r.createElement)("h2",null,"Deleting..."),"success"===x&&0!==_&&(0,r.createElement)("h2",null,"Success!!! ",(0,r.createElement)("span",{className:"success_delete_count"},_)," clicks record Deleted!!!"),"success"===x&&0===_&&(0,r.createElement)("h2",null,!1===(null==O?void 0:O.value)&&"You don't have any clicks data",30===(null==O?void 0:O.value)&&"You don't have clicks data older than 30 days",90===(null==O?void 0:O.value)&&"You don't have clicks data older than 90 days"),"failed"===x&&(0,r.createElement)("h2",null,"Failed!!"))))}));var _=a(5556),E=a.n(_),k=a(40150),w=["is_pro","render"],x={label:E().string,render:E().func},N=function(e){var t=e.is_pro,a=void 0!==t&&t,c=e.render,s=void 0===c?function(){}:c,d=(0,l.A)(e,w),m=d.propsForAnalytics,h=d.activity.darkMode,g=(0,r.useState)(h),y=(0,n.A)(g,2),p=y[0],v=y[1];(0,r.useEffect)((function(){h?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode")}),[]);var f=betterLinksQuery.get("page"),_=d.favouriteSort.sortByFav;return(0,r.createElement)("div",{className:"topbar"},(0,r.createElement)("div",{className:"topbar__logo_container"},(0,r.createElement)("div",{className:"topbar__logo"},(0,r.createElement)("img",{src:u.hq+"assets/images/logo-large".concat(p?"-white":"",".svg"),alt:"logo"}),(0,r.createElement)("span",{className:"topbar__logo__text"},d.label),a&&(0,r.createElement)(k.A,null)),s()),(0,r.createElement)("div",{className:"topbar-inner"},"betterlinks"===f&&(0,r.createElement)(i().Fragment,null,(0,r.createElement)("div",{className:"btl-view-control"},(0,r.createElement)("button",{title:(0,o.__)("Favorite Links","betterlinks"),className:"btl-link-view-toggler btl-sortby-fav ".concat(_?"active":""),onClick:function(){return d.sortFavourite(!_)}},(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"favorite-svg",viewBox:"0 0 512 512",xmlSpace:"preserve"},(0,r.createElement)("path",{className:"fav-icon-svg-path",d:"M392.2 317.5c-3 2.9-4.4 7.1-3.7 11.3L414 477.4c1.2 7-3.5 13.6-10.5 14.9-2.8.5-5.6 0-8.1-1.3L262 420.9c-3.7-2-8.2-2-12 0L116.6 491c-3.1 1.7-6.8 1.9-10.1.8-6-2.1-9.5-8.1-8.5-14.4l25.4-148.5c.7-4.2-.7-8.4-3.7-11.4L11.9 212.4c-5.1-5-5.2-13.1-.2-18.2 2-2 4.6-3.3 7.3-3.7l149.1-21.7c4.2-.6 7.8-3.2 9.7-7l66.7-135c2.6-5.3 8.4-8.1 14.2-6.9 3.9.7 7.2 3.3 8.9 6.9l66.7 135c1.9 3.8 5.5 6.4 9.7 7l149 21.6c7 1 11.9 7.6 10.9 14.6-.4 2.7-1.7 5.3-3.7 7.2l-108 105.3z"}))),(0,r.createElement)("button",{title:(0,o.__)("List View","betterlinks"),className:"btl-link-view-toggler ".concat("list"==d.activity.linksView?"active":""),onClick:function(){return d.linksView("list")}},(0,r.createElement)("i",{className:"btl btl-list"})),(0,r.createElement)("button",{title:(0,o.__)("Grid View","betterlinks"),className:"btl-link-view-toggler ".concat("grid"==d.activity.linksView?"active":""),onClick:function(){return d.linksView("grid")}},(0,r.createElement)("i",{className:"btl btl-grid"})))),(null==m?void 0:m.isResetAnalytics)&&(0,r.createElement)(b,{propsForAnalytics:m}),(0,r.createElement)("label",{className:"theme-mood-button",htmlFor:"theme-mood",title:(0,o.__)("Theme Mode","betterlinks")},(0,r.createElement)("input",{type:"checkbox",name:"theme-mood",id:"theme-mood",value:p,onChange:function(){return function(e){e?document.body.classList.add("betterlinks-dark-mode"):document.body.classList.remove("betterlinks-dark-mode"),d.update_theme_mode(e),v(e)}(!p)},checked:p}),(0,r.createElement)("span",{className:"theme-mood"},(0,r.createElement)("span",{className:"icon"},(0,r.createElement)("i",{className:"btl btl-sun"})),(0,r.createElement)("span",{className:"icon"},(0,r.createElement)("i",{className:"btl btl-moon"}))))))};N.propTypes=x;const A=(0,c.Ng)((function(e){return{activity:e.activity,favouriteSort:e.favouriteSort}}),(function(e){return{linksView:(0,s.zH)(d.xb,e),sortFavourite:(0,s.zH)(m.sortFavourite,e),update_theme_mode:(0,s.zH)(d.Q7,e)}}))(N)},25901:(e,t,a)=>{a.r(t),a.d(t,{default:()=>Ve});var n=a(64467),l=a(3453),r=a(51609),i=a.n(r),c=a(86663),s=a(27723),o=a(16560),u=a(68238),d=a(49924),m=a(17242),h=a(979),g=a(2078),y=a(19735),p=a(10138),v=a(11912),f=[{label:"Browser",value:"browser"},{label:"IP",value:"ip"},{label:"Timestamp",value:"created_at"},{label:"Referer",value:"referer"},{label:"Parameters",value:"query_params"},{label:"OS",value:"os"},{label:"Device",value:"device"}],b=function(){return function(e){var t=localStorage.getItem("btl_analytics_settings")?JSON.parse(localStorage.getItem("btl_analytics_settings")):f;e({type:v.FETCH_ANALYTICS_SETTINGS,payload:t})}},_=function(e){return function(t){localStorage.setItem("btl_analytics_settings",JSON.stringify(e)),t({type:v.FETCH_ANALYTICS_SETTINGS,payload:e})}},E=a(60907),k=a(83757);function w(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function x(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?w(Object(a),!0).forEach((function(t){(0,n.A)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):w(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}const N=function(e){var t=e.searchStatus;return(0,r.createElement)("div",{className:"search-wrapper"},(0,r.createElement)("div",{className:"search loading"},(0,r.createElement)("div",{className:"search__circle",style:x({},t&&{animation:"load8 1.1s infinite linear","-webkit-animation":"load8 1.1s infinite linear","border-left-color":"#ef726c"})}),(0,r.createElement)("div",{className:"search__rectangle"})))};var A=a(87622),L=a(40150);const O=function(e){var t=e.filterText,a=e.onFilter,n=e.searchClickHandler,i=e.searchStatus,c=e.isSearching,o=e.resetSearch,u=e.analytics,d=e.update_analytics_settings,m=e.id,h=(0,r.useState)([]),g=(0,l.A)(h,2),p=g[0],v=g[1];(0,r.useEffect)((function(){v(Object.values(u||[]))}),[u]);var f=betterLinksQuery.get("tag_id"),b=[{label:(0,s.__)("Browser","betterlinks"),value:"browser"},{label:(0,s.__)("IP","betterlinks"),value:"ip"},{label:(0,s.__)("Timestamp","betterlinks"),value:"created_at"},{label:(0,s.__)("Referer","betterlinks"),value:"referer"},{label:(0,r.createElement)("div",null,(0,s.__)("Parameters","betterlinks")," ",!y.JT&&(0,r.createElement)(L.A,null)),value:"query_params"},{label:(0,s.__)("OS","betterlinks"),value:"os"},{label:(0,s.__)("Device","betterlinks"),value:"device"}];return(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"btl-click-filter"},(0,r.createElement)("div",{style:{display:"flex",alignItems:"center"}},m&&(0,r.createElement)("a",{className:"btl-go-back-btn dashicons dashicons-arrow-left-alt",onClick:function(){return window.history.go(-1),!1},href:"#"})),(0,r.createElement)("div",{style:{display:"flex"}},(0,r.createElement)("form",{onSubmit:n},(0,r.createElement)("input",{id:"search",type:"text",placeholder:(0,s.__)("Search...","betterlinks"),value:t,onChange:a}),(0,r.createElement)("button",{className:"btl-search-button",type:"submit",title:(0,s.__)("Searching","betterlinks")},(0,r.createElement)(N,{searchStatus:i})),c&&(0,r.createElement)("button",{className:"btl-search-button btl-search-reset",type:"button",title:(0,s.__)("Reset Search","betterlinks"),onClick:o},(0,r.createElement)("span",{class:"dashicons dashicons-image-rotate"}))),m&&!f&&(0,r.createElement)(A.KF,{options:b,value:p,onChange:function(e){v(e),d(e)},labelledBy:"Select",disableSearch:!0,hasSelectAll:!1}))))};var C,D,P=a(58766);function S(){return S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},S.apply(null,arguments)}var j,F,H=function(e){return r.createElement("svg",S({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),C||(C=r.createElement("g",{stroke:"#9FA1B8",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,clipPath:"url(#link_svg__a)"},r.createElement("path",{d:"m9 15 6-6m-4-3 .463-.536a5 5 0 0 1 7.071 7.072L18 13m-5 5-.397.534a5.07 5.07 0 0 1-7.127 0 4.97 4.97 0 0 1 0-7.071L6 11"}))),D||(D=r.createElement("defs",null,r.createElement("clipPath",{id:"link_svg__a"},r.createElement("path",{fill:"#fff",d:"M0 0h24v24H0z"})))))};function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},M.apply(null,arguments)}var q,T,z=function(e){return r.createElement("svg",M({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),j||(j=r.createElement("g",{stroke:"#9FA1B8",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,clipPath:"url(#target_svg__a)"},r.createElement("path",{d:"M12 6H6a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-6m-7 1 9-9m-5 0h5v5"}))),F||(F=r.createElement("defs",null,r.createElement("clipPath",{id:"target_svg__a"},r.createElement("path",{fill:"#fff",d:"M0 0h24v24H0z"})))))};function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},B.apply(null,arguments)}var I=function(e){return r.createElement("svg",B({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),q||(q=r.createElement("g",{stroke:"#9FA1B8",strokeLinecap:"round",strokeLinejoin:"round",clipPath:"url(#www_svg__a)"},r.createElement("path",{strokeWidth:2,d:"M19.5 7A9 9 0 0 0 12 3a8.99 8.99 0 0 0-7.484 4"}),r.createElement("path",{strokeWidth:2,d:"M11.5 3a17 17 0 0 0-1.826 4M12.5 3a17 17 0 0 1 1.828 4M19.5 17a9 9 0 0 1-7.5 4 8.99 8.99 0 0 1-7.484-4"}),r.createElement("path",{strokeWidth:2,d:"M11.5 21a17 17 0 0 1-1.826-4m2.826 4a17 17 0 0 0 1.828-4"}),r.createElement("path",{strokeWidth:1.5,d:"m2 10 1 4 1.5-4L6 14l1-4m10 0 1 4 1.5-4 1.5 4 1-4M9.5 10l1 4 1.5-4 1.5 4 1-4"}))),T||(T=r.createElement("defs",null,r.createElement("clipPath",{id:"www_svg__a"},r.createElement("path",{fill:"#fff",d:"M0 0h24v24H0z"})))))},R=function(e,t){var a={clicks:{},unique_clicks:{}};return e.total_count.forEach((function(e){a.clicks[e.c_date]=e.click_count})),e.unique_count.forEach((function(e){a.unique_clicks[e.c_date]=e.uniq_count})),a},Y=function(e,t,a){var n,l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if(l&&!(arguments.length>4&&void 0!==arguments[4]&&arguments[4]))return null==e||null===(n=e.filter)||void 0===n?void 0:n.call(e,(function(e){if(e.link_id==l)return JSON.stringify(e).toLowerCase().includes(a.toLowerCase())}));for(var r=[],i=function(){var t=e[c];r.find((function(e){return e.link_id==t.link_id}))||(t.link_title&&t.link_title.toLowerCase().includes(null==a?void 0:a.toLowerCase()),r.push(t))},c=0;c<e.length;c++)i();return 1==t?y.JT||is_extra_data_tracking_compatible?r.sort((function(e,t){return+(null==e?void 0:e.total_clicks)-+(null==t?void 0:t.total_clicks)})).reverse().slice(0,5):[]:r.filter((function(e){return JSON.stringify(e).toLowerCase().includes(null==a?void 0:a.toLowerCase())}))};const V=(0,d.Ng)((function(e){return{analytics:e.analytics,activity:e.activity}}),(function(e){return{searchClicksData:(0,u.zH)(P.Iz,e),fetch_clicks_data:(0,u.zH)(P.AK,e),fetch_analytics_settings:(0,u.zH)(b,e),update_analytics_settings:(0,u.zH)(_,e)}}))((function(e){var t=e.columns,a=e.data,n=e.progressPending,i=e.id,c=void 0===i?null:i,o=e.from,u=void 0===o?null:o,d=(0,r.useState)(!1),m=(0,l.A)(d,2),h=m[0],g=m[1],p=(0,r.useState)(!1),v=(0,l.A)(p,2),f=v[0],b=v[1],_=(0,r.useState)(!1),w=(0,l.A)(_,2),x=w[0],N=w[1],A=(0,r.useState)(""),L=(0,l.A)(A,2),C=L[0],D=L[1],P=e.activity.analyticsTab,S=e.analytics.analytics;(0,r.useEffect)((function(){S||e.fetch_analytics_settings()}),[]);var j=React.useMemo((function(){return(0,r.createElement)(O,{onFilter:function(e){return D(e.target.value)},onClear:function(){C&&(g(!h),D(""))},filterText:C,searchClickHandler:function(t){t.preventDefault(),C&&(b(!0),e.searchClicksData(y.sL,C).then((function(){b(!1),N(!0)})))},searchStatus:f,isSearching:x,resetSearch:function(){N(!1),D("");var t={from:(0,y.Yq)(customDateFilter[0].startDate,"yyyy-mm-dd"),to:(0,y.Yq)(customDateFilter[0].endDate,"yyyy-mm-dd")};e.fetch_clicks_data(t)},analytics:S,update_analytics_settings:e.update_analytics_settings,id:c,analyticsTab:P,update_activity:e.update_activity})}),[C,h,f,b,x,N,S,P,c]);return(0,r.createElement)(k.Ay,{className:"btl-analytic-table",title:(0,s.__)("Single Clicks","betterlinks"),columns:t,data:Y(a,P,C,c,u),pagination:!0,progressPending:n,progressComponent:(0,r.createElement)(E.A,null),paginationResetDefaultPage:h,subHeader:!0,subHeaderComponent:j,persistTableHead:!0,defaultSortFieldId:"name",paginationRowsPerPageOptions:y._t,onChangeRowsPerPage:function(e){return localStorage.setItem("btlAnalyticsRowsPerPage",e)},paginationPerPage:+localStorage.getItem("btlAnalyticsRowsPerPage")||10})})),W=(0,d.Ng)((function(e){return{settings:e.settings,analytics:e.analytics,clicks:e.clicks}}),(function(e){return{fetch_settings_data:(0,u.zH)(p.kc,e),fetch_analytics_settings:(0,u.zH)(b,e)}}))((function(e){var t=e.analyticsTab,a=e.unique_list,n=e.id,l=void 0===n?null:n,i=e.from,c=void 0===i?null:i,s=e.settings.settings,o=e.analytics.analytics;(0,r.useEffect)((function(){s||e.fetch_settings_data(),o||e.fetch_analytics_settings()}),[]);var u=(0,r.useCallback)((0,y.GH)(o,t),[o,t]),d=null!=s&&s.is_disable_analytics_ip?u.filter((function(e){return"ip"!==e.selector})):u;return(0,r.createElement)(V,{columns:d,data:a||[],progressPending:!a,id:l,from:c})}));var J,G,Q=a(10467),K=a(89280),U=a.n(K),X=a(30020),Z=a(73486),$=a(50384),ee=a(77430);function te(){return te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},te.apply(null,arguments)}var ae,ne=function(e){return r.createElement("svg",te({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 44 30"},e),J||(J=r.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M42.43 14.68V2.85c0-.24-.05-.48-.14-.71s-.23-.43-.4-.6c-.01-.01-.02-.01-.03-.02a2 2 0 0 0-.57-.38c-.18-.07-.36-.09-.55-.11-.06 0-.1-.03-.16-.03H28.7"})),G||(G=r.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"m1 28.04 12.75-12.07 5.62 7.19a1.852 1.852 0 0 0 2.77.17L40.77 2.69"})))},le=a(80220);function re(){return re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},re.apply(null,arguments)}var ie,ce,se=function(e){return r.createElement("svg",re({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 40 41"},e),ae||(ae=r.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.65 39.39H1V22.72h8.65zM23.67 39.39h-8.65V8.08h8.65zM38.6 39.39h-8.65V1h8.65z"})))};function oe(){return oe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},oe.apply(null,arguments)}var ue,de=function(e){return r.createElement("svg",oe({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 55 47"},e),ie||(ie=r.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M33.23 45.25c-1.9.33-3.84.49-5.77.49-4.25 0-8.5-.78-12.34-2.31-6.06-2.4-10.87-6.49-13.91-11.81-.29-.51-.29-1.13 0-1.64 2.56-4.48 6.46-8.15 11.27-10.62 4.48-2.29 9.65-3.5 14.95-3.5s10.47 1.21 14.95 3.5c4.81 2.46 8.71 6.13 11.27 10.62.29.51.29 1.13 0 1.64-2.2 3.85-5.29 7.02-9.19 9.44-4.37 2.83-11.23 4.19-11.23 4.19M27.34 10.21V1.79M36.3 12.15l4.21-7.3M14.36 4.85l4.21 7.3"})),ce||(ce=r.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"m31.78 38.69 4.27 5.23m-4.27-5.23a8.9 8.9 0 0 1-5.72 1.63c-4.96-.3-8.74-4.57-8.44-9.52s4.57-8.74 9.52-8.44 8.74 4.57 8.44 9.52c-.13 2.12-1.23 4.68-3.8 6.82z"})))};function me(){return me=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},me.apply(null,arguments)}var he=function(e){return r.createElement("svg",me({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 38 37"},e),ue||(ue=r.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"m21.61 6.16 1.48-4.67M16.35 4.33 16.68 1M2.42 24.55l4.4-2.15M1 18.28l3.24-.82M35.96 30.1l-7.76-7.77a.32.32 0 0 1-.09-.28c.02-.1.09-.19.18-.23l7-3.05a2.193 2.193 0 0 0-.2-4.07L10.05 6.52A2.19 2.19 0 0 0 7.3 9.27l8.18 25.04a2.17 2.17 0 0 0 1.97 1.5h.11c.87 0 1.65-.52 1.99-1.31l3.04-7c.04-.1.13-.17.23-.18.1-.***********.09l7.77 7.77c.86.86 2.24.86 3.1 0l1.99-1.99c.86-.86.86-2.24 0-3.1z"})))},ge=a(72057);const ye=function(){return y.JT?null:(0,r.createElement)(ge.M,{description:(0,s.__)("Track your links with detailed analytics, dynamic infographics, insights on OS,  browser,top medium - social, search platforms and more.","betterlinks")},(0,r.createElement)(Z.A,null,[(0,r.createElement)("div",{className:"btl-graphteaser-icon"},(0,r.createElement)(ne,null),(0,r.createElement)("p",null,(0,s.__)("Track your top-performing link Click Sources","betterlinks"))),(0,r.createElement)("div",{className:"btl-graphteaser-icon"},(0,r.createElement)(le.h,null),(0,r.createElement)("p",null,(0,s.__)("Access exclusive click data for Better Insights","betterlinks"))),(0,r.createElement)("div",{className:"btl-graphteaser-icon"},(0,r.createElement)(se,null),(0,r.createElement)("p",null,(0,s.__)("Measure the performance of your shortened links","betterlinks"))),(0,r.createElement)("div",{className:"btl-graphteaser-icon"},(0,r.createElement)(de,null),(0,r.createElement)("p",null,(0,s.__)("Identify your most-clicked links for optimal strategy","betterlinks"))),(0,r.createElement)("div",{className:"btl-graphteaser-icon"},(0,r.createElement)(he,null),(0,r.createElement)("p",null,(0,s.__)("Get detailed individual click stats at your fingertips","betterlinks")))].map((function(e){return(0,r.createElement)($.A,{disableGutters:!0},(0,r.createElement)(ee.A,{primary:e}))}))))},pe=function(e){var t=e.darkMode;return(0,r.createElement)("div",{className:"btl-top-charts btl-top-charts-teaser"},(0,r.createElement)("img",{style:{width:"100%"},src:"".concat(y.hq,"assets/images/teasers/").concat(t?"dark":"light","-chart-teaser.png")}),(0,r.createElement)(ye,null))},ve=function(){return(0,r.createElement)("div",{className:"btl-chart-preloader"},(0,r.createElement)("img",{src:"".concat(y.hq,"assets/images/dark-mode-loader.gif")}))};var fe=function(){};const be=(0,d.Ng)((function(e){return{clicks:e.clicks,activity:e.activity}}),(function(e){return{fetchCustomClicksData:(0,u.zH)(P.lC,e),get_chart_data:(0,u.zH)(P.Hr,e),get_graph_data:(0,u.zH)(P.rF,e),get_medium_data:(0,u.zH)(P._I,e),fetch_clicks_data:(0,u.zH)(P.AK,e),fetch_individual_clicks:(0,u.zH)(P.fn,e),get_analytics_graph_by_tag:(0,u.zH)(P.MI,e),get_analytics_unique_list_by_id:(0,u.zH)(P.Nr,e)}}))((function(e){var t=e.customDateFilter,a=e.setCustomDateFilter,n=e.extraAnalytics,i=e.chartLoading,c=void 0!==i&&i,o=e.setChartLoading,u=void 0===o?fe:o,d=e.setLoading,h=void 0===d?fe:d,g=e.setGraphLoading,p=void 0===g?fe:g,v=e.setMediumLoading,f=void 0===v?fe:v,b=e.activity,_=betterLinksQuery.get("id"),E=betterLinksQuery.get("tag_id"),k=(0,y.bI)(y.JT?e.data.clicks:[]),w=(0,r.useState)((0,s.__)("Filter","betterlinks")),x=(0,l.A)(w,2),N=x[0],A=x[1],L=(0,r.useState)(!1),O=(0,l.A)(L,2),C=O[0],D=O[1],P=b.darkMode,S=function(){var a=(0,Q.A)(U().mark((function a(){var n,l;return U().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:A((0,s.__)("Filtering...","betterlinks"));try{n=(0,y.Yq)(t[0].startDate,"yyyy-mm-dd"),l=(0,y.Yq)(t[0].endDate,"yyyy-mm-dd"),_||(e.fetch_clicks_data({from:n,to:l,setLoading:h}),e.get_graph_data({from:n,to:l,setLoading:p}),e.get_chart_data({from:n,to:l,setLoading:u}),e.get_medium_data({from:n,to:l,setLoading:f})),_&&y.JT&&e.fetch_individual_clicks({link_id:_,from:n,to:l,setLoading:h}),E&&(e.get_analytics_graph_by_tag({from:n,to:l,tag_id:E,setLoading:h}),e.get_analytics_unique_list_by_id({from:n,to:l,tag_id:E,setLoading:h})),setTimeout((function(){A((0,s.__)("Done!","betterlinks")),setTimeout((function(){A((0,s.__)("Filter","betterlinks"))}),3e3)}),1e3)}catch(e){console.log({error:e.message})}case 2:case"end":return a.stop()}}),a)})));return function(){return a.apply(this,arguments)}}(),j={options:{chart:{id:"analytics-click-count",toolbar:{export:{csv:{headerCategory:"Date"}}}},xaxis:{categories:k},stroke:{curve:"smooth"},colors:["#FF7818","#6034E6"],markers:{size:5},legend:{position:"top"}},series:(0,y.vh)(e.data,e.uniqueIpCount)};return(0,r.createElement)("div",null,(0,r.createElement)("div",{className:"btl-analytics-filter"},(0,r.createElement)("h3",{className:"btl-analytics-filter__heading"},(0,s.__)("Click Analytics","betterlinks")),(0,r.createElement)("div",{className:"btl-analytics-filter__control"},(0,r.createElement)("button",{onClick:function(){(0,y.AX)(),D(!C)},className:"btl-list-view-calendar"},(0,r.createElement)("span",{className:"dashicons dashicons-calendar"}),String(t[0].startDate).slice(4,15)," - ",String(t[0].endDate).slice(4,15)),C&&(0,r.createElement)("div",{className:"btl-date-range-picker-wrap"},(0,r.createElement)("div",{className:"btl-date-range-picker"},(0,r.createElement)("button",{onClick:function(){(0,y.jT)(),D(!1)},className:"btn-date-range-close"},(0,r.createElement)("span",{className:"dashicons dashicons-no-alt"})),(0,r.createElement)(X.Ur,{onChange:function(e){return function(e){a([e.selection]),e.selection.endDate!=e.selection.startDate&&((0,y.jT)(),D(!1))}(e)},showSelectionPreview:!0,moveRangeOnFirstSelection:!1,months:2,ranges:t,direction:"horizontal"}))),(0,r.createElement)("button",{className:"btl-filter-action",onClick:S},N))),(0,r.createElement)("div",{className:"btl-analytics-chart"},(0,r.createElement)("div",{className:"btl-analytics-chart-line".concat(!y.JT&&_?" btl-analytics-chart-line-teaser":"")},!y.JT&&_?(0,r.createElement)("img",{className:"btl-analytics-chart-image",src:y.hq+"assets/images/teasers/individual-analytics.png"}):(0,r.createElement)(m.A,{options:j.options,series:j.series,type:"area",height:"350"}),_&&(0,r.createElement)(ye,null)),y.JT&&c?(0,r.createElement)(ve,null):!_&&!E&&betterLinksHooks.applyFilters("BetterlinksAnalyticsChart",!y.uL&&(0,r.createElement)(pe,{darkMode:P}),n)))}));var _e=a(80702),Ee=a(43516);const ke=function(){return(0,r.createElement)(Ee.Ay,{width:"100%",height:"100%",viewBox:"0 0 350 300"},(0,r.createElement)("rect",{x:"6",y:"24",rx:"0",ry:"0",width:"57",height:"259"}),(0,r.createElement)("rect",{x:"76",y:"89",rx:"0",ry:"0",width:"57",height:"195"}),(0,r.createElement)("rect",{x:"144",y:"120",rx:"0",ry:"0",width:"57",height:"167"}),(0,r.createElement)("rect",{x:"214",y:"163",rx:"0",ry:"0",width:"57",height:"123"}),(0,r.createElement)("rect",{x:"282",y:"53",rx:"0",ry:"0",width:"57",height:"233"}))},we=(0,d.Ng)((function(e){return{clicks:e.clicks,activity:e.activity}}),(function(e){return{get_chart_data:(0,u.zH)(P.Hr,e),get_graph_data:(0,u.zH)(P.rF,e),get_medium_data:(0,u.zH)(P._I,e),fetch_clicks_data:(0,u.zH)(P.AK,e),fetch_individual_clicks:(0,u.zH)(P.fn,e)}}))((function(e){var t=(0,r.useState)(!1),a=(0,l.A)(t,2),n=a[0],i=a[1],c=(0,r.useState)(!1),s=(0,l.A)(c,2),o=s[0],u=s[1],d=(0,r.useState)(!1),p=(0,l.A)(d,2),v=p[0],f=p[1],b=(0,r.useState)(!1),_=(0,l.A)(b,2),E=(_[0],_[1]),k=(0,_e.c)(),w=(0,l.A)(k,3),x=w[0],N=(w[1],w[2]),A=(null==e?void 0:e.propsForAnalytics)||{},L=A.customDateFilter,O=A.setCustomDateFilter,C=e.clicks,D=C.clicks,P=C.unique_list,S=C.unique_count,j=C.referer,F=C.devices,H=C.os,M=C.browser,q=C.medium,T=e.activity,z=T.darkMode,B=T.analyticsTab;return(0,r.useEffect)((function(){var t=(0,y.Yq)(L[0].startDate,"yyyy-mm-dd"),a=(0,y.Yq)(L[0].endDate,"yyyy-mm-dd")||(0,y.Yq)(new Date,"yyyy-mm-dd"),n=betterLinksHooks.applyFilters("betterLinksAnalyticsFilterStartDate",(0,h.A)(new Date,30));n=t||(0,y.Yq)(n,"yyyy-mm-dd"),D||(e.get_chart_data({from:n,to:a,setLoading:f}),e.get_graph_data({from:n,to:a,setLoading:u}),e.get_medium_data({from:n,to:a,setLoading:E})),null!=D&&D.unique_list||e.fetch_clicks_data({from:n,to:a,setLoading:i})}),[D]),(0,r.createElement)("div",{className:"btl-analytic"},(0,r.createElement)(g.A,{isOpenModal:x,closeModal:N}),(0,r.createElement)(be,{data:R(D||{total_count:[],unique_count:[]}),uniqueIpCount:S,customDateFilter:L,setCustomDateFilter:O,chartLoading:v,setChartLoading:f,loading:n,setLoading:i,graphLoading:o,setGraphLoading:u,setMediumLoading:E,extraAnalytics:{top_referer:j||[],devices:F||[],os:H||[],browser:M||[],top_medium:q||[],darkMode:z,Chart:m.A,BarLoader:ke}}),(0,r.createElement)("div",{className:"btl-analytic-table-wrapper"},(0,r.createElement)(W,{analyticsTab:B,unique_list:P,loading:n})))}));var xe=a(45458);const Ne=function(e){var t=e.clicks,a=t.link_title,n=t.short_url,l=t.target_url,i=function(){return(0,r.createElement)(Ee.Ay,{speed:2,width:"100%",height:10,viewBox:"0 0 300 10",backgroundColor:"#f3f3f3",foregroundColor:"#ecebeb"},(0,r.createElement)("rect",{x:"1",y:"2",rx:"3",ry:"3",width:"196",height:"13"}))},c=betterLinksHooks.applyFilters("site_url",y.IV),s="".concat(c,"/").concat(n);return(0,r.createElement)("div",{className:"btl-single-click-info-header"},(0,r.createElement)("div",{className:"btl-single-info--name"},(0,r.createElement)("span",{className:"btl-single-info-svg-icon",style:{width:"20px",height:"20px",transform:"scale(0.8)"}},(0,r.createElement)(H,null)),(0,r.createElement)("span",{className:"btl-column-name",style:{marginRight:"5px"}},"Link Name:"),(0,r.createElement)("span",{className:"btl-link-name",title:a},(null==a?void 0:a.slice(0,40))||(0,r.createElement)(i,null))),(0,r.createElement)("div",{className:"btl-single-info-short-url btl-single-info--name"},(0,r.createElement)("span",{className:"btl-single-info-svg-icon",style:{width:"20px",height:"20px",transform:"scale(0.8)"}},(0,r.createElement)(I,null)),(0,r.createElement)("span",{className:"btl-column-name",style:{marginRight:"5px"}},"Shortened URL:"),(0,r.createElement)("a",{href:s,className:"btl-link-name",target:"_blank",title:s},(null==n?void 0:n.slice(0,40))||(0,r.createElement)(i,null))),(0,r.createElement)("div",{className:"btl-single-info-target-url btl-single-info--name"},(0,r.createElement)("span",{className:"btl-single-info-svg-icon",style:{width:"20px",height:"20px",transform:"scale(0.8)"}},(0,r.createElement)(z,null)),(0,r.createElement)("span",{className:"btl-column-name",style:{marginRight:"5px"}},"Target URL:"),(0,r.createElement)("a",{href:l,className:"btl-link-name",title:l,target:"_blank"},(null==l?void 0:l.slice(0,40))||(0,r.createElement)(i,null),(null==l?void 0:l.length)>40&&"[...]")))};var Ae=a(5051);const Le=(0,d.Ng)((function(e){return{clicks:e.clicks,activity:e.activity,settings:e.settings,analytics:e.analytics}}),(function(e){return{fetch_clicks_data:(0,u.zH)(P.AK,e),fetch_individual_clicks:(0,u.zH)(P.fn,e),fetch_settings_data:(0,u.zH)(p.kc,e),fetch_analytics_settings:(0,u.zH)(b,e),update_analytics_settings:(0,u.zH)(_,e)}}))((function(e){var t,a,n,i,c,o=(0,_e.c)(),u=(0,l.A)(o,3),d=u[0],m=(u[1],u[2]),p=(null==e?void 0:e.propsForAnalytics)||{},v=p.customDateFilter,f=p.setCustomDateFilter,b=e.clicks,_=b.individual_clicks,E=(0,r.useState)(!1),k=(0,l.A)(E,2),w=(k[0],k[1]),x=e.activity,N=(x.darkMode,x.analyticsTab),A=e.settings.settings,L=e.analytics.analytics,O=betterLinksQuery.get("id");(0,r.useEffect)((function(){window.scrollTo(0,y.JT?0:270);var t=(0,y.Yq)(v[0].startDate,"yyyy-mm-dd"),a=(0,y.Yq)(v[0].endDate,"yyyy-mm-dd")||(0,y.Yq)(new Date,"yyyy-mm-dd"),n=betterLinksHooks.applyFilters("betterLinksAnalyticsFilterStartDate",(0,h.A)(new Date,30));n=t||(0,y.Yq)(n,"yyyy-mm-dd"),A||e.fetch_settings_data(),L||e.fetch_analytics_settings(),b||e.fetch_clicks_data({from:n,to:a,setLoading:w})}),[]),(0,r.useEffect)((function(){var t=(0,y.Yq)(v[0].startDate,"yyyy-mm-dd"),a=(0,y.Yq)(v[0].endDate,"yyyy-mm-dd")||(0,y.Yq)(new Date,"yyyy-mm-dd"),n=betterLinksHooks.applyFilters("betterLinksAnalyticsFilterStartDate",(0,h.A)(new Date,30));n=t||(0,y.Yq)(n,"yyyy-mm-dd"),e.fetch_individual_clicks({link_id:O,from:n,to:a,setLoading:w})}),[O]);var C=(0,r.useCallback)((0,y.GH)(L,N,O),[L]),D=null!=A&&A.is_disable_analytics_ip?C.filter((function(e){return"ip"!==e.selector})):C,P=(0,xe.A)(new Set(null==_||null===(t=_[O])||void 0===t?void 0:t.analytics.map((function(e){return e.ip})))).length;return(0,r.createElement)("div",{className:"btl-analytic"},(0,r.createElement)(g.A,{isOpenModal:d,closeModal:m}),null!=_&&_[O]?(0,r.createElement)(be,{data:R(null==_?void 0:_[O].graph_data),customDateFilter:v,setCustomDateFilter:f,setLoading:w,uniqueIpCount:P}):(0,r.createElement)(ve,null),(0,r.createElement)(Ae.A,{mode:"white",notice:(0,s.__)("To use the Parameter Tracking feature, please ensure that you have at least BetterLinks Pro v2.1.0 or later","betterlinks"),compatibleProVersion:"2.1.0"}),O&&(0,r.createElement)(Ne,{clicks:null!=_&&null!==(a=_[O])&&void 0!==a&&a.link_details?null==_||null===(n=_[O])||void 0===n?void 0:n.link_details:{link_title:null,short_url:null,target_url:null}}),(0,r.createElement)("div",{className:"btl-analytic-table-wrapper btl-analytic-table-wrapper-single-clicks"},(0,r.createElement)(V,{id:O,columns:D,data:(null==_||null===(i=_[O])||void 0===i?void 0:i.analytics)||[],progressPending:null==_||null===(c=_[O])||void 0===c||!c.analytics})))}));var Oe,Ce,De=a(61582);function Pe(){return Pe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},Pe.apply(null,arguments)}var Se,je,Fe=function(e){return r.createElement("svg",Pe({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),Oe||(Oe=r.createElement("g",{clipPath:"url(#total-click_svg__a)"},r.createElement("path",{stroke:"#9FA1B8",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7.904 17.563a1.2 1.2 0 0 0 2.228.308l2.09-3.093 4.907 4.907a1.066 1.066 0 0 0 1.509 0l1.047-1.047a1.066 1.066 0 0 0 0-1.509l-4.907-4.907 3.113-2.09a1.2 1.2 0 0 0-.309-2.228L4 4z"}))),Ce||(Ce=r.createElement("defs",null,r.createElement("clipPath",{id:"total-click_svg__a"},r.createElement("path",{fill:"#fff",d:"M0 0h24v24H0z"})))))};function He(){return He=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},He.apply(null,arguments)}var Me,qe,Te=function(e){return r.createElement("svg",He({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),Se||(Se=r.createElement("g",{stroke:"#9FA1B8",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.8,clipPath:"url(#unique-click_svg__a)"},r.createElement("path",{d:"M8 13V4.5a1.5 1.5 0 0 1 3 0V12m0-.5v-2a1.5 1.5 0 1 1 3 0V12m0-1.5a1.5 1.5 0 1 1 3 0V12"}),r.createElement("path",{d:"M17 11.5a1.5 1.5 0 1 1 3 0V16a6 6 0 0 1-6 6h-2 .208a6 6 0 0 1-5.012-2.7L7 19q-.468-.718-3.286-5.728a1.5 1.5 0 0 1 .536-2.022 1.87 1.87 0 0 1 2.28.28L8 13M5 3 4 2m0 5H3m11-4 1-1m0 4h1"}))),je||(je=r.createElement("defs",null,r.createElement("clipPath",{id:"unique-click_svg__a"},r.createElement("path",{fill:"#fff",d:"M0 0h24v24H0z"})))))};function ze(){return ze=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},ze.apply(null,arguments)}var Be=function(e){return r.createElement("svg",ze({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},e),Me||(Me=r.createElement("g",{stroke:"#9FA1B8",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,clipPath:"url(#tag_svg__a)"},r.createElement("path",{d:"M3 8v4.172a2 2 0 0 0 .586 1.414l5.71 5.71a2.41 2.41 0 0 0 3.408 0l3.592-3.592a2.41 2.41 0 0 0 0-3.408l-5.71-5.71A2 2 0 0 0 9.172 6H5a2 2 0 0 0-2 2"}),r.createElement("path",{d:"m18 19 1.592-1.592a4.82 4.82 0 0 0 0-6.816L15 6m-8 4h-.01"}))),qe||(qe=r.createElement("defs",null,r.createElement("clipPath",{id:"tag_svg__a"},r.createElement("path",{fill:"#fff",d:"M0 0h24v24H0z"})))))};const Ie=(0,d.Ng)((function(e){return{clicks:e.clicks,terms:e.terms}}),(function(e){return{get_analytics_unique_list_by_id:(0,u.zH)(P.Nr,e),get_analytics_graph_by_tag:(0,u.zH)(P.MI,e),fetch_all_tags:(0,u.zH)(De.XN,e)}}))((function(e){var t=(0,r.useState)(!1),a=(0,l.A)(t,2),n=a[0],i=a[1],c=e.tag_id,s=e.clicks,o=s.tag_clicks,u=s.tag_graphs,d=e.terms,m=d.tags,g=d.tag_analytics,p=(null==e?void 0:e.propsForAnalytics)||{},v=p.customDateFilter,f=p.setCustomDateFilter;(0,r.useEffect)((function(){var t=(0,y.Yq)(v[0].startDate,"yyyy-mm-dd"),a=(0,y.Yq)(v[0].endDate,"yyyy-mm-dd")||(0,y.Yq)(new Date,"yyyy-mm-dd"),n=(0,h.A)(new Date,30);n=t||(0,y.Yq)(n,"yyyy-mm-dd"),null!=o&&o[c]||e.get_analytics_unique_list_by_id({from:n,to:a,tag_id:c,setLoading:i}),null!=u&&u[c]||e.get_analytics_graph_by_tag({from:n,to:a,tag_id:c,setLoading:i}),m||e.fetch_all_tags()}),[c]);var b=(0,r.useCallback)((function(){var e;return{tagName:null==m||null===(e=m.find((function(e){return e.id===c})))||void 0===e?void 0:e.term_name,totalClicks:(null==g?void 0:g.total_clicks[c])||0,uniqueClicks:(null==g?void 0:g.unique_clicks[c])||0}}),[m])(),_=function(){return(0,r.createElement)(Ee.Ay,{speed:2,width:"100%",height:10,viewBox:"0 0 300 10",backgroundColor:"#f3f3f3",foregroundColor:"#ecebeb"},(0,r.createElement)("rect",{x:"1",y:"2",rx:"3",ry:"3",width:"196",height:"13"}))};return(0,r.createElement)("div",{className:"btl-analytic"},(0,r.createElement)(be,{data:R((null==u?void 0:u[c])||{total_count:[],unique_count:[]}),customDateFilter:v,setCustomDateFilter:f,loading:n,setLoading:i}),(null==b?void 0:b.tagName)&&(0,r.createElement)("div",{className:"btl-single-click-info-header"},(0,r.createElement)("div",{className:"btl-single-click-tag-info"},(0,r.createElement)("div",{className:"btl-single-info--name"},(0,r.createElement)("span",{className:"btl-single-info-svg-icon"},(0,r.createElement)(Be,null)),(0,r.createElement)("span",{className:"btl-column-name",style:{marginRight:"5px"}},"Tag Name:"),(0,r.createElement)("span",{className:"btl-link-name",title:null==b?void 0:b.tagName},(null==b?void 0:b.tagName)||(0,r.createElement)(_,null))),(0,r.createElement)("div",{className:"btl-single-info--name"},(0,r.createElement)("span",{className:"btl-single-info-svg-icon"},(0,r.createElement)(Fe,null)),(0,r.createElement)("span",{className:"btl-column-name",style:{marginRight:"5px"}},"Total Clicks:"),(0,r.createElement)("span",{className:"btl-link-name",title:"".concat(null==b?void 0:b.totalClicks," Clicks")},(null==b?void 0:b.totalClicks)||(0,r.createElement)(_,null))),(0,r.createElement)("div",{className:"btl-single-info--name",style:{marginRight:"15px"}},(0,r.createElement)("span",{className:"btl-single-info-svg-icon"},(0,r.createElement)(Te,null)),(0,r.createElement)("span",{className:"btl-column-name",style:{marginRight:"5px"}},"Unique Clicks:"),(0,r.createElement)("span",{className:"btl-link-name",title:"".concat(null==b?void 0:b.uniqueClicks," Clicks")},(null==b?void 0:b.uniqueClicks)||(0,r.createElement)(_,null))))),(0,r.createElement)("div",{className:"btl-analytic-table-wrapper btl-analytic-table-wrapper-manage-tags"},(0,r.createElement)(W,{analyticsTab:null,unique_list:null==o?void 0:o[c],loading:n,id:c,from:"manage_tags"})))}));function Re(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function Ye(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?Re(Object(a),!0).forEach((function(t){(0,n.A)(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):Re(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}const Ve=function(){var e=(0,r.useState)([{startDate:betterLinksHooks.applyFilters("betterLinksAnalyticsFilterStartDate",(0,h.A)(new Date,30)),endDate:new Date,key:"selection"}]),t=(0,l.A)(e,2),a={customDateFilter:t[0],setCustomDateFilter:t[1],isResetAnalytics:!0},n=c.parse(location.search),u=betterLinksQuery.get("id"),d=betterLinksQuery.get("tag_id");return(0,r.createElement)(i().Fragment,null,(0,r.createElement)(o.A,{propsForAnalytics:a,label:(0,s.__)("BetterLinks Analytics","betterlinks")}),d?(0,r.createElement)(Ie,{propsForAnalytics:a,tag_id:d}):betterLinksHooks.applyFilters("analyticsInnerChild",u?(0,r.createElement)(Le,{id:u,propsForAnalytics:a}):(0,r.createElement)(we,{propsForAnalytics:a}),Ye(Ye({},n),{},{Chart:m.A})))}}}]);