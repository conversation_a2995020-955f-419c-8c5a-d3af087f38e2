.wpnotice-thumbnail-wrapper img {
    display: block
}

.notice.notice-betterlinks-black_friday_notice {
    border-left-color: #aa02d3
}

.betterlinks .wpnotice-content-wrapper {
    padding: 10px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.wpnotice-content-wrapper > p {
    margin-top: 0
}

.wpnotice-content-wrapper > a.button, .wpnotice-content-wrapper > button.dismiss-btn {
    text-transform: capitalize
}

.wpnotice-content-wrapper > a.button-primary, .wpnotice-content-wrapper > a.button-primary:focus {
    background-color: #593dbd;
    border-color: #593dbd
}

.wpnotice-content-wrapper > a.button-primary:hover {
    background-color: #532ae3
}

.wpnotice-content-wrapper > .button-campaign {
    color: #593dbd;
    border-color: #593dbd
}

.wpnotice-content-wrapper > .button-link {
    margin-left: 5px
}

.wpnotice-content-wrapper > .button-link:hover {
    background-color: transparent
}

.notice-betterlinks-opt_in {
    display: block !important
}

.notice-betterlinks-review {
    padding: 10px;
    background-color: #fff;
    border-radius: 3px;
    margin: 15px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.notice-betterlinks-review:after, .wpdeveloper-review-notice:after {
    content: "";
    display: table;
    clear: both
}

.notice-betterlinks-review .wpnotice-content-wrapper {
    display: block;
}

.wpdeveloper-notice-thumbnail img {
    -webkit-transition: all .3s;
    -o-transition: all .3s;
    width: 72px;
    opacity: .85;
    transition: all .3s
}

.wpdeveloper-update-notice .wpdeveloper-notice-thumbnail img, .wpdeveloper-update_400k-notice .wpdeveloper-notice-thumbnail img, .wpdeveloper-upsale-notice .wpdeveloper-notice-thumbnail img {
    width: 32px
}

.wpdeveloper-update-notice .wpdeveloper-notice-thumbnail, .wpdeveloper-update_400k-notice .wpdeveloper-notice-thumbnail {
    width: auto;
    padding: 7px
}

.wpdeveloper-update-notice .wpdeveloper-notice-message, .wpdeveloper-update_400k-notice .wpdeveloper-notice-message {
    padding: 5px 0
}

.wpdeveloper-update-notice, .wpdeveloper-update_400k-notice {
    border-color: #6648fe;
    padding: 0
}

a.ea-notice-cta {
    background-color: #4d18ff;
    background: linear-gradient(-30deg, #4d18ff, #9a7cff);
    margin-top: 30px;
    color: #fff;
    padding: 8px 20px;
    outline: 0;
    text-decoration: none;
    border-radius: 3px;
    transition: all .3s ease
}

a.ea-notice-cta:hover {
    opacity: .85
}

span.coupon-code {
    background: #ebebeb;
    padding: 5px 10px;
    letter-spacing: .035em
}

.eael-review-text {
    overflow: hidden
}

.eael-review-text h3 {
    font-size: 24px;
    margin: 0 0 5px;
    font-weight: 400;
    line-height: 1.3
}

.betterlinks-review-text p, .eael-review-text p {
    font-size: 13px;
    margin: 0 0 5px
}

.betterlinks-notice-links {
    margin: 8px 0 0;
    padding: 0
}

.betterlinks-notice-links li {
    display: inline-flex;
    margin-right: 15px;
    align-items: center
}

.betterlinks-notice-links li a, .wpdeveloper-notice-link li a {
    display: inline-block;
    color: #10738b;
    text-decoration: none;
    position: relative
}

.wpdeveloper-upsale-notice .wpdeveloper-notice-message {
    display: -webkit-box;
    display: -ms-flexbox;
    -webkit-box-align: center;
    -ms-flex-align: center
}

.wpdeveloper-upsale-notice #plugin-install-core, a.ea-notice-cta {
    margin-left: 10px
}

.wpdeveloper-upsale-notice {
    display: -webkit-box;
    display: -ms-flexbox
}

.wpdeveloper-upsale-notice .wpdeveloper-notice-thumbnail {
    padding: 10px;
    width: 40px
}

.toplevel_page_eael-settings .wp-menu-image img {
    max-width: 20px;
    padding-top: 8px !important
}

.wpdeveloper-upsale-notice .wpdeveloper-notice-message .button {
    margin-left: 15px
}

.notice-has-thumbnail.wpdeveloper-freedom30-notice {
    display: grid;
    grid-template-columns:70px 1fr;
    align-items: center;
    position: relative
}

.notice-has-thumbnail.wpdeveloper-freedom30-notice .wpdeveloper-notice-thumbnail {
    width: 70px
}

.notice-has-thumbnail.wpdeveloper-freedom30-notice .wpdeveloper-notice-thumbnail img {
    width: 50px
}

.betterlinks-notice {
    background-color: #f7f6d4;
    padding: 10px 15px;
    border-radius: 5px;
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, .15);
    max-width: 90%;
    color: #9a7223
}

.betterlinks-notice h5 {
    font-size: 1.2em;
    font-weight: 700
}

.betterlinks-error-notice, .betterlinks-success-notice {
    background-color: #fff5f5;
    color: #e8606d;
    padding: 30px;
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, .05)
}

.betterlinks-success-notice {
    background-color: #f5f5f5;
    color: #26a9b4;
    padding: 20px;
    display: block
}

.betterlinks-error-notice h3 {
    color: #e24c4b;
    margin: 15px 0
}

.betterlinks-success-notice h3 {
    color: #6c62ff;
    margin: 15px 0
}

.wpdeveloper-review-notice {
    padding: 10px;
    background-color: #fff;
    border-radius: 3px;
    margin: 15px;
    border-left: 4px solid #00a0d2;
    display: flex;
    align-items: center
}

.wpdeveloper-notice-thumbnail {
    width: 90px;
    float: left;
    padding: 5px;
    text-align: center;
    border-right: 4px solid transparent
}

.wpdeveloper-notice-thumbnail img:hover {
    opacity: 1
}

.betterlinks-review-text {
    overflow: hidden
}

.betterlinks-review-text h3 {
    font-size: 24px;
    margin: 0 0 5px;
    font-weight: 400;
    line-height: 1.3
}

.wpdeveloper-notice-link {
    margin: 8px 0 0;
    padding: 0
}

.wpdeveloper-notice-link li {
    display: inline-block;
    margin-right: 15px
}

.wpdeveloper-notice-link li a {
    padding-left: 26px
}

.wpdeveloper-notice-link li a span {
    position: absolute;
    left: 0;
    top: -2px
}

.wpdeveloper-notice-message {
    padding: 10px 0
}

.wpdeveloper-upsale-notice .wpdeveloper-notice-message {
    display: flex;
    align-items: center;
    padding: 10px 0
}

.wpdeveloper-upsale-notice .wpdeveloper-notice-message + .notice-dismiss {
    top: 18px
}

.notice.notice-has-thumbnail {
    padding-left: 0
}

.wpdeveloper-upsale-notice {
    display: flex
}

.betterlinks-upsale-notice .wpdeveloper-notice-thumbnail {
    padding: 0;
    width: 80px;
    display: flex;
    align-items: center;
    justify-content: center
}

.betterlinks-upsale-notice .wpdeveloper-notice-thumbnail img {
    width: 50px;
    padding: 8px
}

a.betterlinks-notice-cta {
    margin-left: 10px !important
}

.betterlinks-dashboard-body .notice.betterlinks-usage-notice {
    display: flex;
    align-content: center;
    align-items: center
}

.betterlinks-usage-notice-img .betterlinks-logo-inline {
    width: 60px;
    display: block;
    box-shadow: 1px 0 0 0 rgba(0, 0, 0, .1);
    margin-right: 10px;
    padding: 15px 5px 5px 0
}

.toplevel_page_betterlinks .btl-menu-notice {
    background: red;
    position: absolute;
    bottom: 24px;
    right: 0;
    display: inline-block;
    vertical-align: top;
    box-sizing: border-box;
    margin: 1px 0 -1px 2px;
    padding: 0 5px;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    background-color: #d63638;
    color: #fff;
    font-size: 11px;
    line-height: 1.6;
    text-align: center;
    z-index: 26
}

.btl-dashboard-notice {
    margin: 0 0 10px -30px !important;
    background: linear-gradient(202deg, #2961ff 0, #003be2 100%);
    color: #fff;
    padding: 15px 10px;
    position: relative;
    display: flex;
    align-items: center;
    border: unset !important
}

.btl-dashboard-notice p {
    width: 100%;
    text-align: center
}

.btl-dashboard-notice p a {
    color: #fff;
    font-weight: 700
}

.btl-dashboard-notice .notice-dismiss {
    top: unset
}

.btl-dashboard-notice .notice-dismiss::before {
    color: #fff
}

.toplevel_page_betterlinks a.toplevel_page_betterlinks:has(.wp-menu-name>span), .toplevel_page_betterlinks a.toplevel_page_betterlinks:has(.wp-menu-name>span):focus {
    background-color: #00f !important;
    color: #fff !important
}

.notice-betterlinks-betterlinks_4th_year {
    border-left-color: transparent;
    border-image: linear-gradient(to bottom, #6128ed, #5a0090) 1 100%
}

.notice-betterlinks-betterlinks_halloween .wpnotice-content-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%
}

.wpinsights-form-active-betterlinks .wpinsights-goodbye-form-bg {
    background: rgba(0, 0, 0, .8);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9
}

.wpinsights-goodbye-form-wrapper-betterlinks {
    position: relative;
    display: none
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form {
    display: none
}

.wpinsights-form-active-betterlinks .wpinsights-goodbye-form {
    position: relative !important;
    max-width: 80%;
    background: #fff;
    box-shadow: 2px 8px 23px 3px rgba(0, 0, 0, .2);
    white-space: normal;
    display: block;
    z-index: 999999
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-head {
    box-shadow: 0 0 8px rgba(0, 0, 0, .1);
    font-size: 15px
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form .wpinsights-goodbye-form-head strong {
    font-size: 15px
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body {
    color: #333
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body label {
    padding-left: 4px;
    color: #475467;
    font-size: 14px
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body .wpinsights-goodbye-form-caption {
    line-height: 1.4
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body #wpinsights-goodbye-options {
    padding-top: 0
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body #wpinsights-goodbye-options ul > li {
    margin-bottom: 15px
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body #wpinsights-goodbye-options ul > li > div {
    display: inline;
    padding-left: 3px
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body #wpinsights-goodbye-options ul > li > div > input, .wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body #wpinsights-goodbye-options ul > li > div > textarea {
    padding: 10px 16px;
    border-radius: 8px;
    width: 100%;
    border: 1px solid #d0d5dd;
    margin: 16px 0 0;
    color: #1d2939;
    font-size: 14px;
    line-height: 1.5em;
    resize: none;
    max-height: 44px
}

.wpinsights-goodbye-form-wrapper-betterlinks .deactivating-spinner {
    display: none;
    padding-bottom: 20px !important
}

.wpinsights-goodbye-form-wrapper-betterlinks .deactivating-spinner .spinner {
    float: none;
    margin: 4px 4px 0 18px;
    vertical-align: bottom;
    visibility: visible
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-footer .wpinsights-submit-btn {
    background-color: #f3bafd
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-footer .wpinsights-submit-btn:hover {
    background-color: #f5d0fe;
    background: conic-gradient(from 195.22deg at 68.31% 39.29%, rgba(143, 32, 251, 0), #8f20fb 360deg), linear-gradient(0deg, #6f0af2, #6f0af2)
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-footer .wpinsights-deactivate-btn {
    font-size: 13px;
    color: #a4afb7;
    background: 0 0;
    float: right;
    padding-right: 10px;
    width: auto;
    text-decoration: underline
}

.wpinsights-form-active-betterlinks .wpinsights-goodbye-form-wrapper-betterlinks {
    display: flex !important;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    z-index: 9999;
    height: 100%;
    justify-content: center;
    align-items: center
}

.wpinsights-form-active-betterlinks .wpinsights-goodbye-form {
    border-radius: 8px;
    width: 563px;
    overflow: visible
}

.wpinsights-goodbye-form .ea__modal-close-btn {
    position: absolute;
    top: 0;
    right: -55px;
    width: 40px;
    height: 40px;
    background: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
    color: #475467;
    cursor: pointer
}

.wpinsights-goodbye-form .ea__modal-close-btn svg {
    width: 20px
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-head {
    background: #fff;
    color: #495157;
    padding: 24px;
    border-radius: 8px 8px 0 0;
    box-shadow: none;
    border-bottom: 1px solid #eaecf0;
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-head > span {
    display: inline-flex
}

.wpinsights-goodbye-form-head h1 {
    font-size: 22px;
    font-weight: 450;
    color: #1d2939;
    padding: 0
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body {
    padding: 32px 40px;
    max-height: calc(70vh - 230px);
    overflow: auto
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body .wpinsights-goodbye-form-caption {
    font-size: 18px;
    color: #1d2939;
    margin: 0;
    padding-bottom: 16px;
    font-weight: 400
}

.widefat td ul {
    font-size: 14px;
    margin: 0;
    color: #475467;
    line-height: 1.4em
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body #wpinsights-goodbye-options ul > li:not(:last-child) {
    margin-bottom: 16px
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body #wpinsights-goodbye-options ul > li:last-child {
    margin-bottom: 0
}

.wpinsights-goodbye-form-wrapper-betterlinks input[type=radio] {
    border-radius: 50%;
    margin-right: .25rem;
    line-height: .71428571;
    margin: 0;
    border: 1px solid #d0d5dd;
    box-shadow: none
}

.wpinsights-goodbye-form-wrapper-betterlinks input[type=radio]:checked::before {
    content: "";
    border-radius: 50%;
    width: .88rem;
    height: .88rem;
    margin: -.95px;
    background-color: #750ef4;
    border: 1px solid #e2cbff;
    line-height: 1.14285714;
    outline: 0
}

.wpinsights-goodbye-form-wrapper-betterlinks input[type=radio]:focus {
    border-color: #d0d5dd;
    box-shadow: none;
    outline: 0
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body #wpinsights-goodbye-options ul > li > div > input::placeholder, .wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-body #wpinsights-goodbye-options ul > li > div > textarea::placeholder {
    color: #c6cbd2 !important
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-footer {
    padding: 16px 36px;
    margin-bottom: 0;
    border-top: 1px solid #eaecf0
}

input:focus, textarea:focus {
    box-shadow: none !important;
    outline: 0 !important
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-footer > .wpinsights-goodbye-form-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-start
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-footer .wpinsights-submit-btn {
    background: conic-gradient(from 195.22deg at 68.31% 39.29%, rgba(143, 32, 251, 0), #8f20fb 360deg), linear-gradient(0deg, #6f0af2, #6f0af2);
    -webkit-border-radius: 8px;
    border-radius: 8px;
    color: #fff;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5em;
    text-transform: capitalize;
    transition: .3s
}

.wpinsights-goodbye-form-wrapper-betterlinks .wpinsights-goodbye-form-footer .wpsp-put-deactivate-btn {
    font-size: 14px;
    font-weight: 500;
    color: #344054;
    margin-left: 10px
}

.wp-person a:focus .gravatar, a:focus, a:focus .media-icon img, a:focus .plugin-icon {
    color: #043959;
    box-shadow: none;
    outline: 0
}
