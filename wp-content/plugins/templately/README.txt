=== Templately – <PERSON><PERSON><PERSON> & <PERSON><PERSON>nberg Template Library: 5500+ Free & Pro Ready Templates And Cloud! ===
Contributors: templately, wpdevteam, re_enter_rupok, Asif2BD, priyomukul, rahat89, alimuz<PERSON><PERSON><PERSON>
Donate link: https://templately.com
Tags: templates, elementor template, <PERSON><PERSON><PERSON> template, landing page, WoCommerce Templates
Requires at least: 5.0
Tested up to: 6.8
Requires PHP: 7.2
Stable tag: 3.2.8
License: GPLv3 or later
License URI: http://www.gnu.org/licenses/gpl-3.0.html

Ultimate Free Templates Cloud for WordPress 5000+ Free & Premium Designs for Elementor templates & Gutenberg block templates

== Description ==

= ULTIMATE FREE TEMPLATES CLOUD FOR WORDPRESS – ELEMENTOR & GUTENBERG SUPPORTED =

Take your favorite Page Builders to a whole new level & manage all your designs in one place with [Templately](https://templately.com)

https://www.youtube.com/watch?v=coLxfjnrm3I

## 🔥 THE FUTURE OF PAGE BUILDING WITH CLOUD  ##

Save all your designs with Templately & deploy in hundreds of websites with 1-click. Increase productivity and power up your whole team to build websites faster than ever before.

### 🌟 STUNNING FREE TEMPLATES: ###

Sign up right now and get started with beautiful Templates. You can just import these designs with 1 click & start creating WordPress pages.

### 🔥 Full Site Import: ###

Templately Full Site Import will help you import your entire template pack just in one click. It will change your existing design & not at all affect your current designs.

### 🚀 FREE STORAGE AT MYCLOUD ###

Save your design templates in MyCloud storage and access all your Saved Templates anytime from any device.

### 🤝 COLLABORATE AT MY WORKSPACE ###

Invite your team to work together while building a website and get your work done faster than ever.

### ⚙️ SEAMLESS INTEGRATION WITH PAGE BUILDERS ###

Create landing pages with Elementor, Gutenberg and boost up your page building experience to the next level.

### 🔥 OVER 2800 ELEMENTOR TEMPLATES AVAILABLE ALREADY ###

Elementor Templates are the easiest way to build websites fast with the most popular website builder, we have 2800+ templates available already.

### 🔥 READY TO USE 2800+ GUTENBERG TEMPLATES ###

Unlock the true possibilities of the Gutenberg block editor with ready-to-use templates from Templately. More than 2800 templates are ready to build your website in one click.

### 👨‍💻 SUPER FRIENDLY SUPPORT ###

Get help and assistance from our dedicated forum support and create stunning landing pages with Templately.


### 🔥 ADVANCED PREMIUM TEMPLATES [PRO] ###

Create conversion optimized landing pages for every niche using the Templately PRO templates packs.


🙌 After reading this feature list, you can probably imagine Templately can change the way you used to create websites and save plenty of time. So, do you want to unlock the advanced features? Upgrade to our Pro version.


## 🚀 BACKED BY A TRUSTED TEAM ##

This Templates Cloud is brought to you by the team behind [WPDeveloper](https://wpdeveloper.com), a dedicated marketplace for WordPress, trusted by over 6 Million+ happy users.

## 👨‍💻 DOCUMENTATION AND SUPPORT ##

- For documentation and tutorials go to our [documentation](https://docs.templately.com/)
- If you have any more questions, visit our support on the [Plugin's Forum](https://wordpress.org/support/plugin/templately).
- For more information about features, FAQs and documentation, check out our website at [Templately](https://templately.com/).

## 💙 LOVED Templately? ##

- Join our [Facebook Group](https://www.facebook.com/groups/wpdeveloper.net/)
- If you love Templately, rate us on [WordPress](https://wordpress.org/support/plugin/templately/reviews/?rate=5#new-post)

## 🔥 WHAT’S NEXT ##

If you like Templately, then consider checking out our other WordPress Plugins:

- 🔝 [Essential Addons For Elementor](https://wordpress.org/plugins/essential-addons-for-elementor-lite/) – Most popular Elementor extensions with 2 Million+ active users in the WordPress repository.
- 🧱 [Essential Blocks for Gutenberg](https://wordpress.org/plugins/essential-blocks/) – Ultimate Gutenberg block library with 45+ exclusive blocks.
- 🔔 [NotificationX](https://notificationx.com/) – Best Social Proof & FOMO Marketing Solution to increase conversion rates.
- ⏰ [SchedulePress](https://wordpress.org/plugins/wp-scheduled-posts/) – Complete solution for WordPress Post Scheduling to manage schedules through an editorial calendar.
- 📄 [EmbedPress](https://wordpress.org/plugins/embedpress/): EmbedPress lets you embed videos, images, posts, audio, maps and upload PDF, DOC, PPT & all other types of content into your WordPress site with one-click and showcase it beautifully for the visitors.
- 📚 [BetterDocs](https://wordpress.org/plugins/betterdocs/): Best Documentation & Knowledge Base Plugin for WordPress reduce manual support tickets & improve user experience.
- 💼 [easy.jobs](https://wordpress.org/plugins/easyjobs/): One-stop recruitment solution for finding the right talent with an AI-powered screening system, and much more.

Visit [WPDeveloper](https://wpdeveloper.com/) to learn more about how to do better in WordPress with [Help Tutorial, Tips & Tricks](https://wpdeveloper.com/blog).

== Installation ==

= Modern Way: =
1. Go to the WordPress Dashboard "Add New Plugin" section.
2. Search For "Templately".
3. Install, then Activate it.
4. Follow the [Documentation](https://docs.templately.com/)

= Old Way: =
1. Upload `templately` to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Follow the [Documentation](https://docs.templately.com/)


== Frequently Asked Questions ==

= Does it work with any WordPress theme? =

Yes, it will work with any standard WordPress theme.

= How to use Templately templates? =
First you have to install Templately on your website then choose your favorite template from the dashboard. With just a single click you can insert Templately templates on your website.

= Does Templately have WooCommerce Templates? =
Templately has a lot of eCommerce templates which fully supports WooCommerce. So, you can build your WooCommerce store with Templately Templates.

= Can I save my Templates for reusing? =
Yes. Template comes with Cloud Storage. You can easily save your templates there and reuse them anytime.

= Does Templately have free templates? =
Yes, We have many free templates for Elementor & Gutenberg that you can use to build your website.

= Will you add more Elementor templates & Gutenberg templates? =
Every month we are adding new Elementor templates, along with Gutenberg templates & blocks, to help you build stunning websites.


== Screenshots ==

1. Importing Templately Template
2. Saving Templates in Cloud
3. Template Gallery
4. Templately My Clouds
5. Templately My WorkSpace


== Changelog ==

= 3.2.8 – 20-07-2025 =
Fixed: Saved templates can’t delete from Templately cloud workspace.
Improved: Security enhancements (Reported by Patchstack).
Few minor bug fixes & improvements

= 3.2.7 – 30-06-2025 =
Improved: Optimized attachment image import process using FSI.
Fixed: Templately settings page was not loading correctly.
Few minor bug fixes & improvements

= 3.2.6 – 25-05-2025 =
Added: Templately Settings page.
Fixed: Button hiding issue on older WordPress versions.
Few minor bug fixes & improvements.

= 3.2.5 – 16-04-2025 =
Fixed: Text domain issue.
Fixed: Fatal error when previewing pages built with Templately Builder without Elementor installed.
Improved: Image import process for Elementor templates
Few minor bug fixes & improvements

= 3.2.4 – 11-03-2025 =
Improved: Full pack import process using FSI.
Fixed: Pagination issue on Templately Cloud.
Added: Option to hide Templately and “Save Post in Templately” from the Gutenberg editor.
Fixed: Duplicate viewport issue in Gutenberg-based themes.
Few minor bug fixes and overall improvements.

= 3.2.3 – 27-02-2025 =
Fixed: PHP warnings on the Templately dashboard.
Few minor bug fixes and improvements.

= 3.2.2 – 13-02-2025 =
Fixed: Issue with syncing saved templates to Templately Cloud.
Fixed: Single Page templates were not working properly on pages.
Fixed: PHP warning when trying to read property “ID” during import.
Fixed: Template import issue when Elementor Container was disabled.
Few minor bug fixes and improvements.

= 3.2.1 – 02-02-2025 =
Improved: Optimized import process for faster full-site importing.
Few minor bug fix and Improvements.

= 3.2.0 – 31-12-2024 =
Added: Global Typography control for Full site import.
Few minor bug fix & improvements.

= Earlier versions =
Please refer to the separate changelog.txt file.

== Upgrade Notice ==

[Major Update] Must Update.
