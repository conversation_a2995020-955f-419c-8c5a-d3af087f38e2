== Changelog ==
= 3.2.8 – 12-07-2025 =
Fixed: Saved templates can’t delete from Templately cloud workspace.
Improved: Security enhancements (Reported by Patchstack).
Few minor bug fixes & improvements

= 3.2.7 – 30-06-2025 =
Improved: Optimized attachment image import process using FSI.
Fixed: Templately settings page was not loading correctly.
Few minor bug fixes & improvements

= 3.2.6 – 25-05-2025 =
Added: Templately Settings page.
Fixed: Button hiding issue on older WordPress versions.
Few minor bug fixes & improvements.

= 3.2.5 – 16-04-2025 =
Fixed: Text domain issue.
Fixed: Fatal error when previewing pages built with Templately Builder without Elementor installed.
Improved: Image import process for Elementor templates
Few minor bug fixes & improvements

= 3.2.4 – 11-03-2025 =
Improved: Full pack import process using FSI.
Fixed: Pagination issue on Templately Cloud.
Added: Option to hide Templately and “Save Post in Templately” from the Gutenberg editor.
Fixed: Duplicate viewport issue in Gutenberg-based themes.
Few minor bug fixes and overall improvements.

= 3.2.3 – 27-02-2025 =
Fixed: PHP warnings on the Templately dashboard.
Few minor bug fixes and improvements.

= 3.2.2 – 13-02-2025 =
Fixed: Issue with syncing saved templates to Templately Cloud.
Fixed: Single Page templates were not working properly on pages.
Fixed: PHP warning when trying to read property “ID” during import.
Fixed: Template import issue when Elementor Container was disabled.
Few minor bug fixes and improvements.

= 3.2.1 – 31-12-2024 =
Improved: Optimized import process for faster full-site importing.
Few minor bug fix and Improvements.

= 3.2.0 – 31-12-2024 =
Added: Global Typography control for Full site import.
Few minor bug fix & improvements.

= 3.1.11 - 22/12/2024 =
Fixed: Template importing was getting stuck on some servers.
Few minor bug fix & improvements.

= 3.1.10 – 04-12-2024 =
Fixed: Sync issues for saved templates on Templately Cloud.
Fixed: Archive page links were not updating in menus.
Few minor bug fixes & improvements

= 3.1.9 – 20-11-2024 =
Fixed: Issues with importing Gutenberg template images.
Fixed: FSI progress counter accuracy during template imports.
Few minor bug fixes & improvements

= 3.1.8 – 14-11-2024 =
Added: WordPress 6.7 compatibility.
Fixed: FSI revert functionality was not working properly.
Fixed: Issues with Essential Blocks asset generation for Gutenberg templates.
Few minor bug fixes & improvements

= 3.1.7 – 27-10-2024 =
Added: option to set specific Headers and Footers for Pages or Posts.
Fixed: resolved a fatal error when creating Single Product templates.
Fixed: site-wide Header and Footer preview issues.
Improved: Optimized FSI import for LiteSpeed servers.
Improved: Feedback form submission process.
Few minor bug fixes & improvements

= 3.1.6 – 08-10-2024 =
Fixed: Template import issue on Lightspeed server.
Fixed: Broken Gutenberg header and footer on Single Post pages.
Enhanced: Security improvements (reported by Patchstack).
Improved: Template import feature.
Few minor bug fixes & improvements

= 3.1.5 – 03-09-2024 =
Fixed: Header/Footer issues with Gutenberg templates in Templately Theme Builder.
Fixed: FSI revert functionality when Elementor is deactivated.
Fixed: Saved Templates not syncing to Cloud without Elementor.
Other: Minor bug fixes and improvements.

= 3.1.4 – 22-08-2024 =
Enhanced: Security improvements reported by Patchstack
Few minor bug fixes & improvements.

= 3.1.3 – 20-08-2024 =
Added: Option to revert Full Site Import (FSI).
Fixed: Header and footer issues with Gutenberg templates after full site import.
Few minor bug fixes and improvements.

= 3.1.2 – 31-07-2024 =
Fixed: Conflict with Elementor during full site import.
Fixed: Missing validation messages for workspace.
Improved: Workspace name validation added on MyCloud.
Few minor bug fixes & improvements.

= 3.1.1 – 15-07-2024 =
Added: WordPress 6.6 compatibility.
Fixed: WPML compatibility issue.
Few minor bug fixes & improvements

= 3.1.0 – 10-07-2024 =
Added: Global color control for FSI
Added: Live logo adding control for FSI
Fixed: Template import issues in Multisite.
Few minor bug fixes & improvements

= 3.0.9 – 13-06-2024 =
Improved: Added Name (32 chars max) and Password (64 chars max) validation.
Improved: Plugin dependency in single template import.
Minor bug fixes and improvements.

= 3.0.8 – 30-05-2024 =
Fixed: Dependent plugin installation error.
Few minor bug fixes & improvements

= 3.0.7 – 30-05-2024 =
Fixed: Templately theme builder type error.
Fixed: Swiper JS loading issue for Single Product page.
Few minor bug fixes & improvements

= 3.0.6 – 09-05-2024 =
Fixed: CSS loading issue for Gutenberg templates.
Fixed: Full Site Import issue on LiteSpeed server
Fixed: Navigation wasn’t working for Gutenberg packs.
Fixed: Theme builder template was saving as post type instead of template.
Improved: Navigation for FSI modal.
Few minor bug fixes & improvements

= 3.0.5 – 25-04-2024 =
Fixed: Header Footer style was blinking after a hard reload.
Fixed: Template unzipping issue for specific server.
Fixed: Full site import count issue after a failed import.
Few minor bug fixes & improvements

= 3.0.4 – 21-03-2024 =
Added: WordPress 6.5 compatibility.
Fixed: Template import issues in LiteSpeed Server.
Fixed: Template import issues in Gutenberg Editor.
Fixed: Elementor editor modal's cancel button close the whole Templately modal.
Fixed: Same form was importing multiple times when it was used in multiple pages.
Improved: Templates Display Condition from Templately Theme Builder.
Few minor bug fixes & improvements

= 3.0.3 – 05-03-2024 =
Improved: Dependency plugin installation process for FSI.
Improved: Dashboard UI for better user experience.
Few minor bug fixes & improvements

= 3.0.2 – 18-02-2024 =
Fixed: Gutenberg image import to media library.
Fixed: Shows error modal if account isn't verified before importing.
Fixed: Changing the image of a block breaks its design.
Improved: Templately dashboard network/timeout issue.
Improved: Template import execution time.
Few minor bug fixes & improvements

= 3.0.1 – 30-01-2024 =
Fixed: Full site import issue when WordPress is installed in a subdirectory.
Few minor bug fixes & improvements

= 3.0.0 – 24-01-2024 =
Added: Templately Full Site Import 🚀
Few minor bug fixes & improvements

= 2.2.13 – 09-01-2024 =
Fixed: Page layout breaking issue while importing on Elementor.
Improved: Template search option
Few minor bug fixes & improvements

= 2.2.12 - 06-12-2023 =
Fixed: Templately wasn't opening from Elementor editor
Fixed: Warning on PHP 7.3
Improved: WorkSpace sharing option via Email
Improved: Rating given process in pack sidebar
Few minor bug fixes & improvements

= 2.2.11 - 30-11-2023 =
Improved: Pack sidebar design.
Fixed: Template save in Templately for Flexbox Container
Few minor bug fixes & improvements.

= 2.2.10 - 20-11-2023 =
Fixed: Responsive issues in login form.
Few minor bug fixes & improvements.

= 2.2.9 - 8-11-2023 =
Added: WordPress 6.4 compatibility.
Fixed: Few minor bug fixes & improvements.

= 2.2.8 - 05-11-2023 =
Added: Instant Customer Support option.
Fixed: Few responsive issues.
Few minor bug fixes & improvements.

= 2.2.7 - 31-10-2023 =
Revamped: Login Screen UI.
Improved: Added few more validation in Password field & WorkSpace.
Few minor bug fixes and improvements.

= 2.2.6 - 09-10-2023 =
Fixed: Security issue reported by WPScan.
Improvement: Featured item filtering for packs.
Few minor bug fixes and improvements.

= 2.2.5 - 20-09-2023 =
Fixed: Full section was not appearing when certain dependency widgets were missing in that section in Elementor editor.
Fixed: Templates were breaking in Gutenberg after the dependency plugins were installed from the plugin repository.
Fixed: Importing from My Favorites doesn't display the dependency plugins.
Few minor bug fixes and improvements.

= 2.2.4 - 30-08-2023 =
Fixed: PHP 7.2 Compatibility Issue
Few minor bug fixes and improvements.

= 2.2.3 - 28-08-2023 =
Few minor bug fixes and improvements.

= 2.2.2 - 26-07-2023 =
Fixed: Login Issue for certain users
Few minor bug fixes and improvements.

= 2.2.1 - 12-06-2023 =
Few minor bug fixes and improvements.

= 2.2.0 - 11-05-2023 =
Added: New pricing plan based on active sites.
Fixed: Wrong purchase link in Import Failed Modal Button.
Few minor bug fixes and improvements.

= 2.1.0 - 15-02-2023 =
Added: Individual pricing for Templately
Added: Purchased Items section on profile area
Fixed: Image ordering issue on pack thumbnail
Improvement: Single template/block sidebar UI
Improvement: Modal design improvement on Elementor
Few minor bug fixes and improvements.

= 2.0.6 - 13-12-2022 =
Hot Fix: Elementor Header and Footer template insert issue on elementor editor.
Few minor bug fixes and improvements

= 2.0.5 - 12-12-2022 =
Added: Login button on the main screen.
Fixed: Critical Error while importing template on Library.
Fixed: Button color overwrite issue in elementor dark mode.
Improvement: Added proper notice when dependency plugin not exists.
Few minor bug fixes and improvements

= 2.0.4 - 22-11-2022 =
Few minor bug fixes and improvements

= 2.0.3 - 16-11-2022 =
Removed: Unnecessary Codes
Few minor bug fixes and improvements

= 2.0.2 - 04-10-2022 =
Fixed: UX for verification popup for non-logged in users.
Few minor bug fixes and improvements

= 2.0.1 - 04-10-2022 =
Fixed: Login Migration issue
Fixed: Workspace import issue in Gutenberg editor
Fixed: My Cloud Item Download Issue
Fixed: Gutenberg template import not showing proper message for non-verified users
Few minor bug fixes and improvements

= 2.0.0 - 03-10-2022 =
Upgraded: React 16 to 18
Added: Types for Pages and Blocks
Added: Categories for Packs
Added: Layouts or item references on the item details page for a single item pack.
Added: Related items carousel in pack details page.
Improved: State management for the Templately App with Redux and Saga
Improved: Item details page UI/UX for better navigation
Improved: Items filter on the pack details page.
Improved: Backend Call for content from Templately Server.
Improved: Filter options with Types, Categories, Tags, and Dependencies
Improved: Search based on Item Type (i.e: Pages, Blocks, Packs, etc)
Improved: Item Insertion Method for both Elementor and Gutenberg
Improved: Separate Profile page removed and introducing Profile dropdown menu to access Favorites and My Downloads easily.
Improved: Sign In and Signup page.
Improved: Error Handling
Improved: Item Dependency UI. Added tooltip to easily get the dependencies name.
Improved: Favourites functionality.

= 1.3.6 - 07-07-2022 =
Improvement: Added popup message when the MyCloud space is over for Starter plan.
Fixed: Importing issue of downloaded file from MyCloud.
Few minor bug fix and improvements

= 1.3.5 - 02-06-2022 =
Fixed: Dependency Check and Autoinstalling from Search Screen.
Fixed: Placeholder text alignment for workspace-editing in elementor screen.
Few minor bug fix and improvements

= 1.3.4 - 27-04-2022 =
Fixed: Block Editor Console Error
Fixed: PHP Disable Functions Warnings
Few minor bug fix and improvements

= 1.3.3 - 25-01-2022 =
Fixed: Category filter issue.
Few minor bug fix and improvements

= 1.3.2 - 20-01-2022 =
Added: Tri-State Dependency Actions for Filter ( include, exclude and nothing ).
Fixed: Adding Template to My Cloud is not working.
Fixed: WorkSpace Dropdown List Keeps Same if I switched to another WorkSpace.
Fixed: Filter reset button not clearing the tags properly.
Fixed: Cannot save the change in WorkSpace.
Few minor bug fix and improvements

= 1.3.1 - 21-11-2021 =
Added: Packs and pages support for Gutenberg.
Few minor bug fix and improvements

= 1.3.0 - 04-07-2021 =
Added: New Profile page with My Favourites & Downloaded Item List
Fixed: Template Count issues for Gutenberg platform
Fixed: Preloader not showing properly when items are  inserted
Fixed: Dependency Selection Issue
Few minor bug fix and improvements

= 1.2.3 - 25-05-2021 =
Fixed: My Cloud items not syncing after switching to a different account
Fixed: Workspace not updating in real-time
Fixed: Auto Installations of dependency plugins not working properly
Few minor bug fix and improvements

= 1.2.2 - 20-04-2021 =
Fixed: JS Uncaught ERROR issue.
Few minor bug fix and improvements

= 1.2.1 - 17-02-2021 =
Fixed: PHP 5.6 compatibility issue.

= 1.2.0 - 16-02-2021 =

Added: Download Button for Cloud Item, WorkSpace Item
Added: Grid View for Cloud Items, WorkSpace Items.
Added: Copy for Shared WorkSpace Item.
Added: Messages for Clouds for Unverified Users.
Added: Some UI Changes
Fixed: PreLoader on insertion
Fixed: Auto logged out for Unverified Users.
Fixed: Tags Filter Issue
Fixed: My WorkSpace dropdown css issue.
Fixed: Lots of UI Issues.

= 1.1.8 - 17-01-2021 =

Tweaked: Minor changes in the Dashboard UI
Fixed: Missing insert icon for all blocks
Few minor bug fix and improvements

= 1.1.7 - 04-01-2021 =

Added: Pretty URL in the Templately Dashboard for all items
Fixed: Search option for Workspace not working properly
Few minor bug fix and improvements

= 1.1.6 - 20-12-2020 =

Fixed: Search bar not working properly inside Elementor Editor
Fixed: Item Previews not working properly
Fixed: Items insertion failure from Preview page
Few minor bug fix and improvements

= 1.1.5 - 09-12-2020 =

Added: Global Login option
Added: Filter Templates by tags
Fixed: Safari browser incompatibility
Fixed: Scrolling not working inside Elementor Editor
Few minor bug fix and improvements

= 1.1.4 - 26-11-2020 =

- Fixed: Safari Issue
- Added: Tags Filter
- Added: Tags Filter
- Few minor bug fix and improvements

= 1.1.4 - 26-11-2020 =

- Improved: Upgraded to React 17.*
- Revamped: Code Structure for better performance
- Fixed: WordPress Dashboard not scrolling from Templately page
- Fixed: Responsive issues on different devices
- Few minor bug fix and improvements

= 1.1.3 - 27-10-2020 =

- Fixed: No Items Found error inside Workspace
- Fixed: Template Insertion placement issue inside Elementor Editor
- Fixed: Workspace not showing initially inside WordPress Dashboard
- Added: Pagination option for Templates Overview inside WordPress Dashboard
- Improved: Overall UI/UX with Revamped Designs
- Few minor bug fix & improvements

= 1.1.2 - 06-08-2020 =

- Fixed: Push to Cloud from Gutenberg screen.
- Fixed: Auto refresh in MyClouds Page after redirect from SaveTemplates screen.
- Enhanced: Changed Push button to Save Post( post_type ) in Templately in Gutenberg Screen.

= 1.1.1 - 17-07-2020 =

- Added: Dependency Plugin filter
- Fixed: PreviewHeader Insert Issue
- Lots of minor bugfix and improvements

= 1.1.0 - 16-06-2020 =

- Added: Auto install option for dependency plugins
- Enhancement : Login/Sign Up Process
- Fixed : Cloud Search Pagination issue
- Lots of minor bugfix and improvements


= 1.0.2 - 21-05-2020 =

- Added: My Cloud Search
- Fixed: Lodash & Underscore JS conflict
- Fixed: Classic Editor conflict

= 1.0.1 - 14-05-2020 =

* Fixed : Pro template insertion issue
* Fixed : Workspace redirection after delete workspace from it's details page
* Few minor bugfix and improvement

= 1.0.0 - 14-05-2020 =

* Added : Workspace for better collaboration
* Added : Platform switcher (Elementor & Gutenberg)
* Lots of minor bugfix and improvement

= 0.4.0 - 16-04-2020 =

* Added : Search Option
* Added : Dependency Icons
* Fixed : Gutenberg insert from preview
* Lots of minor bugfix and improvement

= 0.3.0 - 30-03-2020 =

* Added : Gutenberg Blocks
* Improvement : Page, Block, Packs are request optimized
* Lots of minor bugfix and improvement

= 0.2.0 - 19-03-2020 =

* Introducing : Gutenberg Cloud
* Improved : Elementor push to cloud functionality
* Few minor bugfix and improvement

= 0.1.3 - 15-03-2020 =

* Fixed : Conflict with Gutenberg cloud

= 0.1.2 - 15-03-2020 =

* Improved : Signup flow
* Few minor bugfix and improvement

= 0.1.0 - 11-03-2020 =

* Added : Context Menu in Elementor to directly save blocks and pages
* Lots of minor bugfix and improvement

= 0.0.1 - 02-03-2020 =

* Initial release
